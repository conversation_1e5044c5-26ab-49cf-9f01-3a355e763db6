const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    let contentArr = await mysqlInstance.getAllContent();
    for (let i = 0; i < contentArr.length; i++) {
        const id = contentArr[i].id;
        const userid = contentArr[i].userid;
        const user = await mysqlInstance.getUserInfoByUserid(userid);
        const location = user[0]['city'] == 'null' ? '北冰洋' : user[0]['city'];
        await mysqlInstance.updateContentLocation(id, location);
        await redisInstance.hashAdd('HASH:CONTENT:INFO:' + id, ['location', location]);
    }
    console.log('success');
}

start();