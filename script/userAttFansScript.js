const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
  let userArr = await mysqlInstance.getAllUserId2();
  for (let i = 0; i < userArr.length; i++) {
    const userid = userArr[i].id;
    const fanskey = 'SET:USER:FANS:' + userid;
    const attentionkey = 'SET:USER:ATTENTION:' + userid;
    await redisInstance.delKey(fanskey);
    await redisInstance.delKey(attentionkey);
  }
  console.log('success');
}

start();
