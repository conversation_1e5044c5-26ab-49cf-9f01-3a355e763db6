const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;

async function start() {
  let from = 0;
  let size = 100;
  do {
    let userArr = await mysqlInstance.getAllUserIdBySignature(from, size);
    from++;
    size = userArr.length;
    for (let i = 0; i < userArr.length; i++) {
      const userid = userArr[i].id;
      // await redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['signature', userArr[i].signature]);
      const data = [
        'mobile',
        userArr[i]['mobile'],
        'ssid',
        userArr[i].ssid,
        'userid',
        userArr[i].id,
        'nickname',
        userArr[i].nickname,
        'signature',
        userArr[i].signature,
        'birth',
        userArr[i].birth,
        'gender',
        userArr[i].gender,
        'hertz',
        userArr[i].hertz,
        'rank',
        userArr[i].rank,
        'type',
        userArr[i].type,
        'city',
        userArr[i].city || '',
        'province',
        userArr[i].province || '',
        'country',
        userArr[i].country || '',
        'userSig',
        userArr[i].usersig,
      ];
      const rs = await redisInstance.hashAdd('HASH:USER:INFO:' + userid, data);
      console.log(rs, data);
    }
  } while (size === 100);

  console.log('success');
}

start();
