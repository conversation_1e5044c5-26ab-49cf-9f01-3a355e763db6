const mysqlInstance = require('../models/mysql').mysqlInstance;
const co = require('co');

const a = '蒸汽波、后摇、funk、民谣、爵士、纪录片、小众电影、影展、画展、日剧、美剧'.split('、')
const aa = '镜头记录者、太空诗人、酒后表演艺术家、野生vlogger、杂食听歌、音乐发烧病友、演出现场出没人员、爱写日记、文字贪婪食客、观影爱好者'.split('、')

const b = '撸铁、潜水、极限运动、改装文化、街头文化、Hiphop、电子产品、摄影、金属硬核、ROCK、EDM、朋克、trap、跑酷、赛车、滑板'.split('、')
const bb = '蹦迪爱好者、每日穿搭、化妆必须自拍、黑暗中的dancer、玩点儿乐器、浴室歌手、深夜街头游荡者'.split('、')

const c = '古风、Cosplay、手办、漫展、汉服、switch、PS4、steam、LOL、吃鸡、日漫、日番'.split('、')
const cc = '日系女孩、日系男孩'.split('、')

const d = [];
const dd = '现充玩家、靠嘴吃饭、轻社交达人、话痨患者、好奇一切事物、梦想当个行路者、情绪稳定、散步爱好者'.split('、')

const e = []
const ee = '丧、肥宅快乐、不爱说话、社恐患者、网络交流也认真、双重人格、金牌倾听者、树洞扮演者、擅长发呆'.split('、')

const sqlArr = ['attributes_sensibility', 'attributes_sensory', 'attributes_selement', 'attributes_extravert', 'attributes_introversion']
const paoArr = [a, b, c, d, e];
const tagArr = [aa, bb, cc, dd, ee];
async function scriptFun() {
    for (let i = 0; i < sqlArr.length; i++) {
        const tableName = sqlArr[i];
        for (let j = 0; j < paoArr[i].length; j++) {
            await mysqlInstance.insertPao(tableName, paoArr[i][j]);
        }

        for (let z = 0; z < paoArr[i].length; z++) {
            await mysqlInstance.insertTag(tableName, tagArr[i][z]);
        }
    }
}

scriptFun();

