const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;

async function start() {
    const contentArr = await mysqlInstance.getAllContent();
    for (let i = 0; i < contentArr.length; i++) {
        const contentid = contentArr[i].id;
        const likeUserRs = await mysqlInstance.getContentLike(contentid);
        let likeUserArr = new Set();
        for (let j = 0; j < likeUserRs.length; j++) {
            likeUserArr.add(`${likeUserRs[j]['userid']}`);
        }
        likeUserArr = Array.from(likeUserArr);
        const likeUserArr_ = likeUserArr.join(',');
        const rs = await redisInstance.hashAdd('HASH:CONTENT:INFO:' + contentid, ['likeUserArr', likeUserArr_, 'likeNum', likeUserArr.length]);
        if (rs && rs['type'] === 'error') {
            console.log(`HASH:CONTENT:INFO::>>${rs['msg']},contentid>>${contentid}`)
        } else {
            console.log(`${contentid}:::::::OK`)
        }   }
}

start();