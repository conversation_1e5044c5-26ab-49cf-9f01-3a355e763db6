const mysqlInstance = require('../models/mysql').mysqlInstance;
const imService = require('../services/IM_service').IMInstance;

async function start() {
  let from = 0;
  let size = 100;

  do {
    const userArr = await mysqlInstance.getAllUser(from, size);
    for (let i = 0; i < userArr.length; i++) {
      const userid = userArr[i].id;
      const rank = userArr[i].rank;
      const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
      await imService.sendBulk(msg, [`${userid}`]);
    }
    size = userArr.length;
    from += size;
  } while (size === 100);
}

start();
