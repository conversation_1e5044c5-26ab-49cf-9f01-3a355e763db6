const str = `曾几何时，我们在这里
聆听过那些孤独又真挚的声音，
分享过不被喧嚣淹没的情感与共鸣。
2022年1月1日，我们暂时沉入深海；
而今天，我们重新浮上海面，
只为继续连接那些遥远的相似性。
欢迎回来，愿你仍相信，
总有一个频率，在等你回应。
52Hz——连接那些遥远的相似性。`;
const set = [
  {
    content: '2022年1月1日',
    color: '#8D99AE',
  },
  {
    content: '连接那些遥远的相似性',
    color: '#8338EC',
  },
  {
    content: '52Hz',
    color: '#3A86FF',
  },
];
function setttt(str, set) {
  const result = [];

  for (const item of set) {
    const content = item.content;
    let index = str.indexOf(content);

    while (index !== -1) {
      result.push({
        content: content,
        color: item.color,
        start: index,
        end: index + content.length,
      });

      // 继续查找下一个匹配位置
      index = str.indexOf(content, index + 1);
    }
  }

  // 按照 start 位置排序，确保返回结果按文本顺序排列
  return result.sort((a, b) => a.start - b.start);
}

// 测试
const result1 = setttt(str, set);
console.log(JSON.stringify(result1));

// // function verifyResult(str, set, result) {
// //   const errors = [];

// //   // 1. 基础检查
// //   if (!Array.isArray(result)) {
// //     errors.push('结果不是数组');
// //     return errors;
// //   }

// //   // 2. 检查是否找到了所有设置中的内容
// //   for (const item of set) {
// //     const matches = result.filter((r) => r.content === item.content);
// //     const actualMatches = str.split(item.content).length - 1;

// //     if (matches.length !== actualMatches) {
// //       errors.push(`内容 "${item.content}" 应该匹配 ${actualMatches} 次，但找到 ${matches.length} 次`);
// //     }

// //     // 检查颜色是否正确
// //     for (const match of matches) {
// //       if (match.color !== item.color) {
// //         errors.push(`内容 "${item.content}" 的颜色应该是 ${item.color}，但得到 ${match.color}`);
// //       }
// //     }
// //   }

// //   // 3. 验证每个结果的位置是否正确
// //   for (const item of result) {
// //     const substring = str.substring(item.start, item.end);
// //     if (substring !== item.content) {
// //       errors.push(`位置错误：在位置 ${item.start}-${item.end} 找到 "${substring}"，应该是 "${item.content}"`);
// //     }
// //   }

// //   // 4. 检查位置是否有重叠
// //   for (let i = 0; i < result.length - 1; i++) {
// //     if (result[i].end > result[i + 1].start) {
// //       errors.push(`位置重叠：${JSON.stringify(result[i])} 和 ${JSON.stringify(result[i + 1])}`);
// //     }
// //   }

// //   // 5. 检查是否按位置排序
// //   for (let i = 0; i < result.length - 1; i++) {
// //     if (result[i].start > result[i + 1].start) {
// //       errors.push('结果未按位置排序');
// //       break;
// //     }
// //   }

// //   // 6. 可视化验证（在控制台中显示标记位置）
// //   console.log('可视化验证:');
// //   let visualText = str;
// //   const markers = [];
// //   result.forEach((item, index) => {
// //     markers.push({ pos: item.start, type: 'start', index });
// //     markers.push({ pos: item.end, type: 'end', index });
// //   });

// //   markers.sort((a, b) => b.pos - a.pos); // 从后往前处理，避免位置变化

// //   for (const marker of markers) {
// //     const symbol = marker.type === 'start' ? `[${marker.index}` : `${marker.index}]`;
// //     visualText = visualText.slice(0, marker.pos) + symbol + visualText.slice(marker.pos);
// //   }
// //   console.log(visualText);

// //   return errors;
// // }

// // // 测试
// // const result = setttt(str, set);
// // const errors = verifyResult(str, set, result);

// // if (errors.length === 0) {
// //   console.log('验证通过！');
// //   console.log('结果：', result);
// // } else {
// //   console.log('验证失败！发现以下问题：');
// //   errors.forEach((error, index) => {
// //     console.log(`${index + 1}. ${error}`);
// //   });
// // }

// // // 额外测试用例
// // const testCases = [
// //   {
// //     str: 'aabbcc',
// //     set: [
// //       { content: 'aa', color: '#FF0000' },
// //       { content: 'bb', color: '#00FF00' },
// //     ],
// //   },
// //   {
// //     str: 'overlapping',
// //     set: [
// //       { content: 'over', color: '#FF0000' },
// //       { content: 'lap', color: '#00FF00' },
// //     ],
// //   },
// //   {
// //     str: 'repeated repeated',
// //     set: [{ content: 'repeated', color: '#FF0000' }],
// //   },
// // ];

// console.log('\n运行额外测试用例：');
// testCases.forEach((testCase, index) => {
//   console.log(`\n测试用例 ${index + 1}:`);
//   const testResult = setttt(testCase.str, testCase.set);
//   const testErrors = verifyResult(testCase.str, testCase.set, testResult);

//   if (testErrors.length === 0) {
//     console.log('通过');
//   } else {
//     console.log('失败：');
//     testErrors.forEach((error) => console.log('- ' + error));
//   }
// });
