const co = require('co');
const mysqlInstance = require('../models/mysql').mysqlInstance;

// 生成随机整数函数，范围是 [min, max]
function getRandomInt(min, max) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 有意义的消息数组
const meaningfulMessages = [
  '你好，今天的天气真好！',
  '愿你前程似锦。',
  '愿你一切顺利。',
  '愿你笑对人生。',
  '愿你拥有属于自己的幸福。',
  'Keep going, you are amazing!',
  'Stay positive and happy every day!',
  'You are stronger than you think.',
  'Believe in yourself and all that you are.',
  'Every day is a new beginning.',
  'Wishing you a wonderful day!',
  'May your dreams come true.',
  'Good luck and best wishes!',
  'Never give up on your dreams.',
  'Happiness is always around you.',
  'Be kind, be brave, be you.',
  'Enjoy every moment of your life.',
  'You light up the world.',
  'Take care and stay healthy.',
  'Cherish every day and every person around you.',
  'The best is yet to come.',
  'Thank you for being here.',
  'Your smile is the best sunshine.',
  'May you always find reasons to smile.',
  '愿你拥有诗和远方。',
  '愿你被温柔以待，心中有光。',
  '愿你所求皆如愿，所行化坦途。',
  '愿你历尽千帆，归来仍是少年。',
  '愿你一生努力，一生被爱。',
  '愿你成为自己的太阳，无需凭借谁的光。',
  '愿你眼里有光，心中有爱。',
  '愿你所遇皆良善，所行皆坦途。',
  '愿你不负时光，不负自己。',
  '愿你温柔以待自己。',
  '愿你所爱之人平安喜乐。',
  '愿你前路光明，未来可期。',
  '人生就像一场旅行，不必在乎目的地，在乎的是沿途的风景以及看风景的心情。愿你在每一个平凡的日子里都能发现生活的美好，收获属于自己的幸福和感动。',
  '有时候我们会觉得生活很难，但请相信，所有的努力和坚持都会在未来的某一天给你最温柔的回报。愿你不负时光，不负自己，勇敢追梦，终有一天会遇见更好的自己。',
  '在这个快节奏的时代，别忘了停下来看看身边的风景，和朋友聊聊天，给家人一个拥抱。愿你在忙碌中也能感受到温暖和爱，愿你的人生充满阳光和希望。',
  '每个人的生活都不容易，但请相信，黑暗过后总会迎来光明。愿你在低谷时不失信心，在顺境时不忘初心，愿你的人生道路越走越宽广。',
  '愿你在未来的日子里，勇敢面对生活的挑战，珍惜每一次成长的机会。无论遇到什么困难，都能保持一颗积极乐观的心，相信美好终会到来。',
  '有些路很远，走下去会很累，但不走会后悔。愿你在追梦的路上不畏艰难，勇敢前行，终有一天会到达理想的彼岸。',
  '生活有时会让我们感到疲惫，但请记得，身边总有人默默关心你、支持你。愿你在孤单时不感到孤独，在迷茫时不失去方向。',
  '愿你在每一个平凡的日子里都能发现生活的美好，哪怕只是一次温暖的问候，一句简单的鼓励，也能让你感受到世界的善意。',
  '成长的路上难免会有风雨，但请相信，每一次经历都是宝贵的财富。愿你在风雨中学会坚强，在阳光下学会感恩。',
  '愿你在未来的日子里，心中有梦，眼里有光，脚下有路。无论遇到什么，都能勇敢面对，微笑前行。',
  'Sometimes life is tough, but remember you are tougher. Every challenge is an opportunity to grow, and every setback is a setup for a comeback. Keep believing in yourself and keep moving forward.',
  'No matter how hard today is, tomorrow is a new day full of hope and possibilities. Take a deep breath, smile, and keep going. You are doing better than you think.',
  'The world is full of beautiful things, just like your smile and your kindness. May you always find reasons to be happy and people who make your life brighter.',
  '每当夜深人静的时候，回想起一天的点点滴滴，或许有遗憾，有感动，有欢笑，也有泪水。愿你珍惜每一个当下，感恩每一次遇见。',
  '愿你在未来的日子里，拥有一颗温柔善良的心，勇敢追逐自己的梦想，不被外界的纷扰所影响，做最真实的自己。',
  '生活的意义不在于拥有多少，而在于用心感受每一刻的美好。愿你在平凡的日子里，活出属于自己的精彩。',
  '愿你在每一个清晨醒来时，都能带着希望和微笑迎接新的一天。无论昨天发生了什么，今天都是新的开始。',
  '愿你在未来的路上，遇见温暖的人，经历美好的事，收获属于自己的幸福和快乐。',
  '愿你在追梦的路上，不畏艰难，勇敢前行。即使前方有风雨，也要相信，彩虹就在不远处等你。',
  '愿你在每一个平凡的日子里，都能发现生活的美好，收获属于自己的幸福和感动。',
  '愿你在未来的日子里，心中有梦，眼里有光，脚下有路。无论遇到什么，都能勇敢面对，微笑前行。',
  '愿你在每一个平凡的日子里都能发现生活的美好，哪怕只是一次温暖的问候，一句简单的鼓励，也能让你感受到世界的善意。',
  '愿你在未来的日子里，拥有一颗温柔善良的心，勇敢追逐自己的梦想，不被外界的纷扰所影响，做最真实的自己。',
  '愿你在追梦的路上，不畏艰难，勇敢前行。即使前方有风雨，也要相信，彩虹就在不远处等你。',
];

function task() {
  return co(function* () {
    let lytimestamp = 1719763200;
    let sdtimestamp = 1751299200;

    for (let i = 0; i < 300; i++) {
      const userid = getRandomInt(100000, 120000);

      const msg = meaningfulMessages[getRandomInt(0, meaningfulMessages.length - 1)];

      const lyincrement = getRandomInt(50000, 100000);
      lytimestamp += lyincrement;

      const sdincrement = getRandomInt(100, 1000);
      sdtimestamp += sdincrement;

      yield mysqlInstance.addShudong(userid, msg, sdtimestamp);
      yield mysqlInstance.addLiuyan(userid, 7, msg, lytimestamp);

      // 输出进度日志
      if (i % 10 === 0) {
        console.log(`已添加 ${i} 条记录`);
      }
    }
    console.log('全部添加完成！');
  });
}

task();
