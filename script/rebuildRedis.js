const co = require('co');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');

class RedisRebuild {
  constructor() {
    this.stats = {
      total: 0,
      success: 0,
      failed: 0,
      errors: [],
    };
    this.pageSize = 100; // 每页处理数量
  }

  async rebuild() {
    try {
      console.log('开始重建 Redis 数据...');

      // 1. 用户基础信息
      console.log('开始重建用户基础信息...');
      await this.rebuildUserInfo();

      // 2. 内容信息
      console.log('开始重建内容信息...');
      await this.rebuildContentInfo();

      // 3. 用户频率数据
      console.log('开始重建用户频率数据...');
      await this.rebuildUserHertz();

      // 4. 用户关系数据
      console.log('开始重建用户关系数据...');
      await this.rebuildUserRelations();

      // 5. 用户登录状态
      console.log('开始重建用户登录状态...');
      await this.rebuildLoginStatus();

      // 6. 用户推送计数
      console.log('开始重建用户推送计数...');
      await this.rebuildPushCount();

      // 7. 用户相似推荐
      console.log('开始重建用户相似推荐...');
      await this.rebuildUserSimilar();

      // 8. 用户评论
      console.log('开始重建用户评论...');
      await this.rebuildComments();

      // 9. 岛评论
      console.log('开始重建岛评论...');
      await this.rebuildIslandComments();

      // 10. 用户新内容
      console.log('开始重建用户新内容...');
      await this.rebuildNewContent();

      console.log('Redis 重建完成:', this.stats);
    } catch (err) {
      console.error('重建失败:', err);
    }
  }

  async rebuildUserInfo() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT * FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;
          const userInfo = {
            userid: user.id,
            nickname: user.nickname || '',
            avatar: user.avatar || '',
            signature: user.signature || '',
            birth: user.birth || '',
            gender: user.gender || 1,
            hertz: user.hertz || '52.00',
            rank: user.rank || 0,
            city: user.city || '',
            province: user.province || '',
            country: user.country || '',
            userSig: user.userSig || '',
          };

          const hashData = Object.entries(userInfo)
            .filter(([_, value]) => value !== undefined)
            .flat();

          await redisInstance.hashAdd(`HASH:USER:INFO:${user.id}`, hashData);
          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'user',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理用户数据: ${offset}`);
    }
  }

  async rebuildContentInfo() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT * FROM content WHERE status = 1 ORDER BY id LIMIT ${this.pageSize} OFFSET ${offset}`;
      const contents = await mysqlInstance.query(sql);

      if (contents.length < this.pageSize) {
        hasMore = false;
      }

      for (const content of contents) {
        try {
          this.stats.total++;

          // 分页获取点赞用户
          const likeUsers = [];
          let likeOffset = 0;
          let hasMoreLikes = true;

          while (hasMoreLikes) {
            const likeSql = `SELECT userid FROM content_like WHERE content_id = ? AND status = 1 LIMIT ${this.pageSize} OFFSET ${likeOffset}`;
            const likes = await mysqlInstance.query(likeSql, [content.id]);

            if (likes.length < this.pageSize) {
              hasMoreLikes = false;
            }

            likeUsers.push(...likes);
            likeOffset += this.pageSize;
          }

          const likeUserArr = [...new Set(likeUsers.map((u) => u.userid))];

          const contentInfo = {
            contentid: content.id,
            userid: content.user_id,
            content: content.content || '',
            commentNum: content.comment_num || 0,
            likeNum: likeUserArr.length || 0,
            likeUserArr: likeUserArr.join(','),
            createTime: content.create_time,
          };

          const hashData = Object.entries(contentInfo)
            .filter(([_, value]) => value !== undefined)
            .flat();

          await redisInstance.hashAdd(`HASH:CONTENT:INFO:${content.id}`, hashData);
          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'content',
            id: content.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理内容数据: ${offset}`);
    }
  }

  async rebuildUserHertz() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;
          const hertzInfo = {
            contentnum: '0',
            likenum: '0',
          };

          const hashData = Object.entries(hertzInfo)
            .filter(([_, value]) => value !== undefined)
            .flat();

          await redisInstance.hashAdd(`HASH:USER:HERTZ:TIME:${user.id}`, hashData);
          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'hertz',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理用户频率数据: ${offset}`);
    }
  }

  async rebuildUserRelations() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;

          // 分页获取粉丝
          let fansOffset = 0;
          let hasMoreFans = true;
          while (hasMoreFans) {
            const fansSql = `SELECT userid1 FROM user_relation WHERE userid2 = ? AND status = 1 ORDER BY create_time LIMIT ${this.pageSize} OFFSET ${fansOffset}`;
            const fans = await mysqlInstance.query(fansSql, [user.id]);

            if (fans.length < this.pageSize) {
              hasMoreFans = false;
            }

            for (let i = 0; i < fans.length; i++) {
              await redisInstance.zsetAdd(`ZSET:USER:FANS:${user.id}`, fansOffset + i + 1, fans[i].userid1);
            }

            fansOffset += this.pageSize;
          }

          // 分页获取关注
          let attOffset = 0;
          let hasMoreAtt = true;
          while (hasMoreAtt) {
            const attSql = `SELECT userid2 FROM user_relation WHERE userid1 = ? AND status = 1 ORDER BY create_time LIMIT ${this.pageSize} OFFSET ${attOffset}`;
            const attentions = await mysqlInstance.query(attSql, [user.id]);

            if (attentions.length < this.pageSize) {
              hasMoreAtt = false;
            }

            for (let i = 0; i < attentions.length; i++) {
              await redisInstance.zsetAdd(`ZSET:USER:ATTENTION:${user.id}`, attOffset + i + 1, attentions[i].userid2);
            }

            attOffset += this.pageSize;
          }

          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'relation',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理用户关系数据: ${offset}`);
    }
  }

  async rebuildLoginStatus() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id, online FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;
          await redisInstance.onlySet(`SET:LOGIN:STATUS:${user.id}`, user.online || '0');
          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'login',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理登录状态数据: ${offset}`);
    }
  }

  async rebuildPushCount() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;
          await redisInstance.onlySet(`STR:USER:PUSH:COUNT:${user.id}`, '0');
          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'push',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理推送计数数据: ${offset}`);
    }
  }

  async rebuildUserSimilar() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;
          await redisInstance.delKey(`STR:USER:SIMILAR:${user.id}`);
          await redisInstance.delKey(`SET:USER:SIMILAR:${user.id}`);
          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'similar',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理用户相似推荐数据: ${offset}`);
    }
  }

  async rebuildComments() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id FROM user WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const users = await mysqlInstance.query(sql);

      if (users.length < this.pageSize) {
        hasMore = false;
      }

      for (const user of users) {
        try {
          this.stats.total++;

          const commentSql = `
            SELECT * FROM comment 
            WHERE user_id = ? AND status = 1 
            ORDER BY create_time DESC LIMIT 30
          `;
          const comments = await mysqlInstance.query(commentSql, [user.id]);

          for (const comment of comments.reverse()) {
            await redisInstance.lpush(
              `LIST:USER:CONMENT:${user.id}`,
              JSON.stringify({
                commentid: comment.id,
                contentid: comment.content_id,
                content: comment.content,
                createTime: comment.create_time,
              }),
            );
          }

          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'comment',
            id: user.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理用户评论数据: ${offset}`);
    }
  }

  async rebuildIslandComments() {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const sql = `SELECT id FROM island_content WHERE status = 1 LIMIT ${this.pageSize} OFFSET ${offset}`;
      const discussions = await mysqlInstance.query(sql);

      if (discussions.length < this.pageSize) {
        hasMore = false;
      }

      for (const discuss of discussions) {
        try {
          this.stats.total++;

          let commentOffset = 0;
          let hasMoreComments = true;

          while (hasMoreComments) {
            const commentSql = `
              SELECT id, create_time 
              FROM island_comment 
              WHERE discuss_id = ? AND status = 1 
              ORDER BY create_time ASC 
              LIMIT ${this.pageSize} OFFSET ${commentOffset}
            `;
            const comments = await mysqlInstance.query(commentSql, [discuss.id]);

            if (comments.length < this.pageSize) {
              hasMoreComments = false;
            }

            for (const comment of comments) {
              await redisInstance.zsetAdd(`ZSET:ISLAND:COMMENT:${discuss.id}`, comment.create_time, comment.id);
            }

            commentOffset += this.pageSize;
          }

          this.stats.success++;
        } catch (err) {
          this.stats.failed++;
          this.stats.errors.push({
            type: 'island_comment',
            id: discuss.id,
            error: err.message,
          });
        }
      }

      offset += this.pageSize;
      console.log(`处理岛评论数据: ${offset}`);
    }
  }

  async rebuildNewContent() {
    const sql = `
      SELECT user_id 
      FROM content 
      WHERE status = 1 
      ORDER BY create_time DESC 
      LIMIT 500
    `;
    const contents = await mysqlInstance.query(sql);

    for (const content of contents.reverse()) {
      await redisInstance.lpush('LIST:CONTENT:NEW', content.user_id);
    }

    console.log('处理新内容数据完成');
  }
}

// 执行重建
const rebuilder = new RedisRebuild();
rebuilder
  .rebuild()
  .then(() => {
    console.log('Redis 重建完成');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Redis 重建失败:', err);
    process.exit(1);
  });
