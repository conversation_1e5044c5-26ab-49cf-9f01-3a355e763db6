const co = require('co');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');
function task() {
  return co(function* () {
    yield mysqlInstance.delBadge();

    const likeRs = yield mysqlInstance.getLikeBadge();
    for (let i = 0; i < likeRs.length; i++) {
      const userid = likeRs[i].userid;
      yield mysqlInstance.addBadge(
        userid,
        2,
        '点赞王认证',
        '2020年发出点赞次数TOP52',
        // 'http://doc.fiftytwohz.com/%E7%82%B9%E8%B5%9E%E7%8E%8B.png',
        DefaultConfig.wailian.DOC_DOMAIN + 'like.png',
      ); //userid, type, type_name, introduction
    }

    const CommentRs = yield mysqlInstance.getCommentBadge();
    for (let i = 0; i < CommentRs.length; i++) {
      const userid = CommentRs[i].userid;
      yield mysqlInstance.addBadge(
        userid,
        3,
        '冒泡王认证',
        '2020年发动态条数TOP52',
        //   'http://doc.fiftytwohz.com/%E5%86%92%E6%B3%A1%E7%8E%8B.png',
        DefaultConfig.wailian.DOC_DOMAIN + 'bubble.png',
      ); //userid, type, type_name, introduction
    }
    const redisRS = yield redisInstance.hashGet('SOCKET:USER:NAMESPACE:ALL');
    let arr = [];
    for (const key in redisRS) {
      const data = JSON.parse(redisRS[key]);
      const param = {
        userid: data['userid'],
        weight: data['weight'],
      };
      arr.push(param);
    }

    arr.sort(compare('weight'));
    arr = arr.slice(0, 52);

    for (let i = 0; i < arr.length; i++) {
      const userid = arr[i].userid;
      yield mysqlInstance.addBadge(
        userid,
        6,
        '大胃王',
        '2020年体重榜TOP52',
        // 'http://doc.fiftytwohz.com/%E5%A4%A7%E8%83%83%E7%8E%8B.png',
        DefaultConfig.wailian.DOC_DOMAIN + 'big_mouth.png',
      ); //userid, type, type_name, introduction
    }
  });
}

function compare(property) {
  return function (a, b) {
    var value1 = a[property];
    var value2 = b[property];
    return value2 - value1;
  };
}

task();
