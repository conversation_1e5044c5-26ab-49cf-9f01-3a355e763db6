const mysqlInstance = require('../models/mysql').mysqlInstance;
// 8262
const task = async () => {
    const receiptRs = await mysqlInstance.getReceipt()
    for (let i = 0; i < receiptRs.length; i++) {
        const receipt = receiptRs[i].receipt
        const id = receiptRs[i].id
        const receipt_hash = getHashCode(receipt)
        console.log(receipt_hash)
        await mysqlInstance.addReceiptHash(id, receipt_hash)
    }
}

function getHashCode(str, caseSensitive) {
    if (!caseSensitive) {
        str = str.toLowerCase();
    }
    var hash = 1315423911, i, ch;
    for (i = str.length - 1; i >= 0; i--) {
        ch = str.charCodeAt(i);
        hash ^= ((hash << 5) + ch + (hash >> 2));
    }

    return (hash & 0x7FFFFFFF);
}

task()
