const co = require("co");

const URL = 'http://172.16.241.103:6763/r/ocean/?userId=11&page=2';
const argv = process.argv
const request = require('request');

function createRequest(userid, page) {
    // console.log(`>>>>>>>>>>>${JSON.stringify(data)}`)
    return new Promise((resolve, reject) => {
        const options = {
            method: 'GET',
            url: 'http://172.16.241.103:6763/r/ocean/',
            qs: { userId: userid, page: page },
            headers:
            {
                'cache-control': 'no-cache'
            }
        };
        request(options, function (error, response, body) {
            console.log(options);
            console.log(`<>>>>>>${JSON.stringify(body)}`)
            if (error) {
                resolve({
                    result: false
                })
            }
            resolve({
                result: true,
                data: body,
            })
        });
    })
}

function task(userId, page) {
    return co(function* () {
        const rs = yield createRequest(userId, page)
        console.log(JSON.stringify(rs));
    })
}

task(parseInt(argv[2]), parseInt(argv[3]))