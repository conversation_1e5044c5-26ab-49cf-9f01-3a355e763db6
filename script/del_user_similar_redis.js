const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    let userArr = await mysqlInstance.getAllUserId2();
    for (let i = 0; i < userArr.length; i++) {
        const userid = userArr[i].id;
        // //获取粉丝
        // const fansArr = await mysqlInstance.getFansFun(userid);
        // //获取关注
        // const attArr = await mysqlInstance.getAttenFun(userid);
        // for (let i = 0; i < fansArr.length; i++) {
        //     const key = 'ZSET:USER:FANS:' + userid;
        //     const fansid = fansArr[i].userid1;
        //     await redisInstance.zsetAdd(key, i + 1, fansid)
        // }
        // for (let i = 0; i < attArr.length; i++) {
        //     const key = 'ZSET:USER:ATTENTION:' + userid;
        //     const attentionid = attArr[i].userid2;
        //     await redisInstance.zsetAdd(key, i + 1, attentionid)
        // }
        const STRkey = 'STR:USER:SIMILAR:' + userid;
        const SETkey = 'SET:USER:SIMILAR:' + userid;
        await redisInstance.delKey(STRkey);
        await redisInstance.delKey(SETkey);
    }
    console.log('success');
}

start();