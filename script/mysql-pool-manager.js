#!/usr/bin/env node
'use strict';

const { getPoolStatus, resetPoolStats, poolManager } = require('../models/mysql');
const { DefaultConfig } = require('../config/default');

/**
 * MySQL连接池管理脚本
 * 用法:
 *   node script/mysql-pool-manager.js status    # 查看状态
 *   node script/mysql-pool-manager.js reset     # 重置统计
 *   node script/mysql-pool-manager.js monitor   # 持续监控
 *   node script/mysql-pool-manager.js test      # 运行测试
 */

class MySQLPoolManager {
  constructor() {
    this.monitorInterval = null;
  }

  /**
   * 显示连接池状态
   */
  showStatus() {
    try {
      const status = getPoolStatus();
      
      console.log('=== MySQL连接池状态 ===');
      console.log(`时间: ${new Date().toISOString()}`);
      console.log('');
      
      // 主库状态
      console.log('📊 主库连接池:');
      console.log(`  主机: ${status.pools.master.config.host}:${status.pools.master.config.port || 3306}`);
      console.log(`  数据库: ${status.pools.master.config.database}`);
      console.log(`  连接数: ${status.pools.master.connected}/${status.pools.master.config.connectionLimit}`);
      console.log(`  空闲连接: ${status.pools.master.free}`);
      console.log(`  使用率: ${(((status.pools.master.connected - status.pools.master.free) / status.pools.master.config.connectionLimit) * 100).toFixed(1)}%`);
      console.log('');
      
      // 从库状态
      if (status.pools.slaves.length > 0) {
        console.log('📊 从库连接池:');
        status.pools.slaves.forEach((slave, index) => {
          console.log(`  从库${index}:`);
          console.log(`    主机: ${slave.config.host}:${slave.config.port || 3306}`);
          console.log(`    数据库: ${slave.config.database}`);
          console.log(`    连接数: ${slave.connected}/${slave.config.connectionLimit}`);
          console.log(`    空闲连接: ${slave.free}`);
          console.log(`    使用率: ${(((slave.connected - slave.free) / slave.config.connectionLimit) * 100).toFixed(1)}%`);
        });
        console.log('');
      }
      
      // 查询统计
      console.log('📈 查询统计:');
      const uptime = Date.now() - status.stats.lastResetTime;
      const qps = status.stats.totalQueries / (uptime / 1000);
      
      console.log(`  运行时间: ${Math.round(uptime / 1000)}秒`);
      console.log(`  总查询数: ${status.stats.totalQueries}`);
      console.log(`  QPS: ${qps.toFixed(2)}`);
      console.log(`  主库查询: ${status.stats.masterQueries} (${status.stats.totalQueries > 0 ? ((status.stats.masterQueries / status.stats.totalQueries) * 100).toFixed(1) : 0}%)`);
      console.log(`  从库查询: ${status.stats.slaveQueries} (${status.stats.totalQueries > 0 ? ((status.stats.slaveQueries / status.stats.totalQueries) * 100).toFixed(1) : 0}%)`);
      console.log(`  错误数: ${status.stats.errors}`);
      console.log(`  慢查询数: ${status.stats.slowQueries}`);
      console.log(`  连接错误数: ${status.stats.connectionErrors}`);
      console.log(`  平均响应时间: ${status.stats.avgResponseTime.toFixed(2)}ms`);
      console.log('');
      
      // 健康状态
      if (status.health) {
        console.log('💚 健康状态:');
        console.log(`  主库: ${status.health.master.healthy ? '✅ 健康' : '❌ 异常'}`);
        console.log(`  主库错误计数: ${status.health.master.errorCount}`);
        console.log(`  主库最后检查: ${status.health.master.lastCheck}`);
        
        if (status.health.slaves.length > 0) {
          console.log(`  从库健康数: ${status.health.totalHealthySlaves}/${status.health.totalSlaves}`);
          status.health.slaves.forEach((slave, index) => {
            console.log(`    从库${index}: ${slave.healthy ? '✅ 健康' : '❌ 异常'} (错误: ${slave.errorCount})`);
          });
        }
        console.log('');
      }
      
      // 监控配置
      console.log('⚙️ 监控配置:');
      console.log(`  监控状态: ${status.monitoring.enabled ? '✅ 已启用' : '❌ 已禁用'}`);
      console.log(`  健康检查间隔: ${status.monitoring.healthCheckInterval}ms`);
      console.log(`  慢查询阈值: ${status.monitoring.slowQueryThreshold}ms`);
      
    } catch (error) {
      console.error('获取状态失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    try {
      resetPoolStats();
      console.log('✅ 统计信息已重置');
    } catch (error) {
      console.error('重置统计失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 持续监控模式
   */
  startMonitoring(interval = 10000) {
    console.log(`🔄 开始持续监控 (间隔: ${interval}ms)`);
    console.log('按 Ctrl+C 停止监控');
    console.log('');
    
    // 立即显示一次状态
    this.showStatus();
    
    // 设置定时器
    this.monitorInterval = setInterval(() => {
      console.clear();
      this.showStatus();
    }, interval);
    
    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 停止监控');
      if (this.monitorInterval) {
        clearInterval(this.monitorInterval);
      }
      process.exit(0);
    });
  }

  /**
   * 运行测试
   */
  async runTest() {
    try {
      const MySQLReadWriteSplitTest = require('../test/mysql-read-write-split.test');
      const test = new MySQLReadWriteSplitTest();
      await test.runAllTests();
    } catch (error) {
      console.error('运行测试失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log('MySQL连接池管理工具');
    console.log('');
    console.log('用法:');
    console.log('  node script/mysql-pool-manager.js <command> [options]');
    console.log('');
    console.log('命令:');
    console.log('  status              显示连接池状态');
    console.log('  reset               重置统计信息');
    console.log('  monitor [interval]  持续监控 (默认间隔10秒)');
    console.log('  test                运行读写分离测试');
    console.log('  help                显示帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node script/mysql-pool-manager.js status');
    console.log('  node script/mysql-pool-manager.js monitor 5000');
    console.log('  node script/mysql-pool-manager.js test');
  }
}

// 主函数
async function main() {
  const manager = new MySQLPoolManager();
  const command = process.argv[2];
  const arg = process.argv[3];
  
  switch (command) {
    case 'status':
      manager.showStatus();
      break;
      
    case 'reset':
      manager.resetStats();
      break;
      
    case 'monitor':
      const interval = arg ? parseInt(arg) : 10000;
      if (isNaN(interval) || interval < 1000) {
        console.error('错误: 监控间隔必须是大于等于1000的数字');
        process.exit(1);
      }
      manager.startMonitoring(interval);
      break;
      
    case 'test':
      await manager.runTest();
      break;
      
    case 'help':
    case '--help':
    case '-h':
      manager.showHelp();
      break;
      
    default:
      console.error(`错误: 未知命令 '${command}'`);
      console.log('');
      manager.showHelp();
      process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('执行失败:', error);
    process.exit(1);
  });
}

module.exports = MySQLPoolManager;
