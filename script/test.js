const co = require('co');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const argv = process.argv;
const request = require("request");
const URL = 'http://172.16.241.103:10031/zoe/rpc';

function createRequest(data) {
    console.log(`>>>>>>>>>>>${JSON.stringify(data)}`)
    return new Promise((resolve, reject) => {
        const options = {
            method: 'POST',
            url: URL,
            headers:
            {
                'cache-control': 'no-cache',
                'Content-Type': 'application/json'
            },
            body:
            {
                function: 'searchByContents',
                params: { data: [{ item_id: parseInt(data['contentid']), content: data['content'] }] }
            },
            json: true
        };
        request(options, function (error, response, body) {
            if (error) {
                resolve({
                    result: false
                })
            }
            if (body.code === 200 && body.result.length && body.result[0].neighbor_id) {
                const rs = {
                    body: body.result,
                    recommen_type: data.recommen_type,
                    accordid: data['contentid'],
                }
                resolve({
                    result: true,
                    data: rs
                })
            } else {
                resolve({
                    result: false
                })
            }
        });
    })
}
const a = {
    likeArr:
        [{ contentid: 70878, content: '还是蓝', recommen_type: 1 },
        {
            contentid: 59513,
            content: '总有很多无法形容的感觉，但可以确定的就是爱意。',
            recommen_type: 1
        }],
    commentArr:
        [{
            contentid: 110588,
            content: '请成为更厉害的大人和更可爱的小孩',
            recommen_type: 2
        }],
    contentArr:
        [{
            contentid: 110588,
            content: '请成为更厉害的大人和更可爱的小孩',
            recommen_type: 3
        }],
    randomArr: [{ contentid: 110776, content: '最近', recommen_type: 4 }]
}

function task(userid) {
    return co(function* () {
        const shuruRs = a
        // console.log(`SHURURS:::${JSON.stringify(shuruRs)}`);
        const zoeArr = [];
        for (const key in shuruRs) {
            const value = shuruRs[key];
            for (let i = 0; i < value.length; i++) {
                const data = value[i];
                const ZoeRs = yield createRequest(data);
                console.log(`ZoeRs::::${JSON.stringify(ZoeRs)}`);
                if (ZoeRs.result) {
                    const zoeData = ZoeRs.data;
                    zoeArr.push(zoeData);
                }
            }
        }
    })
}
task(parseInt(argv[2]))