const co = require("co");
const request = require("request");
const URL = 'http://172.16.241.103:10031/zoe/rpc';

function createRequest(data) {
    // console.log(`>>>>>>>>>>>${JSON.stringify(data)}`)
    return new Promise((resolve, reject) => {
        const options = {
            method: 'POST',
            url: URL,
            headers:
            {
                'cache-control': 'no-cache',
                'Content-Type': 'application/json'
            },
            body:
            {
                function: 'searchByContents',
                params: { data: [{ item_id: parseInt(data['contentid']), content: data['content'] }] }
            },
            json: true
        };
        request(options, function (error, response, body) {
            if (error) {
                resolve({
                    result: false
                })
            }
            if (body.code === 200 && body.result.length && body.result[0].neighbor_id) {
                const rs = {
                    body: body.result,
                    recommen_type: data.recommen_type,
                    accordid: data['contentid'],
                }
                resolve({
                    result: true,
                    data: rs
                })
            } else {
                resolve({
                    result: false
                })
            }
        });
    })
}

function task() {
    return co(function* () {
        const data = {
            contentid: 0,
            content: '财务统计'
        }
        const ZoeRs = yield createRequest(data);
        console.log(`ZoeRs::::${JSON.stringify(ZoeRs)}`);
    })
}

task();