const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    const rs = await mysqlInstance.getAllINvitCode();
    for (let i = 0; i < rs.length; i++) {
        const code = rs[i].code;
        const key = 'SET:ALL:INVIT:CODE';
        await redisInstance.setAdd(key, code);
    }
    console.log('success')
}

start();