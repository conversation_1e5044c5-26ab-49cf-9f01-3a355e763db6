const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    let userArr = await mysqlInstance.getAllUserId2();
    for (let i = 0; i < userArr.length; i++) {
        const userid = userArr[i].id;       const key = 'SET:USER:SIMILAR:' + userid;
        const time = 1296000;
        await redisInstance.expire(key, time);
    }
    console.log('success');
}

start();