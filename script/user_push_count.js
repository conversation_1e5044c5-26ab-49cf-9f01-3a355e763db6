const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    let userArr = await mysqlInstance.getAllUserId2();
    for (let i = 0; i < userArr.length; i++) {
        const userid = userArr[i].id;
        const redisUserPushCount = await redisInstance.onlySet(`STR:USER:PUSH:COUNT:${userid}`, '0')
        console.log(`userid:${userid}::::::::::${redisUserPushCount}`);
    }
}

start();