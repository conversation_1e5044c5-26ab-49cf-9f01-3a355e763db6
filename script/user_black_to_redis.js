const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    const blackArr = await mysqlInstance.getUserBlackFun();
    for (let i = 0; i < blackArr.length; i++) {
        const userid = blackArr[i].userid;
        const black_userid = blackArr[i].black_userid;
        await redisInstance.setAdd(`SET:USER:BLACK:${userid}`, black_userid);
    }
    console.log('success');
}

start();