const co = require('co');
const { redisInstance } = require('../models/redis');
const { mysqlInstance } = require('../models/mysql');
const { DefaultConfig } = require('../config/default');

function task() {
  return co(function* () {
    const allUser = yield mysqlInstance.getAllUserId2();
    for (let i = 0; i < allUser.length; i++) {
      const userid = allUser[i].id;
      console.log(userid);
      const nickname = allUser[i].nickname;
      let avatar = allUser[i].avatar;
      const gender = allUser[i].gender;

      avatar = DefaultConfig.wailian.AVATAR_DOMAIN + avatar;
      const key1 = 'SOCKET:NAMESPACE:ALL';
      const key2 = 'SOCKET:USER:NAMESPACE:';
      const key3 = 'SOCKET:USER:NAMESPACE:ALL';
      const key4 = 'SET:ALL:SOCKET:FOOD:STATISTICS';
      const key5 = 'HASH:SOCKET:FOOD:STATISTICS';
      let path = null;
      const redisRS = yield redisInstance.hashGet(key1);
      for (const key in redisRS) {
        let value = JSON.parse(redisRS[key]);
        for (let i = 0; i < value.length; i++) {
          const userid_ = parseInt(value[i]);
          if (userid === userid_) {
            path = key;
          }
        }
      }
      if (path) {
        const redisRS = yield redisInstance.hmget(`${key2}${path}`, `${userid}`);
        if (redisRS && redisRS[0]) {
          const userInfo = JSON.parse(redisRS[0]);
          userInfo.nickname = nickname;
          userInfo.avatar = avatar;
          userInfo.gender = gender;
          yield redisInstance.hashAdd(`${key2}${path}`, [`${userid}`, JSON.stringify(userInfo)]);
        }
      }
      const redisRS_ = yield redisInstance.hmget(key3, `${userid}`);
      if (redisRS_ && redisRS_[0]) {
        const userInfo = JSON.parse(redisRS_[0]);
        userInfo.nickname = nickname;
        userInfo.avatar = avatar;
        userInfo.gender = gender;
        yield redisInstance.hashAdd(key3, [`${userid}`, JSON.stringify(userInfo)]);
      }

      let allRs = yield redisInstance.get(key4);
      allRs = JSON.parse(allRs);
      for (let i = 0; i < allRs.length; i++) {
        if (parseInt(allRs[i].userid) === userid) {
          allRs[i].avatar = avatar;
          allRs[i].nickname = nickname;
          allRs[i].gender = gender;
        }
      }

      const allArr__ = JSON.stringify(allRs);
      yield redisInstance.delKey(key4);
      yield redisInstance.onlySet(key4, allArr__);

      const redisRS__ = yield redisInstance.hmget(key5, `${userid}`);
      if (redisRS__ && redisRS__[0]) {
        const userInfo = JSON.parse(redisRS__[0]);
        userInfo.nickname = nickname;
        userInfo.avatar = avatar;
        userInfo.gender = gender;
        yield redisInstance.hashAdd(key5, [`${userid}`, JSON.stringify(userInfo)]);
      }
    }
    console.log('SUCCESS');
  });
}

task();
