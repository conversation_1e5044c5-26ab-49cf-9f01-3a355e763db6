// 注销脚本
const mysqlInstance = require('../models/mysql').mysqlInstance;
const argv = process.argv

async function start(userid) {

    const user = await mysqlInstance.getUserInfoByUserid(userid);
    // 删除账号名下的所有动态，删除关注关系，手机号或微信号置空，昵称修改为已注销
    await mysqlInstance.delUserContent(userid)

    await mysqlInstance.delUserAttention(userid)

    await mysqlInstance.delUserPhoneOrWechat(userid)

    console.log('SUCCESS');
}

start(argv[2]);