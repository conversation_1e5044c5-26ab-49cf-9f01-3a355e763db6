const mysqlInstance = require('../models/mysql').mysqlInstance;
// 8262
const task = async () => {
    const userInfo = await mysqlInstance.getNoTimeAtt()
    for (let i = 0; i < userInfo.length; i++) {
        const userid1 = userInfo[i].userid1
        const userid2 = userInfo[i].userid2
        const userTime1 = await mysqlInstance.getUserCreateTime(userid1)
        const userTime2 = await mysqlInstance.getUserCreateTime(userid2)
        let time = 0
        if (userTime1[0].create_time > userTime2[0].create_time) {
            time = userTime1[0].create_time
        } else {
            time = userTime2[0].create_time
        }
        let shijian = 0
        if (userInfo[i].update_time) {
            shijian = getRandom(time, userInfo[i].update_time)
        } else {
            shijian = getRandom(time, time + 60 * 60 * 24 * 30)
        }
        await mysqlInstance.updateUserAttention(userInfo[i].id, shijian)
    }
}

function getRandom(start, end) {
    var choice = end - start + 1;
    return Math.floor(Math.random() * choice + start);
}

task()
