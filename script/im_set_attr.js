const request = require('request');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const USERSIG = 'eJxNjV1vgjAYRv9Lb1lm30I7s2QXU*YskRnmFOSGIK2sg1FSq1GX-fcRopm35zwfP*hjtrjPi0LvG5vZUyvRI8LorsdKyMaqrZKmg5RM0wvP21aJLLeZa8RNfCeqrFcdAw9joB7zyEXKY6uMzPKt7deAUkowvlYP0uyUbjpBMDDAAPhfWvUt*0o3B*xhyK5-quxw*LIc88j-qrlf6dRfu2fnGI5ep-MoWbD34LQZseItcFZkPaggjLmMeMn3s*Vz6A5rXfqfaZnABOqxJHxQsGC*oUbH4uAIFSeT4gn9-gHJHlWD';


async function imSetAttr() {
    let total = 0
    let page = 0;
    let self = this;
    do {
        page++;
        const userArr = await mysqlInstance.getAllUserId(page);
        total = userArr.length;
        const allIDArr = [];
        for (let i = 0; i < userArr.length; i++) {
            const obj = {
                To_Account: `${userArr[i].id}`,
                Attrs: {
                    userid: `${userArr[i].id}`
                }
            }
            allIDArr.push(obj);
        }
        const data = {
            UserAttrs: allIDArr
        }
        const data__ = {
            data: data,
            MsgRandom: parseInt(getRandomCode())
        }
        const result_ = await imSetAttrFun(data__);
        console.log(result);
        if (result_ && result_['type'] === 'error') {
            console.log(`IM_Import error : ERROR : ${result_['msg']} : INFO : ${result_['info']}`)
            return {
                backSuccess: false,
                msg: 'IM error ' + result_['msg']
            }
        } else {
            console.log('success');
        }
        console.log(`>>>>>::::::::::${total}`);
    } while (total === 500)

}

function imSetAttrFun(args) {
    console.log(JSON.stringify(args['data']))
    const data = {
        url: `https://console.tim.qq.com/v4/openim/im_set_attr?usersig=${USERSIG}&identifier=52HZ&sdkappid=1400154642&random=${args['MsgRandom']}&contenttype=json`,
        method: 'POST',
        json: true,
        body: args['data']
    }
    return new Promise((resolve, reject) => {
        request(data, function (error, response, body) {
            // console.log('body:', body); // Print the HTML for the Google homepage.
            if (!error && body.ActionStatus === 'OK') {
                resolve(body);
            } else {
                resolve({
                    'type': 'error',
                    'msg': body.ErrorCode,
                    'info': body.ErrorInfo
                });
            }
        });
    });
}

function getRandomCode(min = 10000000, max = 99999999) {
    return Math.round(Math.random() * (max - min) + min);
}

imSetAttr()