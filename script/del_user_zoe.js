const co = require('co');
const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;


function task() {
    return co(function *() {
        const likeRs = yield mysqlInstance.getAllUserId2();
        for (let i = 0; i < likeRs.length; i++) {
            const userid = likeRs[i].id;
            const key = `HASH:USER:HERTZ:TIME:${userid}`
            yield redisInstance.delKey(key)
        }
        console.log('success')
    })
}

task();