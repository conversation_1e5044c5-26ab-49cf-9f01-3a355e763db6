const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;

async function start() {
    let userArr = await mysqlInstance.getAllUserId2();
    for (let i = 0; i < userArr.length; i++) {
        const userid = userArr[i].id;
        const location = userArr[i].city
        const redisUserInfo = await redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['country', location, 'province', location, 'city', location]);
        if (redisUserInfo && redisUserInfo['type'] === 'error') {
            console.log(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${userid}`)
        } else {
            console.log(`SUCCESS::::::::${userid}`);
        }
    }
}

start();