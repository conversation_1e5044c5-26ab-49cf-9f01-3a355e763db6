const mysqlInstance = require('../models/mysql').mysqlInstance;

async function start() {
    let userArr = await mysqlInstance.getAllUserBlack();
    for (let i = 0; i < userArr.length; i++) {
        const status = userArr[i].status;
        const userid = userArr[i].userid;
        const blackUserid = userArr[i].black_userid;
        if (status == 1) {
            await mysqlInstance.insertIntoUserBlack(blackUserid, userid, 1);
        } else if (status == 0) {
            await mysqlInstance.insertIntoUserBlack(blackUserid, userid, 2);
        }
    }
}

start();