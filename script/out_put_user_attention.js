const co = require('co');
const mysql = require('../models/mysql').mysqlInstance;
const fs = require('fs');
function task() {
    return co(function* () {
        const arr = [];
        let from = 0;
        let size = 100;
        do {
            const user = yield mysql.getAllUserIdV2(from, size);
            from += user.length;
            size = user.length;
            for (let i = 0; i < user.length; i++) {
                const user_id = user[i].id;
                console.log(user_id)
                const attentionCount = yield mysql.getUserAttention(user_id);
                const attentionCount2 = yield mysql.getUserAttentionV2(user_id);
                const fansCount = yield mysql.getUserFans(user_id);
                const data = {
                    userName: user[i].nickname,
                    attentionCount: attentionCount[0].count,
                    mutualAttentionCount: attentionCount2[0].count,
                    fansCount: fansCount[0].count
                }
                console.log(`${user_id}::::${JSON.stringify(data)}`);

                arr.push(data);
            }

        } while (size === 100)
        fs.writeFile('./words.txt', JSON.stringify(arr), function (err) {
            if (err) {
                console.log('写文件操作失败');
            } else {
                console.log('写文件操作成功');
            }
        });
    })
}

task();
