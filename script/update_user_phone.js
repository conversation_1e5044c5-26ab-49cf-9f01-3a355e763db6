const argv = process.argv;
const co = require('co');
const { mysqlInstance } = require('../models/mysql');
const { redisInstance } = require('../models/redis');

function task(oldPhone, newPhone) {
    return co(function* () {
        const oldRs = yield mysqlInstance.getUserByPhone(oldPhone);
        if (!oldRs.length) {
            elogger.error('老用户不存在')
            return
        }
        const userid = oldRs[0].id;
        const newRs = yield mysqlInstance.getUserByPhone(newPhone);
        if (newRs.length) {
            const newUserid = newRs[0].id;

            const updateSqlRs = yield mysqlInstance.updateUserMobile(userid, newPhone);
            if (updateSqlRs && updateSqlRs['type'] === 'error') {
                elogger.error('Common Error 11010 ' + updateSqlRs['sql'])
                return
            }
            const redisRs = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['mobile', newPhone]);
            if (redisRs && redisRs['type'] === 'error') {
                elogger.error(`HASH:USER:INFO:>>${redisRs['msg']},userid>>${userid}`)
                return
            }

            const updateSqlRs_ = yield mysqlInstance.updateUserMobile(newUserid, oldPhone);
            if (updateSqlRs_ && updateSqlRs_['type'] === 'error') {
                elogger.error('Common Error 11010 ' + updateSqlRs_['sql'])
                return
            }
            const redisRs_ = yield redisInstance.hashAdd('HASH:USER:INFO:' + newUserid, ['mobile', oldPhone]);
            if (redisRs_ && redisRs_['type'] === 'error') {
                elogger.error(`HASH:USER:INFO:>>${redisRs_['msg']},userid>>${newUserid}`)
                return
            }
        } else {
            const updateSqlRs = yield mysqlInstance.updateUserMobile(userid, newPhone);
            if (updateSqlRs && updateSqlRs['type'] === 'error') {
                elogger.error('Common Error 11010 ' + updateSqlRs['sql'])
                return
            }
            const redisRs = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['mobile', newPhone]);
            if (redisRs && redisRs['type'] === 'error') {
                elogger.error(`HASH:USER:INFO:>>${redisRs['msg']},userid>>${userid}`)
                return
            }
        }
        console.log('SUCCESS');
        process.exit();
    })
}

task(argv[2], argv[3])