'use strict';

module.exports.IOSPayUrl = {
    onLine: {
        host: 'https://buy.itunes.apple.com',
        path: '/verifyReceipt',
    },
    sandBox: {
        host: 'https://sandbox.itunes.apple.com',
        path: '/verifyReceipt',
    },
}

module.exports.responses = {
    21000: 'The App Store could not read the JSON object you provided.',
    21002: 'The data in the receipt-data property was malformed or missing.',
    21003: 'The receipt could not be authenticated.',
    21004: 'The shared secret you provided does not match the shared secret on file for your account.',
    21005: 'The receipt server is not currently available.',
    21006: 'This receipt is valid but the subscription has expired. When this status code is returned to your server, the receipt data is also decoded and returned as part of the response.',
    21007: 'This receipt is from the test environment, but it was sent to the production service for verification. Send it to the test environment service instead.',
    21008: 'This receipt is from the production receipt, but it was sent to the test environment service for verification. Send it to the production environment service instead.',
    21009: 'Internal data access error. Try again later.',
    21010: 'The user account cannot be found or has been deleted.'
};