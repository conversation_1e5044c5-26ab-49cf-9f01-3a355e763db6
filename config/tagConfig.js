const attribute = {
  AB: {
    interval: [10, 13.99],
    remark: '内容图文为主，感性倾向高，同时具有感官倾向。',
    depict:
      '出没于浅海湾，头部常受岩石碰撞，常帮助生病、受伤的同类浮上水面呼吸。像是你，着迷于穿越边界，受伤于不确定性，而跟同类一起时，你说困难只是换气问题。',
    whale: '灰鲸',
    num: 1,
  },
  AC: {
    interval: [14, 17.99],
    remark: '内容感性倾向高，同时喜欢二次元文化/电玩。',
    depict:
      '常驻深海，喜欢单独行动，潜水深度最多可达2992米，哺乳动物种的潜水冠军。像是你，拥有自己的深海，常潜入水底，给海水讲秘密，说你是自己的最佳玩伴。',
    whale: '柯氏喙鲸',
    num: 2,
  },
  AD: {
    interval: [18, 21.99],
    remark: '内容感性倾向高，用户性格较外倾。',
    depict:
      '目前世界上最大的动物，温暖海水与冰冷海水的交汇处，是蓝鲸绝佳的栖息地。像是你，想象力大于这个世界，在人群与自我之间，有自由穿梭的能力，你在哪里都能安睡。',
    whale: '蓝鲸',
    num: 3,
  },
  AE: {
    interval: [22, 25.99],
    remark: '内容感性倾向高，用户性格较内倾。',
    depict:
      '通常见于外海及大陆棚附近，多出现在海底断崖周围，通常2-3只同游。像是你，喜欢在边缘和自己愉快对话，也会和朋友缓慢游行，远离世界中心，是出于你本意。',
    whale: '贝氏喙鲸',
    num: 4,
  },
  BA: {
    interval: [26, 29.99],
    remark: '内容图文为主，新鲜、感官体验强，同时较感性。',
    depict:
      '是鲸鱼从陆地往海洋的过渡形态，现代鲸鱼的祖先，试图重回海洋，并非完全是海洋生物。像是你，在不断的变化中填补新鲜感，又不愿被记忆抛弃，回溯和前进，你可以同时完成。',
    whale: '陆行鲸',
    num: 5,
  },
  BC: {
    interval: [30, 33.99],
    remark: '内容图文为主，新鲜、感官体验强，同时喜欢二次元文化/电玩。',
    depict:
      '达到一定年龄会离开群体，形成自己的集群，长时间潜水，具备独特的外形和喷气形态。像是你，具备独立的价值体系，谁也不知道你悄悄爱着多少事物，用着不同于他人的方式。',
    whale: '抹香鲸',
    num: 6,
  },
  BD: {
    interval: [34, 37.99],
    remark: '内容图文为主，新鲜、感官体验强，用户性格较外倾。',
    depict:
      '数十头或数百头成群游行，听觉视觉较好，能发出各种声音，遭遇船只驱赶时，不会散群。像是你，对世界有敏感的知觉，与天气都能互动。险境中朋友是撑住你的网，而正义是你本能。',
    whale: '短肢领航鲸',
    num: 7,
  },
  BE: {
    interval: [38, 41.99],
    remark: '内容图文为主，新鲜、感官体验强，用户性格较内倾。',
    depict:
      '性情凶猛，善于进攻，不成群，2-3只并肩游泳，具备眷恋性，不轻易离开负伤的同伴。像是你，对于新鲜事物显得贪婪，不善于合群，你看似强大有距离感，但内心有片柔软森林。',
    whale: '虎鲸',
    num: 8,
  },
  CA: {
    interval: [42, 45.99],
    remark: '用户喜欢二次元/电玩，输出感性内容倾向高。',
    depict:
      '多单独或2-3只一起游动，适温水域20℃以上，一般不做远距离洄游。像是你，温和敏感，不失坚定，生活波动在舒适范围内，对于喜欢的一切，你不会真正离开。',
    whale: '鳀鲸',
    num: 9,
  },
  CB: {
    interval: [46, 49.99],
    remark: '用户喜欢二次元/电玩，输出感官型内容倾向高。',
    depict:
      '即使在黑暗的北极冬季，也能在尖锐冰山间游动，甚至会主动撞裂薄冰层，浮出海面。像是你，明知周围危险，也愿意为了双眼的渴望，撞破质疑的冰面，你总能肆意观赏和呼吸。',
    whale: '北极露脊鲸',
    num: 10,
  },
  CD: {
    interval: [50, 53.99],
    remark: '用户喜欢二次元/电玩，用户性格较外倾。',
    depict:
      '通常以1至4头鲸组成的群体出现，会在静止或缓慢行进的船附近游动，观察到满意为止。像是你，阻止不了好奇心的外溢，世界在你眼里是巨大游乐园，你是其中最勤奋的玩家。',
    whale: '弓头鲸',
    num: 11,
  },
  CE: {
    interval: [54, 57.99],
    remark: '用户喜欢二次元/电玩，用户性格较内倾。',
    depict:
      '单独游动居多，几乎无结群现象，由于呼吸喷出的雾柱细而稀薄，消失得很快，不易观察。像是你，喜欢单独行动胜过结群，有人可交流，但没人见过你的心底，那一片无人秘境。',
    whale: '小须鲸',
    num: 12,
  },
  DA: {
    interval: [58, 61.99],
    remark: '用户性格较外倾，但有感性的一面。',
    depict:
      '会吐出环状泡泡，并追赶泡泡，当夏季到来，皮肤进行蜕变，肤色从白色转变为淡黄。像是你，独处从来不是难事，开发乐趣是你的天赋，而背过太阳，你会展露出另一面。',
    whale: '白鲸',
    num: 13,
  },
  DB: {
    interval: [62, 65.99],
    remark: '用户性格较外倾，更倾向产出感官型内容。',
    depict:
      '会跃身或以鲸尾击浪，浮于海面观察四周。以洪厚的嗓音，在迁移、进食、和社交时沟通。像是你，长着不同的触角，偏爱视觉体验，你在与人类共处时，发出自己的声音不算难事。',
    whale: '短肢领航鲸',
    num: 14,
  },
  DC: {
    interval: [66, 69.99],
    remark: '用户性格较外倾，同时都二次元文化/电玩感兴趣。',
    depict:
      '喜结成小群，并肩游动，呼吸与潜水的方式独特，常在连续呼吸的最后一次，垂直投入海里。像是你，将自己豢养在爱好的小圈子里，安全行进，用你独有的呼吸方式，跟世界交换讯息。',
    whale: '座头鲸',
    num: 15,
  },
  DE: {
    interval: [70, 73.99],
    remark: '用户性格具有复杂性，内外倾向兼有。',
    depict:
      '在靠近海岸的地方，追逐其他海洋哺乳动物，活动路线飘忽，通常不会在一个地方停留太久。像是你，擅长进攻其他人类的精神地带，用以摄取养分，你的轨迹无法预测，性格无法定义。',
    whale: '过客鲸',
    num: 16,
  },
  EA: {
    interval: [82, 85.99],
    remark: '用户性格较内倾，输出的内容倾向感性图文。',
    depict:
      '出没于印度洋-太平洋的热带和暖温带海域，对其他生活习性一无所知，生性应该非常谨慎。像是你，眷恋熟悉的环境，安全感是必需品，低调行事带来的神秘感，是你不可避免的标签。',
    whale: '银杏齿中喙鲸',
    num: 17,
  },
  EB: {
    interval: [78, 81.99],
    remark: '用户性格较内倾，但偏向输出感官型内容。',
    depict:
      '头圆，口大，有着较为恐怖的面孔，全身黑色，存在很强的同伴间眷恋性，较少单独行动。像是你，悄悄吞下生活的各种美味，贪婪又勇敢，隐隐透着凶猛，而朋友知道你是温柔本身。',
    whale: '伪虎鲸',
    num: 18,
  },
  EC: {
    interval: [74, 77.99],
    remark: '用户性格较内倾，同时喜欢二次元文化/电玩。',
    depict:
      '摄食行动没有规律，浮现海面的时间极短，无背鳍，脖子和椎骨的连接方式不同于一般鲸鱼。像是你，不定期补充喜好，长时间的独处并不妨碍你与世界联系，你只是有自己的通话方式。',
    whale: '一角鲸',
    num: 19,
  },
  ED: {
    interval: [70, 73.99],
    remark: '用户性格具有复杂性，内外倾向兼有。',
    depict:
      '在靠近海岸的地方，追逐其他海洋哺乳动物，活动路线飘忽，通常不会在一个地方停留太久。像是你，擅长进攻其他人类的精神地带，用以摄取养分，你的轨迹无法预测，性格无法定义。',
    whale: '过客鲸',
    num: 16,
  },
  OTHER: {
    interval: [86, 89.99],
    remark: '各个属性值均不明显（均<30%），没有特定的倾向，复合型或未进行认真选择。',
    depict:
      '广食性鲸种，基本以群集性饵料生物为食，多单独或成对行动，结成小群洄游。像是你，感兴趣的范围为“一切未知”，能只身探险，也能投入队伍，只要方向不违背你本意。',
    whale: '鳁鲸',
    num: 20,
  },
};

const tag = {
  1: {
    tagId: '1',
    name: '蒸汽波',
    type: '0',
  },
  2: {
    tagId: '2',
    name: '后摇',
    type: '0',
  },
  3: {
    tagId: '3',
    name: 'funk',
    type: '0',
  },
  4: {
    tagId: '4',
    name: '民谣',
    type: '0',
  },
  5: {
    tagId: '5',
    name: '爵士',
    type: '0',
  },
  6: {
    tagId: '6',
    name: '纪录片',
    type: '0',
  },
  7: {
    tagId: '7',
    name: '小众电影',
    type: '0',
  },
  8: {
    tagId: '8',
    name: '影展',
    type: '0',
  },
  9: {
    tagId: '9',
    name: '画展',
    type: '0',
  },
  10: {
    tagId: '10',
    name: '日剧',
    type: '0',
  },
  11: {
    tagId: '11',
    name: '美剧',
    type: '0',
  },
  12: {
    tagId: '12',
    name: '镜头记录者',
    type: '1',
  },
  13: {
    tagId: '13',
    name: '太空诗人',
    type: '1',
  },
  14: {
    tagId: '14',
    name: '酒后表演艺术家',
    type: '1',
  },
  15: {
    tagId: '15',
    name: '野生vlogger',
    type: '1',
  },
  16: {
    tagId: '16',
    name: '杂食听歌',
    type: '1',
  },
  17: {
    tagId: '17',
    name: '音乐发烧病友',
    type: '1',
  },
  18: {
    tagId: '18',
    name: '演出现场出没人员',
    type: '1',
  },
  19: {
    tagId: '19',
    name: '爱写日记',
    type: '1',
  },
  20: {
    tagId: '20',
    name: '文字贪婪食客',
    type: '1',
  },
  21: {
    tagId: '21',
    name: '观影爱好者',
    type: '1',
  },
  1001: {
    tagId: '1001',
    name: '撸铁',
    type: '0',
  },
  1002: {
    tagId: '1002',
    name: '潜水',
    type: '0',
  },
  1003: {
    tagId: '1003',
    name: '极限运动',
    type: '0',
  },
  1004: {
    tagId: '1004',
    name: '改装文化',
    type: '0',
  },
  1005: {
    tagId: '1005',
    name: '街头文化',
    type: '0',
  },
  1006: {
    tagId: '1006',
    name: 'Hiphop',
    type: '0',
  },
  1007: {
    tagId: '1007',
    name: '电子产品',
    type: '0',
  },
  1008: {
    tagId: '1008',
    name: '摄影',
    type: '0',
  },
  1009: {
    tagId: '1009',
    name: '金属硬核',
    type: '0',
  },
  1010: {
    tagId: '1010',
    name: 'ROCK',
    type: '0',
  },
  1011: {
    tagId: '1011',
    name: 'EDM',
    type: '0',
  },
  1012: {
    tagId: '1012',
    name: '朋克',
    type: '0',
  },
  1013: {
    tagId: '1013',
    name: 'trap',
    type: '0',
  },
  1014: {
    tagId: '1014',
    name: '跑酷',
    type: '0',
  },
  1015: {
    tagId: '1015',
    name: '赛车',
    type: '0',
  },
  1016: {
    tagId: '1016',
    name: '滑板',
    type: '0',
  },
  1017: {
    tagId: '1017',
    name: '蹦迪爱好者',
    type: '1',
  },
  1018: {
    tagId: '1018',
    name: '每日穿搭',
    type: '1',
  },
  1019: {
    tagId: '1019',
    name: '化妆必须自拍',
    type: '1',
  },
  1020: {
    tagId: '1020',
    name: '黑暗中的dancer',
    type: '1',
  },
  1021: {
    tagId: '1021',
    name: '玩点儿乐器',
    type: '1',
  },
  1022: {
    tagId: '1022',
    name: '浴室歌手',
    type: '1',
  },
  1023: {
    tagId: '1023',
    name: '深夜街头游荡者',
    type: '1',
  },
  2001: {
    tagId: '2001',
    name: '古风',
    type: '0',
  },
  2002: {
    tagId: '2002',
    name: 'Cosplay',
    type: '0',
  },
  2003: {
    tagId: '2003',
    name: '手办',
    type: '0',
  },
  2004: {
    tagId: '2004',
    name: '漫展',
    type: '0',
  },
  2005: {
    tagId: '2005',
    name: '汉服',
    type: '0',
  },
  2006: {
    tagId: '2006',
    name: 'switch',
    type: '0',
  },
  2007: {
    tagId: '2007',
    name: 'PS4',
    type: '0',
  },
  2008: {
    tagId: '2008',
    name: 'steam',
    type: '0',
  },
  2009: {
    tagId: '2009',
    name: 'LOL',
    type: '0',
  },
  2010: {
    tagId: '2010',
    name: '吃鸡',
    type: '0',
  },
  2011: {
    tagId: '2011',
    name: '日漫',
    type: '0',
  },
  2012: {
    tagId: '2012',
    name: '日番',
    type: '0',
  },
  2013: {
    tagId: '2013',
    name: '日系女孩',
    type: '1',
  },
  2014: {
    tagId: '2014',
    name: '日系男孩',
    type: '1',
  },
  2015: {
    tagId: '2015',
    name: '守望先锋',
    type: '0',
  },
  3001: {
    tagId: '3001',
    name: '现充玩家',
    type: '1',
  },
  3002: {
    tagId: '3002',
    name: '靠嘴吃饭',
    type: '1',
  },
  3003: {
    tagId: '3003',
    name: '轻社交达人',
    type: '1',
  },
  3004: {
    tagId: '3004',
    name: '话痨患者',
    type: '1',
  },
  3005: {
    tagId: '3005',
    name: '好奇一切事物',
    type: '1',
  },
  3006: {
    tagId: '3006',
    name: '梦想当个行路者',
    type: '1',
  },
  3007: {
    tagId: '3007',
    name: '情绪稳定',
    type: '1',
  },
  3008: {
    tagId: '3008',
    name: '散步爱好者',
    type: '1',
  },
  4001: {
    tagId: '4001',
    name: '丧',
    type: '1',
  },
  4002: {
    tagId: '4002',
    name: '肥宅快乐',
    type: '1',
  },
  4003: {
    tagId: '4003',
    name: '不爱说话',
    type: '1',
  },
  4004: {
    tagId: '4004',
    name: '社恐患者',
    type: '1',
  },
  4005: {
    tagId: '4005',
    name: '网络交流也认真',
    type: '1',
  },
  4006: {
    tagId: '4006',
    name: '双重人格',
    type: '1',
  },
  4007: {
    tagId: '4007',
    name: '金牌倾听者',
    type: '1',
  },
  4008: {
    tagId: '4008',
    name: '树洞扮演者',
    type: '1',
  },
  4009: {
    tagId: '4009',
    name: '擅长发呆',
    type: '1',
  },
  5001: {
    tagId: '5001',
    name: '白羊座',
    type: '1',
  },
  5002: {
    tagId: '5002',
    name: '金牛座',
    type: '1',
  },
  5003: {
    tagId: '5003',
    name: '双子座',
    type: '1',
  },
  5004: {
    tagId: '5004',
    name: '巨蟹座',
    type: '1',
  },
  5005: {
    tagId: '5005',
    name: '狮子座',
    type: '1',
  },
  5006: {
    tagId: '5006',
    name: '处女座',
    type: '1',
  },
  5007: {
    tagId: '5007',
    name: '天秤座',
    type: '1',
  },
  5008: {
    tagId: '5008',
    name: '天蝎座',
    type: '1',
  },
  5009: {
    tagId: '5009',
    name: '射手座',
    type: '1',
  },
  5010: {
    tagId: '5010',
    name: '摩羯座',
    type: '1',
  },
  5011: {
    tagId: '5011',
    name: '水瓶座',
    type: '1',
  },
  5012: {
    tagId: '5012',
    name: '双鱼座',
    type: '1',
  },
  5013: {
    tagId: '5013',
    name: '留学党',
    type: '1',
  },
  5014: {
    tagId: '5014',
    name: '学生党',
    type: '1',
  },
  5015: {
    tagId: '5015',
    name: '考研党',
    type: '1',
  },
  5016: {
    tagId: '5016',
    name: '互联网民工',
    type: '1',
  },
  5017: {
    tagId: '5017',
    name: '产品狗',
    type: '1',
  },
  5018: {
    tagId: '5018',
    name: '程序员',
    type: '1',
  },
  5019: {
    tagId: '5019',
    name: '搞点设计',
    type: '1',
  },
  5020: {
    tagId: '5020',
    name: '新媒体从业者',
    type: '1',
  },
  5021: {
    tagId: '5021',
    name: '野生KOL',
    type: '1',
  },
  5022: {
    tagId: '5022',
    name: '金融民工',
    type: '1',
  },
  5023: {
    tagId: '5023',
    name: '半个艺术工作者',
    type: '1',
  },
  5024: {
    tagId: '5024',
    name: '创业党',
    type: '1',
  },
  5025: {
    tagId: '5025',
    name: '自由工作者',
    type: '1',
  },
  5026: {
    tagId: '5026',
    name: 'LGBT',
    type: '1',
  },
  5027: {
    tagId: '5027',
    name: '中华小当家',
    type: '1',
  },
  5028: {
    tagId: '5028',
    name: '工作狂',
    type: '1',
  },
};

module.exports.attribute = attribute;
module.exports.tag = tag;
