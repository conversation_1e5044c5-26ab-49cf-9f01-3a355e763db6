// 标签权重配置表
// 说明：key为标签ID，value为权重。未配置的标签默认权重为1。
// 代表性强/稀有/深度兴趣/特殊身份：1.5~2.0
// 普通/常见/大众化标签：1.0
// 泛用性/娱乐性/弱区分度标签：0.8~0.9

module.exports = {
  // 音乐/文艺/影视类
  1: 1.7, // 蒸汽波：风格独特，表达性高，小众但社群粘性强
  2: 1.5, // 后摇：音乐深度兴趣
  3: 1.4, // funk：音乐风格独特，但表达力略弱，下调
  4: 1.2, // 民谣：中度兴趣
  5: 1.3, // 爵士：风格兴趣标签，识别力一般
  6: 1.8, // 纪录片：典型深度内容偏好，高识别性
  7: 1.7, // 小众电影：强人格化标签，具行为导向
  8: 1.5, // 影展：中度表达兴趣
  9: 1.5, // 画展：视觉文化兴趣标签
  10: 1.0, // 日剧：大众化
  11: 1.0, // 美剧：泛娱乐
  12: 1.8, // 镜头记录者：表达欲极强，标签画像力高
  13: 1.5, // 太空诗人：创意型标签，但偏虚构表达，下调
  14: 1.7, // 酒后表演艺术家：偏行为角色型，表达度高
  15: 1.6, // 野生vlogger：持续创作型
  16: 1.0, // 杂食听歌：泛兴趣
  17: 1.2, // 音乐发烧病友：轻度专业偏好
  18: 1.3, // 演出现场出没人员：行为驱动型
  19: 1.5, // 爱写日记：情绪表达高
  20: 1.5, // 文字贪婪食客：文字内容深度偏好
  21: 1.0, // 观影爱好者：泛类标签，识别度弱

  // 潮流/运动/极限类
  1001: 1.2, // 撸铁：大众运动偏好
  1002: 1.5, // 潜水：小众、高成本行为，识别力较强
  1003: 1.7, // 极限运动：稀有+强标签性
  1004: 1.5, // 改装文化：亚文化兴趣
  1005: 1.3, // 街头文化：轻潮流向
  1006: 1.2, // Hiphop：亚文化表现力适中
  1007: 1.0, // 电子产品：大众偏好
  1008: 1.5, // 摄影：创作表达型兴趣，上调
  1009: 1.6, // 金属硬核：极端风格偏好
  1010: 1.2, // ROCK：大众与小众之间
  1011: 1.0, // EDM：泛娱乐向
  1012: 1.2, // 朋克：态度型标签，社群性适中
  1013: 1.0, // trap：亚流派，表达性不强
  1014: 1.3, // 跑酷：表现力强但人群基数极少，下调
  1015: 1.2, // 赛车：兴趣导向明确但泛化
  1016: 1.2, // 滑板：轻潮流文化
  1017: 1.0, // 蹦迪爱好者：泛娱乐
  1018: 1.0, // 每日穿搭：平台中等表达行为
  1019: 1.0, // 化妆必须自拍：娱乐性标签，区分力低
  1020: 1.2, // 黑暗中的dancer：轻人格化标签
  1021: 1.0, // 玩点儿乐器：轻技能向
  1022: 1.0, // 浴室歌手：趣味向标签
  1023: 1.0, // 深夜街头游荡者：轻情绪行为标签

  // 二次元/ACG/游戏类
  2001: 1.3, // 古风：文化审美向
  2002: 1.5, // Cosplay：视觉表达兴趣，下调
  2003: 1.6, // 手办：收藏类，粘性强
  2004: 1.5, // 漫展：参与型兴趣标签
  2005: 1.3, // 汉服：文化表达
  2006: 1.0, // switch：游戏设备类标签，泛用
  2007: 1.0, // PS4：同上
  2008: 1.0, // steam：同上
  2009: 1.0, // LOL：主流游戏
  2010: 1.0, // 吃鸡：主流游戏
  2011: 1.0, // 日漫：泛类兴趣
  2012: 1.0, // 日番：同上
  2013: 1.2, // 日系女孩：审美标签
  2014: 1.2, // 日系男孩：审美标签
  2015: 1.0, // 守望先锋：游戏类泛用标签

  // 社交/性格/人格类
  3001: 1.5, // 现充玩家：外向活跃型
  3002: 1.2, // 靠嘴吃饭：轻社交能力
  3003: 1.5, // 轻社交达人：社交驱动表达，上调
  3004: 1.7, // 话痨患者：极高表达欲
  3005: 1.8, // 好奇一切事物：广泛参与型人格标签
  3006: 1.7, // 梦想当个行路者：情绪+价值型人格
  3007: 1.5, // 情绪稳定：人格特质，平台稳定用户偏好
  3008: 1.2, // 散步爱好者：温和型兴趣标签

  // 情绪/内向/特殊心理
  4001: 1.6, // 丧：情绪主导标签
  4002: 1.2, // 肥宅快乐：自嘲+娱乐型
  4003: 1.3, // 不爱说话：静态特质标签
  4004: 1.5, // 社恐患者：心理特征标签，下调
  4005: 1.5, // 网络交流也认真：表达意图高
  4006: 1.8, // 双重人格：人格复杂度高，稀缺性强
  4007: 1.7, // 金牌倾听者：内向+共情标签
  4008: 1.8, // 树洞扮演者：强倾诉/聆听意愿
  4009: 1.2, // 擅长发呆：轻度性格标签

  // 星座/职业/身份类
  5001: 0.8,
  5002: 0.8,
  5003: 0.8,
  5004: 0.8,
  5005: 0.8,
  5006: 0.8,
  5007: 0.8,
  5008: 0.8,
  5009: 0.8,
  5010: 0.8,
  5011: 0.8,
  5012: 0.8, // 星座类：泛娱乐，识别力弱

  5013: 1.0, // 留学党：身份标签
  5014: 1.0, // 学生党
  5015: 1.0, // 考研党
  5016: 1.0, // 互联网民工
  5017: 1.0, // 产品狗
  5018: 1.0, // 程序员
  5019: 1.0, // 搞点设计
  5020: 1.0, // 新媒体从业者
  5021: 1.2, // 野生KOL：表达力中等，识别度适中
  5022: 1.0, // 金融民工
  5023: 1.5, // 半个艺术工作者：创作意愿强，上调
  5024: 1.5, // 创业党：高表现意愿身份
  5025: 1.5, // 自由工作者：强个性表达群体
  5026: 1.8, // LGBT：身份标签，区分度极高
  5027: 1.3, // 中华小当家：趣味个性标签
  5028: 1.2, // 工作狂：行为表现稳定型标签
};
