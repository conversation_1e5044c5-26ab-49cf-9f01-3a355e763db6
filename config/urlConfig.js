'use strict';

module.exports.URLConfig = {
  noCheck: [],
  noLogin: [
    '/n/sms/code',
    '/n/common/mobile/judge',
    '/n/common/wx/login/new',
    '/n/common/validate_code/new',
    '/n/common/invitationCode/new',
    '/n/script/user/active',
    '/n/agreement',
    '/n/script/whitelist/get',
    '/n/script/whitelist/add',
    '/n/script/whitelist/del',
    '/n/script/get',
    '/n/common/invitationCode',
    '/n/common/code',
    '/n/common/validate_code',
    '/n/common/wx/login',
    '/n/index.html',
    '/n/common/qiniu/callback',
    '/n/common/usersigFun',
    '/n/test',
    '/n/common/invitationStatus',
    '/n/im/black',
    '/n/test2',
    '/n/common/user/verify',
    '/n/common/user/verify/qa',
    '/n/common/login/password',
    '/n/common/free',
    '/n/user/cocreation',
  ],
  notUse: [
    '/n/content/like',
    '/n/content/comment',
    '/n/attention/add',
    '/n/content/like/new',
    '/n/content/like/release',
  ],
  reject_android: [
    '/n/sms/code',
    '/n/common/mobile/judge',
    '/n/common/wx/login/new',
    '/n/common/validate_code/new',
    '/n/common/invitationCode/new',
    '/n/common/invitationCode',
    '/n/common/code',
    '/n/common/validate_code',
    '/n/common/wx/login',
    '/n/common/usersigFun',
    '/n/common/invitationStatus',
  ],
  bot: ['/bot/login', '/bot/user/info'],
};
