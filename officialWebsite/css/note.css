* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #1a1a1a;
  --secondary-color: #4a4a4a;
  --accent-color: #007bff;
  --light-color: #ffffff;
  --light-bg: #f8f9fa;
  --border-color: #e0e0e0;
  --shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --max-width: 1200px;
  --spacing: 100px;
}

body {
  font-family: 'Microsoft Yahei', Arial, sans-serif;
  line-height: 1.6;
  color: var(--secondary-color);
  background-color: var(--light-color);
  overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--primary-color);
  font-weight: 500;
  margin-bottom: 1rem;
  line-height: 1.3;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-color);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 4px;
}

.container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background-color: var(--light-color);
  box-shadow: var(--shadow);
  padding: 1rem 0;
  z-index: 1000;
}

.logo {
  margin-left: 10px;
}

.logo a {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
}

.logo a:before {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: 10px;
  background-image: url('../img/icon.ico');
  background-size: contain;
  background-repeat: no-repeat;
}

.hero {
  height: 100vh;
  background: url(../img/6.webp) center/cover no-repeat;
  display: flex;
  align-items: center;
  text-align: center;
  position: relative;
}

.hero:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
}

.hero p {
  font-size: 1.1rem;
  color: var(--secondary-color);
}

.guide {
  padding: var(--spacing) 0;
}

.light-bg {
  background-color: var(--light-bg);
}

.guide-content {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.guide-content.reverse {
  flex-direction: row-reverse;
}

.guide-text {
  flex: 1;
}

.guide-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.guide-text h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: var(--accent-color);
}

.guide-images {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.guide-images img {
  width: 100%;
  aspect-ratio: auto;
  object-fit: contain;
  background-color: #f5f5f5;
  box-shadow: var(--shadow);
  transition: var(--transition);
  padding: 5px;
}

.guide-images img:hover {
  transform: translateY(-10px);
}

.contact {
  padding: var(--spacing) 0;
}

.contact h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.contact-info {
  max-width: 600px;
  margin: 0 auto;
  margin-left: 0;
  text-align: left;
}

.info-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  text-align: left;
}

.info-item h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--accent-color);
}

.info-item p {
  font-size: 1.1rem;
  margin-bottom: 0;
}

.footer {
  background-color: var(--primary-color);
  color: var(--light-color);
  padding: 2rem 0;
  text-align: center;
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.icp {
  font-size: 0.9rem;
  color: #aaa;
}

.icp a {
  color: #aaa;
}

.icp a:hover {
  color: var(--light-color);
}

.beian a {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #aaa;
}

.beian a:hover {
  color: var(--light-color);
}

.beian img {
  width: 15px;
  height: 15px;
}

@media screen and (max-width: 992px) {
  :root {
    --spacing: 70px;
  }

  .guide-content {
    flex-direction: column;
    gap: 2rem;
  }

  .guide-content.reverse {
    flex-direction: column;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .guide-text h2 {
    font-size: 2rem;
  }

  .guide-text h3 {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 768px) {
  :root {
    --spacing: 50px;
  }

  .hero-content {
    padding: 1.5rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .guide-text h2 {
    font-size: 1.8rem;
  }

  .guide-text h3 {
    font-size: 1.3rem;
  }

  .guide-images {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}

@media screen and (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .hero h1 {
    font-size: 1.8rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .guide-text h2 {
    font-size: 1.5rem;
  }

  .guide-text h3 {
    font-size: 1.2rem;
  }

  .guide-images {
    grid-template-columns: 1fr;
  }

  .info-item {
    padding: 1.5rem;
  }
}
