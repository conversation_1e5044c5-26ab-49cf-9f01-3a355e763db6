window.addEventListener('scroll', function () {
  const navbar = document.querySelector('.navbar');
  if (window.scrollY > 50) {
    navbar.style.padding = '0.7rem 0';
    navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
  } else {
    navbar.style.padding = '1rem 0';
    navbar.style.backgroundColor = 'var(--light-color)';
  }
});

document.addEventListener('DOMContentLoaded', function () {
  const animateElements = document.querySelectorAll('.guide-content, .info-item');
  function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.75;
  }
  function checkAnimation() {
    animateElements.forEach((element) => {
      if (isInViewport(element) && !element.classList.contains('animate')) {
        element.classList.add('animate');
      }
    });
  }
  checkAnimation();
  window.addEventListener('scroll', checkAnimation);
  animateElements.forEach((element) => {
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
    element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
  });
  document.head.insertAdjacentHTML(
    'beforeend',
    `
    <style>
      .animate {
        opacity: 1 !important;
        transform: translateY(0) !important;
      }
    </style>
  `,
  );
});
