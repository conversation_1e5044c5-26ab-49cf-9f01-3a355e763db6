var nodemailer = require('nodemailer');

class mailService {
    async sendmail(text) {
        var mail = '<EMAIL>'
        var transporter = nodemailer.createTransport({
            service: '163',
            auth: {
                user: '<EMAIL>',
                pass: 'zgx200034@'
            }
        });
        var mailOptions = {
            from: '<EMAIL>',
            to: mail,
            subject: '服务端BUG',
            text: 'Nodejs之邮件发送',
            html: `${text}`
        };
    
        transporter.sendMail(mailOptions, function (error, info) {
            if (!error) {
                console.log("邮件发送成功，请注意查收！")
            } else {
                console.log(error);
            }
        });
    
    };
}

module.exports.MailService = new mailService();