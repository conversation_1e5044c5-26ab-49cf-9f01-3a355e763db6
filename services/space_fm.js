'use strict';
const request = require('request');

class SFMService {
  async getMusicInfo(url) {
    const self = this;
    url = self.httpString(url);
    if (!url.length) {
      return {
        type: false,
        data: 'URL无法解析，请传入完整的URL',
      };
    }
    url = url[0];
    const result = await self.musicInfoRequest(url);
    if (result && !result['type']) {
      elogger.error(`SPACEFM::::${url}:::${JSON.stringify(result['msg'])}`);
      return {
        type: false,
        data: result.msg,
      };
    }
    return {
      type: true,
      data: result.data,
    };
  }
  musicInfoRequest(url) {
    const data = {
      json: true,
      method: 'POST',
      url: 'https://spacefm.bopulab.cn/music/getMusicByUrl',
      headers: {
        'Content-Type': 'application/json',
        udid: '95F20092-5E4A-41D3-9DA4-7AFB3B96A137',
        token: '91c5f3d8521911e9ba3629c65d3d2beb',
        userId: '91a06a75-5219-11e9-ba36-2f3acdfb4faa',
      },
      body: { url: url },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        if (!error && body.code === 0) {
          resolve({
            type: true,
            data: body.data,
          });
        } else {
          resolve({
            type: false,
            msg: body.message,
          });
        }
      });
    });
  }

  httpString(s) {
    var reg = /(http:\/\/|https:\/\/)((\w|=|\?|\.|\/|&|-)+)/g;
    var reg = /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
    s = s.match(reg);
    if (!s) {
      s = [];
    }
    return s;
  }
}

module.exports.SFMService = new SFMService();
