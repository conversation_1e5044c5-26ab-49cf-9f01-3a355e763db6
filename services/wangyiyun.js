const a = 'http://music.163.com/api/song/detail/?id=406346500&ids=[406346500]';
const b = 'http://music.163.com/song/media/outer/url?id=406346500.mp3';
const c = 'http://music.163.com/song/1313354324/?userid=33344360 (来自@网易云音乐)';

function getCaption(obj) {
  var index1 = obj.lastIndexOf('/song/');
  var index2 = obj.lastIndexOf('/?');
  console.log(index1, index2);
  obj = obj.substring(index + 1, obj.length);
  //  console.log(obj);
  return obj;
}
getCaption(c);
