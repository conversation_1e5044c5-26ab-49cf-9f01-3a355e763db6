'use strict';
const co = require('co');
const request = require('request');
const { mysqlInstance } = require('../models/mysql');
const redisInstance = require('../models/redis').redisInstance;

class IMService {
  async sendMsg(args) {
    try {
      // const self = this;
      // const to_Account = `${args['userid']}`;
      // const text = args['text'];
      // const MsgRandom = parseInt(self.getRandomCode());
      // const from = args.from;
      // // console.log(args);

      // const key = `STR:USER:PUSH:COUNT:${to_Account}`
      // const redisRS = await redisInstance.get(key);
      // const count = parseInt(redisRS) + 1;
      // await redisInstance.onlySet(key, `${count}`)
      // const data = {
      //     to_Account: to_Account,
      //     text: text,
      //     MsgRandom: MsgRandom,
      //     from: from
      // }
      // console.log('>>>>>>>>>>>>>>>>>>>>', USERSIG);
      // const result = await self.sendMsgFun(data);
      // console.log(result);
      // if (result && result['type'] === 'error') {
      //     elogger.error(`sendMsg IM error From : ${from} : ERROR : ${JSON.stringify(result['msg'])} : INFO : ${result['info']}`)
      // }
      // await self.msgPush(data)
      // return {
      //     backSuccess: true,
      //     data: result
      // }
      return;
    } catch (err) {
      console.log(`>>>>>>>>>>>>>>>>>>>${JSON.stringify(err)}`);
    }
  }

  sendMsgFun(args) {
    const data = {
      url: `https://console.tim.qq.com/v4/openim/sendmsg?usersig=${USERSIG}&identifier=52HZ&sdkappid=**********&random=${args['MsgRandom']}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        SyncOtherMachine: 2, //消息不同步至发送方
        To_Account: args['to_Account'],
        From_Account: '52HZ',
        MsgLifeTime: 3600 * 24, //消息保存60秒
        MsgRandom: args['MsgRandom'],
        MsgTimeStamp: parseInt(Math.round(new Date().getTime() / 1000)),
        MsgBody: [
          {
            MsgType: 'TIMTextElem',
            MsgContent: {
              Text: args['text'],
            },
          },
        ],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }

  async sendBulk(text, userarr) {
    return {
      backSuccess: true,
    };
    // const self = this;
    // const MsgRandom = parseInt(self.getRandomCode());
    // let from = 0
    // let size = 100
    // do {
    //     const to_Account = []
    //     const users = await mysqlInstance.getAllUser(from, size)
    //     from += 100
    //     size = users.length
    //     for (let i = 0; i < users.length; i++) {
    //         const userid = users[i].id
    //         to_Account.push(userid)
    //     }
    // const data = {
    //     to_Accounts: userarr,
    //     text: text,
    //     MsgRandom: MsgRandom,
    // }

    // const result = await self.sendBulkMsgFun(data);
    // if (result && result['type'] === 'error') {
    //     elogger.error(`ERROR : ${result['msg']} : INFO : ${result['info']}`)
    // }
    // } while (size === 100)
    // return {
    //     backSuccess: true,
    // }
  }

  sendBulkMsgFun(args) {
    const data = {
      url: `https://console.tim.qq.com/v4/openim/batchsendmsg?sdkappid=**********&identifier=52HZ&usersig=${USERSIG}&random=${args['MsgRandom']}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        SyncOtherMachine: 2, //消息不同步至发送方
        To_Account: args['to_Accounts'],
        From_Account: '108474',
        MsgLifeTime: 3600 * 24, //消息保存60秒
        MsgRandom: args['MsgRandom'],
        MsgTimeStamp: parseInt(Math.round(new Date().getTime() / 1000)),
        MsgBody: [
          {
            MsgType: 'TIMTextElem',
            MsgContent: {
              Text: args['text'],
            },
          },
        ],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }

  accountImport(args) {
    const self = this;
    return co(function* () {
      const userid = args['userid'];
      const nickName = args['nickName'];
      const avatar = args['avatar'];
      const MsgRandom = parseInt(self.getRandomCode());
      // console.log(args);
      const data = {
        userid: userid,
        nickName: nickName,
        avatar: avatar,
        MsgRandom: MsgRandom,
      };
      const result = yield self.accountImportFun(data);
      // console.log(result);
      if (result && result['type'] === 'error') {
        elogger.error(`IM_Import error : ERROR : ${result['msg']} : INFO : ${result['info']}`);
        return {
          backSuccess: false,
          msg: 'IM error ' + result['msg'],
        };
      }
      // const result_ = yield self.imSetAttr(data);
      // // console.log(result);
      // if (result_ && result_['type'] === 'error') {
      //     elogger.error('IM error02 ' + result_['msg'])
      //     return {
      //         backSuccess: false,
      //         msg: 'IM error ' + result_['msg']
      //     }
      // }
      return {
        backSuccess: true,
        data: result,
      };
    });
  }

  accountImportFun(args) {
    const data = {
      url: `https://console.tim.qq.com/v4/im_open_login_svc/account_import?usersig=${USERSIG}&identifier=52HZ&sdkappid=**********&random=${args['MsgRandom']}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        Identifier: args['userid'],
        Nick: args['nickName'],
        FaceUrl: args['avatar'],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }

  imSetAttr(args) {
    const data = {
      url: `https://console.tim.qq.com/v4/openim/im_set_attr?usersig=${USERSIG}&identifier=52HZ&sdkappid=**********&random=${args['MsgRandom']}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        UserAttrs: [
          {
            To_Account: args['userid'],
            Attrs: {
              userid: args['userid'],
            },
          },
        ],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }

  async msgPush(args) {
    const self = this;
    const to_Account = `${args['to_Account']}`;
    const text = args['text'];
    const MsgRandom = parseInt(self.getRandomCode());
    const from = args.from;
    // console.log(args);

    const data = {
      Identifier: to_Account,
      text: text,
      MsgRandom: MsgRandom,
    };
    // console.log('>>>>>>>>>>>>>>>>>>>>', USERSIG);
    const result = await self.msgPushFun(data);
    // console.log(result);
    if (result && result['type'] === 'error') {
      elogger.error(`IM_PUSH error From : ${from} : ERROR : ${result['msg']} : INFO : ${result['info']}`);
    } else {
      elogger.info(`.......................PUSH success ${data['text']}`);
    }
    // return {
    //     backSuccess: true,
    //     data: result
    // }
  }

  msgPushFun(args) {
    const data = {
      url: `https://console.tim.qq.com/v4/openim/im_push?usersig=${USERSIG}&identifier=52HZ&sdkappid=**********&random=${args['MsgRandom']}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        MsgRandom: args['MsgRandom'],
        Condition: {
          AttrsAnd: {
            userid: args['Identifier'],
          },
        },
        MsgBody: [
          {
            MsgType: 'TIMTextElem',
            MsgContent: {
              Text: args['text'],
            },
          },
        ],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }

  getRandomCode(min = 10000000, max = 99999999) {
    return Math.round(Math.random() * (max - min) + min);
  }

  async addImBlack(userid, to_userid) {
    const self = this;
    const result = await self.addImBlackFun(userid, to_userid);
    // console.log(result);
    if (result && result['type'] === 'error') {
      elogger.error(`addImBlack IM error From : ${from} : ERROR : ${result['msg']} : INFO : ${result['info']}`);
    }
  }

  async rmImBlack(userid, to_userid) {
    const self = this;
    const result = await self.rmImBlackFun(userid, to_userid);
    // console.log(result);
    if (result && result['type'] === 'error') {
      elogger.error(`rmImBlack IM error From : ${from} : ERROR : ${result['msg']} : INFO : ${result['info']}`);
    }
  }

  addImBlackFun(userid, to_userid) {
    const self = this;
    const MsgRandom = parseInt(self.getRandomCode());
    const data = {
      url: `https://console.tim.qq.com/v4/sns/black_list_add?sdkappid=**********&identifier=52HZ&usersig=${USERSIG}&random=${MsgRandom}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        From_Account: `${userid}`,
        To_Account: [to_userid],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }

  rmImBlackFun(userid, to_userid) {
    const self = this;
    const MsgRandom = parseInt(self.getRandomCode());
    const data = {
      url: `https://console.tim.qq.com/v4/sns/black_list_delete?sdkappid=**********&identifier=52HZ&usersig=${USERSIG}&random=${MsgRandom}&contenttype=json`,
      method: 'POST',
      json: true,
      body: {
        From_Account: `${userid}`,
        To_Account: [to_userid],
      },
    };
    return new Promise((resolve, reject) => {
      request(data, function (error, response, body) {
        // console.log('body:', body); // Print the HTML for the Google homepage.
        if (!error && body.ActionStatus === 'OK') {
          console.log(`${userid}--${to_userid}:::黑名单删除成功`);
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: body,
            info: error,
          });
        }
      });
    });
  }
}

module.exports.IMInstance = new IMService();
