'use strict';
const co = require('co');
const request = require('request');
const crypto = require('crypto');
const utf8 = require('utf8');
const redisInstance = require('../models/redis').redisInstance;

const appkey = '5c382d64b465f5f2ad000ceb';
const app_master_secret = 'qtp26ztutxvgsqgmunklfknnb7qzmcyv';

// method = 'POST'
// url = 'http://msg.umeng.com/api/send'
// params = {
//     'appkey': appkey,
//     'timestamp': timestamp,
//     'device_tokens': device_token,
//     'type': 'unicast',
//     'payload': {
//         'body': {
//             'ticker': 'Hello World',
//             'title': '你好',
//             'text': '来自友盟推送',
//             'after_open': 'go_app'
//         },
//         'display_type': 'notification'
//     }
// }
// post_body = json.dumps(params)
// print post_body
// sign = md5('%s%s%s%s' % (method, url, post_body, app_master_secret))

class YMService {
  async msgPush(args) {
    try {
      const self = this;
      const data = {
        text: args['text'],
        device_token: args['device_token'],
        pushCount: parseInt(args['pushCount']) + 1,
      };
      const rs = await redisInstance.get(`SET:LOGIN:STATUS:${args['to_userid']}`);
      if (parseInt(rs) == 2) {
        return;
      }
      const result = await self.sendMsgFun(data);
      if (result && !result['type']) {
        elogger.error(`YOUMENG:::${JSON.stringify(result['msg'])}:::${result['error']}`);
      }
    } catch (err) {
      console.log(`::::::::::::::::::::::::${JSON.stringify(err)}`);
    }
  }
  //TODO args['pushCount']
  sendMsgFun(args) {
    const timestamp = `${parseInt(Math.round(new Date().getTime() / 1000))}`;
    const method = 'POST';
    const url = 'https://msgapi.umeng.com/api/send';

    const data = {
      description: '52HZ',
      production_mode: 'true',
      device_tokens: `${args['device_token']}`,
      appkey: appkey,
      payload: {
        aps: {
          alert: {
            title: '',
            subtitle: '',
            body: args['text'],
          },
          sound: 'default',
          badge: args['pushCount'],
        },
      },
      type: 'unicast',
      timestamp: timestamp,
    };

    const post_body = JSON.stringify(data);

    const sign = method + url + post_body + app_master_secret;
    const md5sum = crypto.createHash('md5').update(sign).digest('hex');
    const body = {
      url: `https://msgapi.umeng.com/api/send?sign=${md5sum}`,
      method: 'POST',
      json: true,
      body: data,
    };
    return new Promise((resolve, reject) => {
      request(body, function (error, response, body) {
        if (!error && body['ret'] == 'SUCCESS') {
          resolve({
            type: true,
            data: body['msg_id'],
          });
        } else {
          resolve({
            type: false,
            msg: body,
            error: error,
          });
        }
      });
    });
  }

  getRandomCode(min = 10000000, max = 99999999) {
    return Math.round(Math.random() * (max - min) + min);
  }
}

module.exports.YMInstance = new YMService();
