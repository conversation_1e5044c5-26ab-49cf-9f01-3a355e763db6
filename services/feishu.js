const crypto = require('crypto');
const request = require('request-promise');

const APP_ID = "cli_a0b27dac0c385014"
const APP_SECRET = "7Z1je6B1Uk5mltsrjHeuycsvkEDaLJPS"
const APP_VERIFICATION_TOKEN = "hAGp04Z5nhgcZ4kOvbfkufJcb21rIIOf"
const privateKey = 'FGHvyQGi1bIaWyYYvqDGLODj10oaDdGS'

/**
         * {
  uuid: '8a6525b6d1e91a2890c3f3e083aff571',
  event: {
    app_id: 'cli_a0b27dac0c385014',
    chat_type: 'private',
    employee_id: 'd3e1f4e8',
    is_mention: false,
    lark_version: 'lark/3.42.8',
    message_id: '',
    msg_type: 'text',
    open_chat_id: 'oc_a4e4d3aef037cf986642e1fbfae1870e',
    open_id: 'ou_6a66d2b40250efaf7f1d5e93e0ff2d06',
    open_message_id: 'om_a6698c251751ac396ef0f35e24935391',
    parent_id: '',
    root_id: '',
    tenant_key: '2d36537b3e0f975e',
    text: '撒打算',
    text_without_at_bot: '撒打算',
    type: 'message',
    union_id: '',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.66 Safari/537.36 Lark/3.42.8 LarkLocale/zh_CN ttnet SDK-Version/3.42.22',
    user_open_id: 'ou_6a66d2b40250efaf7f1d5e93e0ff2d06'
  },
  token: 'hAGp04Z5nhgcZ4kOvbfkufJcb21rIIOf',
  ts: '1614667181.530080',
  type: 'event_callback'
}
         */

/**
 * {
uuid: 'fd760f9613227bbd0936020734e3903e',
event: {
app_id: 'cli_a0b27dac0c385014',
chat_id: 'oc_246099e36cddfa17a56e464c75967f3b',
operator: {
open_id: 'ou_983d041189cda11c62dc015bf5715c01',
user_id: '89fc8ebc'
},
tenant_key: '2d36537b3e0f975e',
type: 'p2p_chat_create',
user: {
name: '张苏雅',
open_id: 'ou_983d041189cda11c62dc015bf5715c01',
user_id: '89fc8ebc'
}
},
token: 'hAGp04Z5nhgcZ4kOvbfkufJcb21rIIOf',
ts: '1614667677.102437',
type: 'event_callback'
}
 */

class ChatBot {
    do_post = async (req_body) => {
        const self = this
        console.log(req_body)

        const token = req_body['token']
        if (token != APP_VERIFICATION_TOKEN) {
            console.log("verification token not match, token =", token)
            args.response("")
            return
        }
        const type = req_body['type']
        if ("url_verification" == type) {
            await self.handle_request_url_verify(req_body)
        } else if ("event_callback" == type) {
            const event = req_body['event'];
            if (event['type'] == 'message') {
                await self.handle_message(event)
            }
        }
        return
    }

    handle_request_url_verify = async (post_obj) => {
        const challenge = post_obj['challenge']
        const rsp = { 'challenge': challenge }
        console.log(rsp)
        return
    }

    handle_message = async (event) => {
        const self = this
        const msg_type = event["msg_type"]
        if (msg_type != "text") {
            console.log("unknown msg_type =", msg_type)
            return
        }

        await self.send_message(event["open_id"], event["text"])
        return

    }

    send_message = async (open_id, text) => {
        const self = this
        const access_token = await self.genAppAccessToken()
        console.log("access_token>>>>>>>>>>>>", access_token)
        if (access_token == "") {
            console.log("unknown access_token =")
            return
        }
        const params = {
            url: "https://open.feishu.cn/open-apis/message/v4/send/",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + access_token
            },
            body: {
                open_id: open_id,
                msg_type: "text",
                content: {
                    text: text
                }
            },
            json: true,
            timeout: 3000,
            method: 'POST',
        };
        return await makeRequest(params);
    }

    send_messageV2 = async (open_id, text) => {
        const self = this
        const access_token = await self.genAppAccessToken()
        console.log("access_token>>>>>>>>>>>>", access_token)
        if (access_token == "") {
            console.log("unknown access_token =")
            return
        }
        const params = {
            url: "https://open.feishu.cn/open-apis/message/v4/send/",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + access_token
            },
            body: {
                open_id: open_id,
                msg_type: "interactive",
                card: {
                    config: {
                        wide_screen_mode: true
                    },
                    header: {
                        title: {
                            tag: "plain_text",
                            content: "this is header"
                        }
                    },
                    elements: [
                        {
                            tag: "div",
                            text: {
                                tag: "plain_text",
                                content: "This is a very very very very very very very long text;"
                            },
                            fields: [
                                {
                                    is_short: false,
                                    text: {
                                        tag: 'lark_md',
                                        content: "**module:**\n第一行"
                                    }
                                },
                                {
                                    is_short: false,
                                    text: {
                                        tag: 'lark_md',
                                        content: "**module:**\n第二行"
                                    }
                                },
                                {
                                    is_short: false,
                                    text: {
                                        tag: 'lark_md',
                                        content: "**module:**\n第三行"
                                    }
                                },
                            ],
                        },
                    ]
                }
            },
            json: true,
            timeout: 3000,
            method: 'POST',
        };
        return await makeRequest(params);
    }


    genAppAccessToken = async () => {
        const params = {
            url: "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/",
            body: {
                app_id: APP_ID,
                app_secret: APP_SECRET,
            },
            json: true,
            timeout: 3000,
            method: 'POST',
        };
        const rs = await makeRequest(params);
        return rs['tenant_access_token']
    };

    getUserInfo = async (user_id) => {
        const self = this
        const access_token = await self.genAppAccessToken()
        if (access_token == "") {
            console.log("unknown access_token =")
            return
        }
        const params = {
            url: `https://open.feishu.cn/open-apis/contact/v3/users/${user_id}`,
            headers: {
                "Content-Type": "application/json;charset=utf-8",
                "Authorization": "Bearer " + access_token
            },
            qs: {
                user_id_type: 'user_id'
            },
            json: true,
            timeout: 3000,
            method: 'GET',
        };
        console.log(JSON.stringify(params))
        const rs = await makeRequest(params);
        console.log(rs)
        return rs
    }
}

async function makeRequest(params) {
    let times = 3;
    let e;
    do {
        try {
            const result = await request(params);
            console.log(result)
            return result;
        } catch (err) {
            e = err;
        }
    } while (--times);
    if (e) {
        throw e;
    }
}


module.exports = ChatBot;