'use strict';
const co = require('co');
const request = require('request');
const urlencode = require('urlencode');
const crypto = require('crypto');
const APPKEY = 'kfXZGOq8kkNT0bjL';
const APPID = 2154713372

class AIChatService {
    async aiChat(question) {
        const self = this;
        const result = await self.getAiChat(question);

        return result
    }
    getAiChat(question) {
        const self = this
        const body = {
            app_id: APPID,
            time_stamp: parseInt(new Date() / 1000),
            nonce_str: '20000',
            session: `${parseInt(new Date() / 1000)}`,
            question: question,
        }
        const signRs = self.getSign(body)

        // const url = `https://api.ai.qq.com/fcgi-bin/nlp/nlp_textchat?app_id=${body['app_id']}&time_stamp=${body['time_stamp']}&nonce_str=${body['nonce_str']}&session=${body['session']}&question=${body['question']}&sign=${body['sign']}`
        const data = {
            url: `https://api.ai.qq.com/fcgi-bin/nlp/nlp_textchat?${signRs['str']}&sign=${signRs['result']}`,
            method: 'GET',
            "Content-Type": "application/x-www-form-urlencoded",
            json: true,
            // body: `${signRs['str']}$sign=${signRs['result']}`
        }
        console.log(data)
        return new Promise((resolve, reject) => {
            request(data, function (error, response, body) {
                if (!error && body.ret === 0) {
                    resolve(body['data'].answer);
                } else {
                    console.log(body)
                    resolve();
                }
            });
        });
    }

    getSign(params) {
        const res = Object.keys(params).sort();
        let str = ''
        res.forEach(key => {
            if (params[key] !== '') {
                str += `${key}=${urlencode(params[key])}&`
            }
        });
        str += `app_key=${APPKEY}`
        const md5 = crypto.createHash('md5');
        const result = md5.update(str).digest('hex').toUpperCase();
        return { result, str }
    }

    getRandom() {
        const chars = '1234567890qwertyuiopasdfghjklzxcvbnm'
        const string_length = 8;
        let randomstring = '';
        for (let x = 0; x < string_length; x++) {
            const letterOrNumber = Math.floor(Math.random() * 2);
            if (letterOrNumber == 0) {
                const newNum = Math.floor(Math.random() * 9);
                randomstring += newNum;
            } else {
                const rnum = Math.floor(Math.random() * chars.length);
                randomstring += chars.substring(rnum, rnum + 1);
            }
        }
        return randomstring
    }
}



module.exports.AIChatService = new AIChatService();
