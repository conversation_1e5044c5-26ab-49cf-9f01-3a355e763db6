const mysqlInstance = require('../models/mysql').mysqlInstance;
const schedule = require('node-schedule');

async function timingContent() {
  let num = 0;
  const time = parseInt(Math.round(new Date().getTime() / 1000));
  let offset = 0;
  const limit = 100;

  while (true) {
    const timingContentRs = await mysqlInstance.getTimingContent(time, offset, limit);
    if (!timingContentRs || timingContentRs.length === 0) {
      break;
    }
    for (let i = 0; i < timingContentRs.length; i++) {
      const content = timingContentRs[i];
      const userid = content.userid;
      const contentid = content.id;
      await mysqlInstance.postContentHideV2(userid, contentid, 0);
      if (content.atUserIds) {
        const atUserIds = content.atUserIds.split(',').map((id) => parseInt(id));
        if (atUserIds.length > 0) {
          const userInfo = await mysqlInstance.getUserInfoByUserid(userid);
          const content_ = content.text ? content.text.slice(0, 50) : '';
          let image = '';
          if (content.images) {
            const imagesArr = content.images.split(',');
            image = imagesArr[0];
          }
          for (const toUserid of atUserIds) {
            if (toUserid === userid) {
              continue;
            }
            const notificationData = {
              userid: toUserid,
              text: ' 提及了你',
              avatar: userInfo[0]['avatar'],
              nickname: userInfo[0]['nickname'],
              type: 7,
              contentid: contentid,
              content: content_,
              images: image,
            };
            await mysqlInstance.insertIntoNotification(notificationData);
          }
        }
      }
    }
    offset += limit;
    num += timingContentRs.length;
  }
  slogger.info(`${num}条定时动态发布成功`);
}

schedule.scheduleJob('1 * * * *', timingContent);

timingContent();
