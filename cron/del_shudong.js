const mysqlInstance = require('../models/mysql').mysqlInstance;
const schedule = require('node-schedule');

async function deleteOldShudong() {
  let num = 0;
  const time = parseInt(Math.round(new Date().getTime() / 1000)) - 60 * 60 * 24 * 30;
  let offset = 0;
  const limit = 100;

  while (true) {
    const shudongRs = await mysqlInstance.getAllShudong(time, offset, limit);
    if (!shudongRs || shudongRs.length === 0) {
      break;
    }
    for (let i = 0; i < shudongRs.length; i++) {
      const shudongid = shudongRs[i].id;
      await mysqlInstance.delShudong(shudongid);
    }
    offset += limit;
    num += shudongRs.length;
  }
  slogger.info(`30天前的${num}条树洞删除数据成功`);
}

schedule.scheduleJob('0 1 * * *', deleteOldShudong);

deleteOldShudong();
