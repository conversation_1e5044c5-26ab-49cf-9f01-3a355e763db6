'use strict';

const Koa = require('koa');
const gzip = require('koa-gzip');
const log4js = require('log4js');
const cluster = require('cluster');
const bodyParser = require('koa-bodyparser');
const cors = require('koa-cors');
const os = require('os');

global.USERSIG = null;

const logConfig = {
  appenders: {
    siteLogs: { type: 'file', filename: 'logs/site.log' },
    console: { type: 'console' },
  },
  categories: {
    site: { appenders: ['console', 'siteLogs'], level: 'error' },
    default: { appenders: ['console'], level: 'trace' },
  },
  pm2: true,
  pm2InstanceVar: 'INSTANCE_ID',
};

log4js.configure(logConfig);
global.elogger = log4js.getLogger('site');
global.slogger = log4js.getLogger('');

const routers = {
  user: require('./routes/userRouter'),
  common: require('./routes/commonRouter'),
  attention: require('./routes/attentionRouter'),
  rate: require('./routes/rateRouter'),
  report: require('./routes/reportRouter'),
  content: require('./routes/contentRouter'),
  test: require('./routes/test'),
  script: require('./routes/script'),
  php: require('./routes/phpRouter'),
  island: require('./routes/islandRouter'),
  recomment: require('./routes/recommenRouter'),
  ocean: require('./routes/oceanRouter'),
  map: require('./routes/mapRouter'),
  im: require('./routes/imRouter'),
  bot: require('./routes/botRouter'),
  feed: require('./routes/feedRouter'),
  pay: require('./routes/payRouter'),
  shudong: require('./routes/shudong'),
  operations: require('./routes/operationsRouter'),
  adminReport: require('./routes/adminReportRouter'),
};

class App {
  constructor() {
    this.app = new Koa();
    this.init();
  }

  init() {
    const sigService = require('./services/sigService').sigInstance;
    sigService.start();

    this.app.use(function* (next) {
      if (this.request.url === '/') {
        this.body = '52HZ';
        return;
      }
      yield next;
    });

    this.app.use(bodyParser());
    this.app.use(gzip());
    this.app.use(cors());

    // this.app.use(require('./middlewares/botMidWare'));
    this.app.use(require('./middlewares/loginMidWare'));

    this.registerRoutes();
  }

  registerRoutes() {
    Object.values(routers).forEach((router) => {
      Object.values(router).forEach((middleware) => {
        this.app.use(middleware);
      });
    });
  }

  start() {
    if (cluster.isMaster) {
      const numCPUs = os.cpus().length;
      for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
      }

      cluster.on('listening', (worker, address) => {
        const host = address?.address || '0.0.0.0';
        const port = address?.port || 3690;
        slogger.info(`Worker ${worker.id} listening on ${host}:${port}`);
      });

      cluster.on('exit', (worker, code, signal) => {
        slogger.warn(`Worker ${worker.id} died, restarting...`);
        cluster.fork();
      });
    } else {
      const port = process.env.PORT || 3690;
      const server = this.app.listen(port, '0.0.0.0', () => {
        slogger.info(`Server listening on port ${port}`);
      });
    }
  }
}

const app = new App();
app.start();

module.exports = app.app;
