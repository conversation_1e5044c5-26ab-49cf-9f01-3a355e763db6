'use strict';
const router = require('koa-route');
const redisInstance = require('../models/redis').redisInstance;
const mapController = require('../controller/map_controller').mapInstance;

const getSocketNamespace = router.get('/n/socket/namespace', function* getSocketNamespace() {
  try {
    const userid = this.request._uid;
    const redisRS = yield redisInstance.hashGet('SOCKET:NAMESPACE:ALL');
    for (const key in redisRS) {
      let value = JSON.parse(redisRS[key]);
      if (value.length < 200) {
        value.push(userid);
        value = arrayNonRepeatfy(value);
        yield redisInstance.hashAdd('SOCKET:NAMESPACE:ALL', [`${key}`, JSON.stringify(value)]);
        this.body = { code: 0, msg: 'success', data: `/${key}` };
        return;
      }
    }
    this.body = { code: 1, msg: 'falie', data: 'map is not enough' };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getSocketFoodStatistics = router.get('/n/socket/weight/statistics', function* getSocketFoodStatistics() {
  try {
    const userid = this.request._uid;
    const rs = yield mapController.getFoodStatistics(userid);

    return (this.body = { code: 0, msg: 'success', data: rs });
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getSocketNamespace: getSocketNamespace,
  getSocketFoodStatistics: getSocketFoodStatistics,
};

function compare(property) {
  return function (a, b) {
    var value1 = a[property];
    var value2 = b[property];
    return value2 - value1;
  };
}

function arrayNonRepeatfy(arr) {
  const map = new Map();
  const array = new Array();
  for (let i = 0; i < arr.length; i++) {
    if (map.has(arr[i])) {
      map.set(arr[i], true);
    } else {
      map.set(arr[i], false);
      array.push(arr[i]);
    }
  }
  return array;
}
