'use strict';
const router = require('koa-route');
const reportController = require('../controller/report_controller').reportInstance;
const IMService = require('../services/IM_service').IMInstance;
const { DefaultConfig } = require('../config/default');
const REPORTTYPE = DefaultConfig.reportType;

const postReport = router.post('/n/report', function* postReport() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const reported_userid = body.toUserid;
    const report_type = body.report_type;
    const type = body.type || 0; // 1: 动态 2: 用户 3: 声波 4: 留言 5: 碎碎念
    const rid = body.rid || 0;
    const detail = body.detail;
    if (!reported_userid) {
      this.body = { code: 1, msg: 'toUserid不得为空', data: [] };
      return;
    }
    if (!report_type) {
      this.body = { code: 1, msg: 'report_type不得为空', data: [] };
      return;
    }
    if (userid == parseInt(reported_userid)) {
      this.body = { code: 1, msg: '无法举报自己', data: [] };
      return;
    }

    const userrs = yield reportController.getUserAndReporter(userid, reported_userid);

    const msg = `用户 ${userid}: ${userrs['nickname']} 举报用户 ${reported_userid}: ${userrs['reportName']} 举报类型 ${
      REPORTTYPE[parseInt(report_type)]
    }`;

    const rs = yield reportController.getReport(userid, reported_userid, type, rid);
    if (!rs) {
      return (this.body = { code: 0, msg: 'success', data: [] });
    }
    const data = {
      userid,
      reported_userid,
      report_type,
      type,
      rid,
      detail,
    };
    const result = yield reportController.postReport(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  postReport: postReport,
};
