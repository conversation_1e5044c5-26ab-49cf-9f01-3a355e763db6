'use strict';

const router = require('koa-route');
const attentionController = require('../controller/attention_controller').attentionInstance;

//添加关注
let addAttention = router.post('/n/attention/add', function* addAttention() {
  try {
    let body = this.request.body;
    let userid1 = this.request._uid;
    let userid2 = parseInt(body.attentionid);
    // if (userid1 !== 7) {
    //
    //
    //  return this.body = { 'code': 1, 'msg': '正在调试关注功能，请稍等', 'data': [] }
    // }
    if (!userid2) {
      return (this.body = { code: 1, msg: '请上传attentionid', data: [] });
    }
    if (userid1 == userid2) {
      return (this.body = { code: 1, msg: '无法关注自己', data: [] });
    }
    let data = {
      userid1: userid1,
      userid2: userid2,
    };
    let result = yield attentionController.addAttention(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
      yield attentionController.baseVaryHertz(userid1, userid2);
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//取消关注
let releaseAttention = router.post('/n/attention/release', function* releaseAttention() {
  try {
    let body = this.request.body;
    let userid1 = this.request._uid;
    let userid2 = body.attentionid;
    // if (userid1 !== 7) {
    //
    //
    //  return this.body = { 'code': 1, 'msg': '正在调试关注功能，请稍等', 'data': [] }
    // }

    if (!userid2) {
      return (this.body = { code: 1, msg: '请上传attentionid', data: [] });
    }
    if (userid1 == userid2) {
      return (this.body = { code: 1, msg: '无法取关自己', data: [] });
    }
    let data = {
      userid1: userid1,
      userid2: userid2,
    };
    let result = yield attentionController.releaseAttention(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
//获取粉丝
let getFans = router.get('/n/fans', function* getFans() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const { lastId } = query;
    let result = yield attentionController.getFans(userid, lastId);

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'], count: result['count'], lastId: result['lastId'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取关注
let getAttention = router.get('/n/attention', function* getAttention() {
  try {
    let userid = this.request._uid;
    const query = this.request.query;
    const { lastId } = query;

    let result = yield attentionController.getAttention(userid, lastId);

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'], count: result['count'], lastId: result['lastId'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getFriends = router.get('/n/friends', function* getFriends() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const { page, keyword } = query;
    const result = yield attentionController.getFriends(userid, parseInt(page), keyword);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'], count: result['count'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取粉丝
let newGetFans = router.get('/n/fans/new', function* newGetFans() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 10;
    const data = {
      size: size,
      page: page,
      userid: userid,
    };
    let result = yield attentionController.newGetFans(data);
    const data_ = {
      count: `${result['count']}`,
      size: size,
      page: page,
      data: result['data'],
    };
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [data_] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取关注
let newGetAttention = router.get('/n/attention/new', function* newGetAttention() {
  try {
    let userid = this.request._uid;
    const query = this.request.query;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 10;
    const data = {
      size: size,
      page: page,
      userid: userid,
    };
    let result = yield attentionController.newGetAttention(data);
    const data_ = {
      count: `${result['count']}`,
      size: size,
      page: page,
      data: result['data'],
    };
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [data_] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//关注主题
const addTagAttention = router.post('/n/attention/tag', function* addTagAttention() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const tagid = body.tagid;
    if (!tagid) {
      return (this.body = { code: 1, msg: '请上传tagid', data: [] });
    }
    const result = yield attentionController.addTagAttention(userid, tagid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//取关主题
const releaseTagAttention = router.post('/n/attention/tag/release', function* releaseTagAttention() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const tagid = body.tagid;
    if (!tagid) {
      return (this.body = { code: 1, msg: '请上传tagid', data: [] });
    }
    const result = yield attentionController.releaseTagAttention(userid, tagid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取关注的主题列表
const getTagAttention = router.get('/n/attention/tag/', function* getTagAttention() {
  try {
    const userid = this.request._uid;
    const result = yield attentionController.getTagAttention(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取我关注主页内容
const getAttentionUserContent = router.get('/n/affair/followed/list', function* getAttentionUserContent() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 20;
    const data = {
      userid: userid,
      page: page,
      size: size,
    };
    const result = yield attentionController.getAttentionUserContent(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  addAttention: addAttention,
  releaseAttention: releaseAttention,
  getFans: getFans,
  getAttention: getAttention,
  newGetAttention: newGetAttention,
  newGetFans: newGetFans,
  addTagAttention: addTagAttention,
  releaseTagAttention: releaseTagAttention,
  getTagAttention: getTagAttention,
  // getAttentionUserContent: getAttentionUserContent
  getFriends: getFriends,
};
