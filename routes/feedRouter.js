'use strict';
const router = require('koa-route');
const feedController = require('../controller/feed_controller').feedInstance;

const foodGet = router.get('/n/food', function* foodGet() {
  try {
    const query = this.request.query;
    const type = parseInt(query.type) || 1;
    const result = yield feedController.getAllFood(type);

    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const feed = router.post('/n/feed', function* feed() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const toUserid = body.toUserid;
    const food_id = body.food_id;
    if (!toUserid) {
      this.body = { code: 1, msg: '请上传toUserid', data: [] };
      return;
    }

    if (!food_id) {
      this.body = { code: 1, msg: '请上传food_id', data: [] };
      return;
    }

    if (parseInt(toUserid) === userid) {
      this.body = { code: 1, msg: '不能投喂自己', data: [] };
      return;
    }

    const result = yield feedController.feed(userid, toUserid, food_id);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const userWeight = router.get('/n/user/weight', function* userWeight() {
  try {
    const uid = this.request._uid;
    const query = this.request.query;
    const userid = query.userid || uid;
    const result = yield feedController.getUserWeight(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ weight: result['userWeight'] }] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  foodGet: foodGet,
  feed: feed,
  userWeight: userWeight,
};
