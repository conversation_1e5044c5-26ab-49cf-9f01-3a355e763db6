'use strict';

const router = require('koa-route');
const IMService = require('../services/IM_service').IMInstance;
const AIChatService = require('../services/aichat').AIChatService;
const botController = require('../controller/bot_controller').botInstance;

const sendMsg = router.put('/n/im/sendmsg', function* sendMsg() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const toUserid = body.toUserid;
    const msg = body.msg;
    if (!toUserid) {
      this.body = { code: 1, msg: '消息传达用户不得为空' };
      return;
    }
    if (!msg) {
      this.body = { code: 1, msg: '内容不得为空' };
      return;
    }

    const data = {
      userid: userid,
      toUserid: toUserid,
      msg: msg,
      from: 5,
    };
    const result = IMService.sendMsg(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, contentid: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const sendBulkMsg = router.put('/n/im/sendBulkmsg', function* sendBulkMsg() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const msg = `hi，第xxxx只小鲸鱼，我是zoe\n这是一份《52hz使用指南\n\n指南：52hz的底层分发和推荐是基于NLP（自然语义理解）在app里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
    const result = yield IMService.sendBulk(msg, ['7']);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'SUCCESS' };
    } else {
      this.body = { code: 1, msg: result['msg'] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
// {
//     "MsgBody": [{
//         "MsgType": "TIMTextElem",
//         "MsgContent": { "Text": "哈哈张柏芝" }
//     }],
//     "CallbackCommand": "C2C.CallbackAfterSendMsg",
//     "From_Account": "7",
//     "To_Account": "108474",
//     "MsgRandom": **********,
//     "MsgSeq": 21708,
//     "MsgTime": **********,
//     "MsgKey": "21708_**********_**********"
// }
const imBack = router.post('/n/im/black', function* imBack() {
  try {
    const body = this.request.body;
    const CallbackCommand = body.CallbackCommand;
    if (CallbackCommand === 'C2C.CallbackAfterSendMsg') {
      const From_Account = parseInt(body.From_Account);
      const To_Account = parseInt(body.To_Account);
      if (To_Account === 108474) {
        let msg = '我现在还很笨，还不会说话';
        if (From_Account === 7) {
          const question = body.MsgBody[0].MsgContent.Text;
          if (question === '@52botfather') {
            msg = '我可以帮助您创建和管理 52HZ 机器人。\n您可以通过发送以下命令来控制我:\n/newbot 创建一个新的机器人';
          } else if (question.startsWith('@callback')) {
            const result = yield botController.botTest();
            if (result && result['backSuccess']) {
              msg = '机器人成功啦';
            } else {
              msg = '机器人失败了';
            }
          } else {
            msg = yield AIChatService.aiChat(question);
          }
        }
        yield IMService.sendBulk(msg, [`${From_Account}`]);
      }
    }
    return;
  } catch (err) {
    console.log(err);
    return;
  }
});

module.exports = {
  sendMsg: sendMsg,
  sendBulkMsg: sendBulkMsg,
  imBack: imBack,
};
