'use strict';
const router = require('koa-route');
const islandController = require('../controller/island_controller').islandInstance;
const redisInstance = require('../models/redis').redisInstance;
const imService = require('../services/IM_service').IMInstance;
const youmengService = require('../services/youmeng').YMInstance;

const getWaters = router.get('/n/island/waters', function* getWaters() {
  try {
    const result = yield islandController.getWaters();
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//发布海域内容
const postDiscuss = router.post('/n/island/discuss', function* postDiscuss() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const island_type = parseInt(body.island_type);
    const voice_type = body.voice_type;
    const title = body.title;
    const content = body.content;
    if (!island_type) {
      return (this.body = { code: 1, msg: 'island_type不得为空', data: [] });
    }
    if (!voice_type) {
      return (this.body = { code: 1, msg: 'voice_type不得为空', data: [] });
    }
    if (!title) {
      return (this.body = { code: 1, msg: 'title不得为空', data: [] });
    }
    const data = {
      userid: userid,
      island_type: island_type,
      voice_type: parseInt(voice_type),
      title: title,
      content: content || '',
    };
    const result = yield islandController.postDiscuss(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const delIslandContent = router.delete('/n/island/content', function* delIslandContent() {
  try {
    const query = this.request.query;
    const discussId = query.discussId;
    const userid = this.request._uid;
    if (!discussId) {
      return (this.body = { code: 1, msg: 'discussId不得为空', data: [] });
    }
    const data = {
      discussId: parseInt(discussId),
      userid: userid,
    };

    const result = yield islandController.delIslandContent(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取海域内容
const getDiscussByWater = router.get('/n/island/alldiscuss', function* getDiscussByWater() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const island_type = query.island_type;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 20;
    const sort = query.sort || 1; // 1: 最新 2: 最热

    if (!island_type) {
      return (this.body = { code: 1, msg: 'type不得为空', data: [] });
    }
    const data = {
      island_type: parseInt(island_type),
      page: page,
      size: size,
      sort: parseInt(sort),
      userid,
    };

    const result = yield islandController.getDiscussByWaterNew(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取声波详情
const getDiscussInfo = router.get('/n/island/discuss', function* getDiscussInfo() {
  try {
    const query = this.request.query;
    const discussId = query.discussId;
    if (!discussId) {
      return (this.body = { code: 1, msg: 'discussId不得为空', data: [] });
    }
    const result = yield islandController.getDiscussInfo(discussId);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//回复
const putIslandContentComment = router.post('/n/island/comment', function* putIslandContentComment() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const authorid = body.authorid;
    const discussId = body.discussId;
    const comment = body.comment;
    const to_userid = body.to_userid;
    const belongs = body.belongs;
    const to_commentid = body.to_commentid;
    const type = body.type; //回复类型 1:图文 2:语音
    const images = body.images;
    const voice = body.voice;
    const voice_time = body.voice_time;
    if (!type) {
      this.body = { code: 1, msg: '请上传type', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!discussId) {
      this.body = { code: 1, msg: '请上传discussId', data: [] };
      return;
    }

    if (parseInt(type) == 1 && !images && !comment) {
      this.body = { code: 1, msg: '请上传comment或images', data: [] };
      return;
    }

    if (parseInt(type) == 2 && !voice) {
      this.body = { code: 1, msg: '请上传voice', data: [] };
      return;
    }

    if (parseInt(type) == 2 && !voice_time) {
      this.body = { code: 1, msg: '请上传voice_time', data: [] };
      return;
    }

    if (!to_userid) {
      this.body = { code: 1, msg: '请上传to_userid', data: [] };
      return;
    }
    if (!belongs) {
      this.body = { code: 1, msg: '请上传belongs', data: [] };
      return;
    }
    if (!to_commentid) {
      this.body = { code: 1, msg: '请上传to_commentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      authorid: parseInt(authorid),
      discussId: parseInt(discussId),
      comment: comment,
      to_userid: parseInt(to_userid),
      time: parseInt(Math.round(new Date().getTime() / 1000)),
      belongs: parseInt(belongs),
      to_commentid: parseInt(to_commentid),
      type: parseInt(type),
      images: images,
      voice: voice,
      voice_time: parseInt(voice_time) || 0,
    };
    const result = yield islandController.putIslandContentComment(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ commentid: result['data'] }] };
      yield islandController.baseVaryHertz(userid, authorid);
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    console.log(`11111::::${err}`);
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//删除回复
const delIslandComment = router.del('/n/island/comment', function* delIslandComment() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const discussId = query.discussId;
    const commentid = query.commentid;
    const authorid = query.authorid;
    if (!discussId) {
      this.body = { code: 1, msg: '请上传discussId', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!commentid) {
      this.body = { code: 1, msg: '请上传commentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      commentid: parseInt(commentid),
      discussId: parseInt(discussId),
      authorid: parseInt(authorid),
    };
    const result = yield islandController.delIslandComment(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getIslandContentComment = router.get('/n/island/comment', function* getIslandContentComment() {
  try {
    const uid = this.request._uid;
    const query = this.request.query;
    const discussId = query.discussId;
    const authorid = query.authorid;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 20;
    if (!discussId) {
      this.body = { code: 1, msg: '请上传discussId', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    const data = {
      discussId: parseInt(discussId),
      authorid: parseInt(authorid),
      uid: uid,
      size: size,
      page: page,
    };
    const result = yield islandController.getIslandContentComment(data);
    const data_ = {
      count: `${result['totalCount']}`,
      mainCount: `${result['count']}`,
      size: size,
      page: page,
      data: result['data'],
    };
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [data_] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//点赞
const postIslandLike = router.post('/n/island/like', function* postIslandLike() {
  try {
    const body = this.request.body;
    const discussId = body.discussId;
    const commentid = body.commentid;
    const userid = this.request._uid;
    if (!discussId) {
      this.body = { code: 1, msg: '请上传discussId', data: [] };
      return;
    }
    if (!commentid) {
      this.body = { code: 1, msg: '请上传commentid', data: [] };
      return;
    }
    const data = {
      userid: userid,
      discussId: discussId,
      commentid: commentid,
    };
    const result = yield islandController.postIslandLike(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
      // const authorid = result['anthorId'];
      // yield islandController.baseVaryHertz(userid, authorid);
      // if (parseInt(userid) != parseInt(authorid)) {
      //     const key__ = 'HASH:USER:INFO:' + authorid;
      //     const redisRS__ = yield redisInstance.hashGet(key__);
      //     const nickName = redisRS__['nickname'];
      //     const data = {
      //         userid: authorid,
      //         text: `${nickName}  喜欢了你的声波回复`,
      //         from: 4,
      //         nickname: nickName
      //     }
      //     const sendMsgRs = imService.sendMsg(data);
      //     if (redisRS__['deviceToken']) {
      //         const key2__ = `STR:USER:PUSH:COUNT:${authorid}`
      //         const redisRS2__ = yield redisInstance.get(key2__);
      //         const pushCount = redisRS2__;

      //         const body__ = {
      //             text: `${nickName} 喜欢了你的声波回复`,
      //             device_token: redisRS__['deviceToken'],
      //             pushCount: pushCount,
      //             to_userid: authorid
      //         }
      //         const msgPush = youmengService.msgPush(body__)
      //     }
      // }
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getWaters: getWaters,
  getDiscussByWater: getDiscussByWater,
  postDiscuss: postDiscuss,
  getDiscussInfo: getDiscussInfo,
  putIslandContentComment: putIslandContentComment,
  delIslandComment: delIslandComment,
  getIslandContentComment: getIslandContentComment,
  postIslandLike: postIslandLike,
  delIslandContent: delIslandContent,
};
