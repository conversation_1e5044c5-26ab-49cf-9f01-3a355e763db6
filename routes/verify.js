'use strict';
const router = require('koa-route');
const verifyController = require('../controller/verify_controller').verifyInstance;
const moment = require('moment');

const getVerify = router.get('/n/user/verify', function* foodGet() {
  try {
    const uid = this.request._uid;

    const result = yield verifyController.iosPay(uid);

    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const incomeStatistics = router.get('/n/income/statistics', function* incomeStatistics() {
  try {
    const uid = this.request._uid;
    const query = this.request.query;
    let { startTime, endTime } = query;
    if (uid !== 7) {
      this.body = { code: 1, msg: 'faile', data: [] };
      return;
    }
    startTime = startTime || moment().startOf('month').format('X');
    endTime = endTime || parseInt(new Date() / 1000);
    const result = yield payController.incomeStatistics(startTime, endTime);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getVerify: getVerify,
  incomeStatistics: incomeStatistics,
};
