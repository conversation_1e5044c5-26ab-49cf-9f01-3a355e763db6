'use strict';
const router = require('koa-route');
const operationsController = require('../controller/operations_controller').operationsInstance;

const addOperations = router.post('/n/operations', function* addOperations() {
  try {
    const body = this.request.body;
    const text = body.text;
    const images = body.images;
    const title = body.title;

    if (!text && !images) {
      this.body = { code: 1, msg: '内容不得为空', data: [] };
      return;
    }

    const result = yield operationsController.addOperations(text, images, title);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getOperations = router.get('/n/operations', function* getOperations() {
  try {
    const query = this.request.query;
    const page = query.page || 1;
    const size = query.size || 20;
    const result = yield operationsController.getOperations(page, size);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getOperationsDetail = router.get('/n/operations/detail', function* getOperationsDetail() {
  try {
    const query = this.request.query;
    const id = query.id;
    const result = yield operationsController.getOperationsDetail(id);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  addOperations: addOperations,
  getOperations: getOperations,
  getOperationsDetail: getOperationsDetail,
};
