'use strict';
const router = require('koa-route');
const payController = require('../controller/pay_controller').payInstance;
const moment = require('moment');

const iosPay = router.post('/n/ios/pay', function* foodGet() {
  try {
    const uid = this.request._uid;
    const body = this.request.body;
    const receiptData = body.receiptData;
    const product_id = body.product_id;
    if (!receiptData) {
      this.body = { code: 1, msg: 'receiptData不能为空', data: [] };
      return;
    }

    if (!product_id) {
      this.body = { code: 1, msg: 'product_id不能为空', data: [] };
      return;
    }

    const result = yield payController.iosPay(receiptData, uid, product_id);

    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const incomeStatistics = router.get('/n/income/statistics', function* incomeStatistics() {
  try {
    const uid = this.request._uid;
    const query = this.request.query;
    let { startTime, endTime } = query;
    if (uid !== 7) {
      this.body = { code: 1, msg: 'faile', data: [] };
      return;
    }
    startTime = startTime || moment().startOf('month').format('X');
    endTime = endTime || parseInt(new Date() / 1000);
    const result = yield payController.incomeStatistics(startTime, endTime);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  iosPay: iosPay,
  incomeStatistics: incomeStatistics,
};
