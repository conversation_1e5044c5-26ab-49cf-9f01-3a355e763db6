'use strict';

const router = require('koa-route');
const contentController = require('../controller/content_controller').contentInstance;
const imService = require('../services/IM_service').IMInstance;
const redisInstance = require('../models/redis').redisInstance;
const youmengService = require('../services/youmeng').YMInstance;
const { DefaultConfig } = require('../config/default');

const LINKSHARE = DefaultConfig.LINKSHARE;
const LINKSHAREBODY = DefaultConfig.LINKSHAREBODY;
const LINKSHAREICON = DefaultConfig.LINKSHAREICON;

//获取动态标签类型
const getContentTag = router.get('/n/content/tag', function* getContentTag() {
  try {
    const result = yield contentController.getContentTag();
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//发布新动态
const putContent = router.post('/n/content', function* putContent() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const text = body.text;
    const images = body.images;
    const video = body.video;
    const imageType = body.imageType;
    const lat = body.lat || 0;
    const lon = body.lon || 0;
    const location = body.location || '';
    const tag = body.tag || 0;
    const atUserIds = body.atUserIds;
    const timing = body.timing;

    if (!text && !images) {
      this.body = { code: 1, msg: '内容不得为空', data: [] };
      return;
    }

    const data = {
      userid: userid,
      text: text || '',
      images: images || '',
      video: video || '',
      imageType: imageType || '',
      tag: parseInt(tag),
      lat: lat,
      lon: lon,
      location: location,
      atUserIds: atUserIds,
      timing: timing,
    };
    const result = yield contentController.putContent(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//音乐url解析
const getMusicInfo = router.get('/n/content/musicinfo/sheqi', function* getMusicInfo() {
  try {
    const query = this.request.query;
    const musicText = query.musicText;

    if (!musicText) {
      this.body = { code: 1, msg: '音乐链接不得为空', data: [] };
      return;
    }

    if (musicText.indexOf('网易云音乐') == -1 && musicText.indexOf('QQ音乐') == -1) {
      this.body = { code: 1, msg: '暂只支持网易云音乐和QQ音乐链接', data: [] };
      return;
    }
    let result = null;
    if (musicText.indexOf('QQ音乐') > 0) {
      result = yield contentController.getQQMusicInfo(musicText);
    }

    if (musicText.indexOf('网易云音乐') > 0) {
      result = yield contentController.getWYMusicInfo(musicText);
    }

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getMusicInfoNew = router.get('/n/content/musicinfo', function* getMusicInfo() {
  try {
    const query = this.request.query;
    const musicText = query.musicText;

    if (!musicText) {
      this.body = { code: 1, msg: '音乐链接不得为空', data: [] };
      return;
    }

    let type = 1;

    for (let i = 0; i < LINKSHARE.length; i++) {
      if (musicText.indexOf(LINKSHARE[i]) >= 0) {
        type = 2;
      }
    }

    let result = {
      msg: '解析失败',
    };

    if (type === 2) {
      let from = null;
      for (const key in LINKSHAREBODY) {
        if (musicText.indexOf(key) !== -1) {
          from = LINKSHAREBODY[key];
        }
      }
      if (from) {
        let icon = null;
        for (const key in LINKSHAREICON) {
          if (musicText.indexOf(key) !== -1) {
            icon = LINKSHAREICON[key];
          }
        }
        result = {
          backSuccess: true,
          data: {
            type: 2,
            from: from,
            icon: icon,
          },
        };
      } else {
        let from = 0;
        if (musicText.indexOf('QQ音乐') > 0 || musicText.indexOf('.qq.com') > 0) {
          from = 3;
        }

        if (musicText.indexOf('网易云音乐') > 0 || musicText.indexOf('.163.com') > 0) {
          from = 1;
        }

        if (musicText.indexOf('虾米音乐') > 0 || musicText.indexOf('.xiami.com') > 0) {
          from = 2;
        }

        result = yield contentController.getMusicInfo(musicText, from);
      }
    } else {
      let from = 0;
      if (musicText.indexOf('QQ音乐') > 0 || musicText.indexOf('.qq.com') > 0) {
        from = 3;
      }

      if (musicText.indexOf('网易云音乐') > 0 || musicText.indexOf('.163.com') > 0) {
        from = 1;
      }

      if (musicText.indexOf('虾米音乐') > 0 || musicText.indexOf('.xiami.com') > 0) {
        from = 2;
      }

      result = yield contentController.getMusicInfo(musicText, from);
    }

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//发布音乐动态
const putMusicContent = router.post('/n/content/music/sheqi', function* putMusicContent() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const text = body.text;
    const images = body.images;
    const imageType = body.imageType;
    const musicText = body.musicText;
    const lat = body.lat || 0;
    const lon = body.lon || 0;
    const location = body.location || '';
    const tag = body.tag || 0;

    if (!musicText) {
      this.body = { code: 1, msg: '音乐链接不得为空', data: [] };
      return;
    }
    if (musicText.indexOf('网易云音乐') == -1 && musicText.indexOf('QQ音乐') == -1) {
      this.body = { code: 1, msg: '暂只支持网易云音乐和QQ音乐链接', data: [] };
      return;
    }

    const data = {
      userid: userid,
      text: text || '',
      images: images || '',
      imageType: imageType || '',
      musicText: musicText,
      tag: parseInt(tag),
      lat: lat,
      lon: lon,
      location: location,
    };

    let result = null;
    if (musicText.indexOf('QQ音乐') > 0) {
      result = yield contentController.putQQMusicContent(data);
    }

    if (musicText.indexOf('网易云音乐') > 0) {
      result = yield contentController.putWYMusicContent(data);
    }
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//发布音乐动态
const putMusicContentNew = router.post('/n/content/music', function* putMusicContentNew() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const text = body.text;
    const images = body.images;
    const imageType = body.imageType;
    const musicText = body.musicText;
    const lat = body.lat || 0;
    const lon = body.lon || 0;
    const location = body.location || '';
    const tag = body.tag || 0;
    const atUserIds = body.atUserIds;
    const timing = body.timing;

    if (!musicText) {
      this.body = { code: 1, msg: '音乐链接不得为空', data: [] };
      return;
    }
    // if((musicText.indexOf("网易云音乐") == -1) && (musicText.indexOf("QQ音乐") == -1)){
    //     this.body = { 'code': 1, 'msg': '暂只支持网易云音乐和QQ音乐链接', 'data': [] };
    //     return;
    // }

    let type = 1;

    for (let i = 0; i < LINKSHARE.length; i++) {
      if (musicText.indexOf(LINKSHARE[i]) >= 0) {
        type = 2;
      }
    }

    let result = {
      msg: '解析失败',
    };

    if (type === 2) {
      const data = {
        userid: userid,
        text: text || '',
        images: images || '',
        imageType: imageType || '',
        shareText: musicText,
        tag: parseInt(tag),
        lat: lat,
        lon: lon,
        location: location,
        from: 1,
        atUserIds: atUserIds,
        timing: timing,
      };
      result = yield contentController.putLinkShareContent(data);
    } else {
      const data = {
        userid: userid,
        text: text || '',
        images: images || '',
        imageType: imageType || '',
        musicText: musicText,
        tag: parseInt(tag),
        lat: lat,
        lon: lon,
        location: location,
        from: 1,
        atUserIds: atUserIds,
        timing: timing,
      };
      result = yield contentController.putMusicContentNew(data);
    }

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

// 发布链接动态
const putLinkShareContentNew = router.post('/n/content/link/share', function* putMusicContentNew() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const text = body.text;
    const images = body.images;
    const imageType = body.imageType;
    const shareText = body.shareText;
    const lat = body.lat || 0;
    const lon = body.lon || 0;
    const location = body.location || '';
    const tag = body.tag || 0;

    if (!shareText) {
      this.body = { code: 1, msg: '分享链接不得为空', data: [] };
      return;
    }

    const data = {
      userid: userid,
      text: text || '',
      images: images || '',
      imageType: imageType || '',
      shareText: shareText,
      tag: parseInt(tag),
      lat: lat,
      lon: lon,
      location: location,
      from: 1,
    };

    let result = {
      msg: '解析失败',
    };

    result = yield contentController.putLinkShareContent(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//标签动态列表
const getContentByTag = router.get('/n/content/by/tag', function* getContentByTag() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const tagid = query.tagid;
    const lastId = query.lastId;
    if (!tagid) {
      this.body = { code: 1, msg: '请上传tagid', data: [] };
      return;
    }
    const data = {
      tagid: parseInt(tagid),
      userid: userid,
      lastId: parseInt(lastId),
    };

    const result = yield contentController.getContentByTagNew(data);

    if (result && result['backSuccess']) {
      const data = {
        count: result['count'],
        lastId: result['lastId'],
        data: result['data'],
      };

      this.body = { code: 0, msg: 'success', data: [data] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//删除动态
const delContent = router.post('/n/content/del', function* delContent() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const contentid = body.contentid;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      contentid: parseInt(contentid),
    };
    const result = yield contentController.delContent(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//点赞
const putContentLike = router.post('/n/content/like', function* contentLike() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const authorid = body.authorid;
    const contentid = body.contentid;
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      authorid: parseInt(authorid),
      contentid: parseInt(contentid),
    };
    const result = yield contentController.putContentLike(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
      yield contentController.baseVaryHertz(userid, authorid);
      const status = result['status'];
      // if (status == 0 && parseInt(userid) != parseInt(authorid)) {
      //     const nickName = result['nickName'];
      //     const data = {
      //         userid: authorid,
      //         text: `${nickName} 赞了你的动态`,
      //         from: 4,
      //         nickname: nickName
      //     }
      //     const sendMsgRs = imService.sendMsg(data)
      //     const key__ = 'HASH:USER:INFO:' + authorid
      //     const redisRS__ = yield redisInstance.hashGet(key__);
      //     const deviceToken = redisRS__['deviceToken'];

      //     const key2__ = `STR:USER:PUSH:COUNT:${authorid}`
      //     const redisRS2__ = yield redisInstance.get(key2__);
      //     const pushCount = redisRS2__;

      //     const body__ = {
      //         text: `${nickName} 赞了你的动态`,
      //         device_token: deviceToken,
      //         pushCount: pushCount,
      //         to_userid: authorid
      //     }
      //     const msgPush = youmengService.msgPush(body__)
      //     // if (!sendMsgRs['backSuccess']) {
      //     //     console.log('content-0321 ', sendMsgRs['msg']);
      //     // }
      // }
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//点赞
const addLike = router.post('/n/content/like/new', function* addLike() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const authorid = body.authorid;
    const contentid = body.contentid;
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      authorid: parseInt(authorid),
      contentid: parseInt(contentid),
    };
    const result = yield contentController.addLike(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
      yield contentController.baseVaryHertz(userid, authorid);
      // const status = result['status'];
      // if (status == 0 && parseInt(userid) != parseInt(authorid)) {
      //     const nickName = result['nickName'];
      //     const data = {
      //         userid: authorid,
      //         text: `${nickName} 赞了你的动态`,
      //         from: 4,
      //         nickname: nickName
      //     }
      //     const sendMsgRs = imService.sendMsg(data)
      //     const key__ = 'HASH:USER:INFO:' + authorid
      //     const redisRS__ = yield redisInstance.hashGet(key__);
      //     const deviceToken = redisRS__['deviceToken'];

      //     const key2__ = `STR:USER:PUSH:COUNT:${authorid}`
      //     const redisRS2__ = yield redisInstance.get(key2__);
      //     const pushCount = redisRS2__;

      //     const body__ = {
      //         text: `${nickName} 赞了你的动态`,
      //         device_token: deviceToken,
      //         pushCount: pushCount,
      //         to_userid: authorid
      //     }
      //     const msgPush = youmengService.msgPush(body__)
      //     // if (!sendMsgRs['backSuccess']) {
      //     //     console.log('content-0321 ', sendMsgRs['msg']);
      //     // }
      // }
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//取消点赞
const releaseLike = router.post('/n/content/like/release', function* releaseLike() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const authorid = body.authorid;
    const contentid = body.contentid;
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      authorid: parseInt(authorid),
      contentid: parseInt(contentid),
    };
    const result = yield contentController.releaseLike(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//评论
const putContentComment = router.post('/n/content/comment', function* putContentComment() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const authorid = body.authorid;
    const contentid = body.contentid;
    const comment = body.comment;
    const to_userid = body.to_userid;
    const belongs = body.belongs;
    const to_commentid = body.to_commentid;
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    if (!comment) {
      this.body = { code: 1, msg: '请上传comment', data: [] };
      return;
    }
    if (!to_userid) {
      this.body = { code: 1, msg: '请上传to_userid', data: [] };
      return;
    }
    if (!belongs) {
      this.body = { code: 1, msg: '请上传belongs', data: [] };
      return;
    }
    if (!to_commentid) {
      this.body = { code: 1, msg: '请上传to_commentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      authorid: parseInt(authorid),
      contentid: parseInt(contentid),
      comment: comment,
      to_userid: parseInt(to_userid),
      time: parseInt(Math.round(new Date().getTime() / 1000)),
      belongs: parseInt(belongs),
      to_commentid: parseInt(to_commentid),
    };
    const result = yield contentController.putContentComment(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ commentid: result['data'] }] };
      yield contentController.baseVaryHertz(userid, authorid);
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//删除评论
const delComment = router.del('/n/content/comment', function* delComment() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const contentid = query.contentid;
    const commentid = query.commentid;
    const authorid = query.authorid;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!commentid) {
      this.body = { code: 1, msg: '请上传commentid', data: [] };
      return;
    }
    const data = {
      userid: parseInt(userid),
      commentid: parseInt(commentid),
      contentid: parseInt(contentid),
      authorid: parseInt(authorid),
    };
    const result = yield contentController.delComment(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, contentid: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getContentInfo = router.get('/n/content', function* getContentInfo() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const from = query.from;
    const contentid = query.contentid;
    const authorid = query.authorid;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    if (!from) {
      this.body = { code: 1, msg: '请上传from', data: [] };
      return;
    }
    const data = {
      contentid: parseInt(contentid),
      authorid: parseInt(authorid),
      userid: userid,
    };
    const result = yield contentController.getContentInfo(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
      if (parseInt(from) === 1) {
        yield contentController.baseVaryHertz(userid, authorid);
      }
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const newGetContentInfo = router.get('/n/content/new', function* newGetContentInfo() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const from = query.from;
    const contentid = query.contentid;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    if (!from) {
      this.body = { code: 1, msg: '请上传from', data: [] };
      return;
    }
    const data = {
      contentid: parseInt(contentid),
      userid: userid,
    };
    const result = yield contentController.newGetContentInfo(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
      const authorid = parseInt(result['data'].authorid);
      if (parseInt(from) === 1) {
        yield contentController.baseVaryHertz(userid, authorid);
      }
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getContentLike = router.get('/n/content/like', function* getContentLike() {
  try {
    const query = this.request.query;
    const contentid = query.contentid;
    const authorid = query.authorid;
    const page = query.page || 1;
    const size = query.size || 10;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    const data = {
      contentid: parseInt(contentid),
      authorid: parseInt(authorid),
      size: size,
      page: page,
    };
    const result = yield contentController.getContentLike(data);
    const data_ = {
      count: `${result['count']}`,
      size: size,
      page: page,
      data: result['data'],
    };
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [data_] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getContentComment = router.get('/n/content/comment', function* getContentComment() {
  try {
    const uid = this.request._uid;
    const query = this.request.query;
    const contentid = query.contentid;
    const authorid = query.authorid;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 10;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    if (!authorid) {
      this.body = { code: 1, msg: '请上传authorid', data: [] };
      return;
    }
    const data = {
      uid: uid,
      contentid: parseInt(contentid),
      authorid: parseInt(authorid),
      size: size,
      page: page,
    };
    const result = yield contentController.getContentComment(data);
    const data_ = {
      count: `${result['count']}`,
      size: size,
      page: page,
      data: result['data'],
    };
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [data_] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const contentSearch = router.get('/n/content/search', function* contentSearch() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const content = query.content;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 20;
    if (!content) {
      this.body = { code: 1, msg: '内容不能为空', data: [] };
      return;
    }
    const result = yield contentController.contentSearchNew(userid, content, page, size);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getUserLikeList = router.get('/n/conntent/like/list', function* getUserLikeList() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const page = query.page || 1;
    const size = query.size || 20;
    const result = yield contentController.getUserLikeList(userid, page, size);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'], count: result['count'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const postContentHide = router.post('/n/content/hide', function* postContentHide() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const contentid = body.contentid;
    const immediately = body.immediately;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }
    const result = yield contentController.postContentHide(userid, contentid, immediately);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const postContentTop = router.post('/n/content/top', function* postContentHide() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const contentid = body.contentid;
    if (!contentid) {
      this.body = { code: 1, msg: '请上传contentid', data: [] };
      return;
    }

    const result = yield contentController.postContentTop(userid, contentid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  putContent: putContent,
  putContentLike: putContentLike,
  putContentComment: putContentComment,
  delContent: delContent,
  delComment: delComment,
  getContentInfo: getContentInfo,
  getContentLike: getContentLike,
  getContentComment: getContentComment,
  newGetContentInfo: newGetContentInfo,
  getContentTag: getContentTag,
  getContentByTag: getContentByTag,
  putMusicContent: putMusicContent,
  getMusicInfo: getMusicInfo,
  addLike: addLike,
  releaseLike: releaseLike,
  getMusicInfoNew: getMusicInfoNew,
  putMusicContentNew: putMusicContentNew,
  contentSearch: contentSearch,
  getUserLikeList: getUserLikeList,
  putLinkShareContentNew: putLinkShareContentNew,
  postContentHide: postContentHide,
  postContentTop: postContentTop,
};
