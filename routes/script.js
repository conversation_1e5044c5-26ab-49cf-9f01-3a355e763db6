'use strict';

const router = require('koa-route');
const scriptController = require('../controller/script').scriptInstance;

const getInfo = router.get('/n/script/get', function* getInfo() {
  try {
    const query = this.request.query;
    const userid = query.userid;
    const password = query.password;
    const startTime = parseInt(query.startTime);
    const endTime = parseInt(query.endTime);
    if (parseInt(userid) !== 11) {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    if (password !== 'woshiguochenxi') {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    if (!startTime) {
      this.body = { code: 1, msg: 'startTime不得为空' };
      return;
    }

    if (!endTime) {
      this.body = { code: 1, msg: 'endTime不得为空' };
      return;
    }

    const data = {
      startTime: startTime,
      endTime: endTime,
    };
    const result = yield scriptController.getInfo(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getSsid = router.get('/n/script/get/ssid', function* getSsid() {
  try {
    const query = this.request.query;
    const password = query.password;
    const userid = query.userid;
    if (password != 'woshiguochenxi') {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }
    const result = yield scriptController.getSsid(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getWhiteList = router.get('/n/script/whitelist/get', function* getWhiteList() {
  try {
    const query = this.request.query;
    const userid = query.userid;
    const password = query.password;
    if (parseInt(userid) !== 11) {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    if (password !== 'woshiguochenxi') {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }
    const result = yield scriptController.getWhiteList(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
const addWhiteList = router.post('/n/script/whitelist/add', function* addWhiteList() {
  try {
    const body = this.request.body;
    const userid = body.userid;
    const password = body.password;
    const whiteUserid = body.whiteUserid;
    if (parseInt(userid) !== 11) {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    if (password !== 'woshiguochenxi') {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }
    if (!whiteUserid) {
      this.body = { code: 1, msg: 'whiteUserid不得为空' };
      return;
    }
    const result = yield scriptController.addWhiteList(whiteUserid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
const delWhiteList = router.post('/n/script/whitelist/del', function* delWhiteList() {
  try {
    const body = this.request.body;
    const userid = body.userid;
    const password = body.password;
    const whiteUserid = body.whiteUserid;
    if (parseInt(userid) !== 11) {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    if (password !== 'woshiguochenxi') {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }
    if (!whiteUserid) {
      this.body = { code: 1, msg: 'whiteUserid不得为空' };
      return;
    }
    const result = yield scriptController.delWhiteList(whiteUserid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const userActive = router.get('/n/script/user/active', function* userActive() {
  try {
    const query = this.request.query;
    const userid = query.userid;
    const password = query.password;
    if (parseInt(userid) !== 11) {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    if (password !== 'woshiguochenxi') {
      this.body = { code: 1, msg: '不可调用该接口' };
      return;
    }

    const result = yield scriptController.userActive();
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getInfo: getInfo,
  // getSsid: getSsid
  getWhiteList: getWhiteList,
  addWhiteList: addWhiteList,
  delWhiteList: delWhiteList,
  userActive: userActive,
};
