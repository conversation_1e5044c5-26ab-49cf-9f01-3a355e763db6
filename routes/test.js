'use strict';

const router = require('koa-route');
const testInstance = require('../controller/test').testInstance;
const hbaseInstance = require('../models/hbase').hbaseInstance;
const testController = require('../controller/test').testInstance;
const feedController = require('../controller/feed_controller').feedInstance;
const imService = require('../services/IM_service').IMInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;
const CHInstance = require('../models/clickhouse').CHInstance;
const feishu = require('../services/feishu');
const FSBot = new feishu();

const test2 = router.post('/n/test2', function* test2() {
  try {
    const body = this.request.body;
    console.log(body);

    // const rs = yield FSBot.getUserInfo(body['event']['open_id']);
    const rs = yield FSBot.getUserInfo('89fc8ebc');

    console.log(rs);
    this.body = rs;
    return;
  } catch (err) {
    console.log(err);
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const test = router.get('/n/test', function* test() {
  try {
    console.log(1111);
    yield feedController.toUserWeight();
    this.body = { code: 0, msg: 'SUCCESS', data: [] };
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
const test3 = router.get('/n/test/ch', function* test3() {
  try {
    const query = this.request.query;
    const id = query.id;
    const type = parseInt(query.type);
    if (type === 1) {
      yield CHInstance.addTest();
    } else if (type === 2) {
      if (!id) {
        return (this.body = { code: 0, msg: 'FAILE', data: [] });
      }
      yield CHInstance.updateTest(id);
    } else if (type === 3) {
      if (!id) {
        return (this.body = { code: 0, msg: 'FAILE', data: [] });
      }
      yield CHInstance.delTest(id);
    }
    const result = yield CHInstance.test(id);
    this.body = { code: 0, msg: 'SUCCESS', data: result };
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const test4 = router.get('/n/test/4', function* test4() {
  try {
    yield feedController.toUserWeight();
    this.body = { code: 0, msg: 'SUCCESS', data: [] };
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  test2: test2,
  test: test,
  test3: test3,
  test4: test4,
};
