'use strict';

const router = require('koa-route');
const commonController = require('../controller/common_controller').commonInstance;
const invitationCode = require('../config/invitationCode').invitationCode;
const imService = require('../services/IM_service').IMInstance;
const { DefaultConfig } = require('../config/default');
const mobileArr = DefaultConfig.mobileArr;
const SIG = require('tls-sig-api');
const Agreement = require('../config/user_agreement').Agreement;
const blackUserArr = DefaultConfig.blackUserArr;
const config = DefaultConfig.sig_config;
//获取用户协议
const getUserAgreement = router.get('/n/agreement', function* getUserAgreement() {
  try {
    this.body = Agreement;
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
//1:显示邀请码页面 0:不展示
const invitationStatus = router.get('/n/common/invitationStatus', function* invitationStatus() {
  try {
    this.body = { code: 0, msg: 'success', data: [{ type: '0' }] };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateUserSig = router.post('/n/common/usersig', function* takeUserSig() {
  try {
    const userid = this.request._uid;
    const sig_ = new SIG.Sig(config);
    const sig = sig_.genSig(`${userid}`);
    yield commonController.updateUserSig(userid, sig);
    this.body = { code: 0, msg: 'success', data: [{ usersig: sig }] };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getGuideMapStatus = router.get('/n/common/guidemapstatus', function* getGuideMapStatus() {
  try {
    const userid = this.request._uid;
    const result = yield commonController.getGuideMapStatus(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ status: result['data'] }] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateGuideMapStatus = router.post('/n/common/guidestatus', function* getGuideMapStatus() {
  try {
    const userid = this.request._uid;
    const result = yield commonController.updateGuideMapStatus(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateUserSigFun = router.post('/n/common/usersigFun', function* takeUserSig() {
  try {
    const usersig = this.request.body.usersig;
    elogger.info(`USERSIG : ${USERSIG}     usersig : ${usersig}`);
    USERSIG = usersig;
    this.body = { code: 0, msg: 'success', data: [] };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateSsid = router.post('/n/common/updatessid', function* updateSsid() {
  try {
    const ssid = this.request.ssid;
    const userid = this.request._uid;
    if (blackUserArr.indexOf(parseInt(userid)) > -1) {
      return (this.body = { code: 1, msg: 'faile', data: [] });
    }
    const result = yield commonController.updateSsid(ssid, userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ ssid: result['ssid'] }] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const invitationCodeFun = router.post('/n/common/invitationCode', function* invitationCodeFun() {
  try {
    const body = this.request.body;
    const code = body.code.toString();
    if (!code) {
      this.body = { code: 1, msg: 'code不得为空', data: [] };
      return;
    }
    const invitationCodeArr = invitationCode.split(',');
    if (invitationCodeArr.indexOf(code) === -1) {
      this.body = { code: 1, msg: '邀请码不合法', data: [] };
      return;
    }
    const result = yield commonController.invitationCodeFun(`${code}`);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const invitationCodeFunNew = router.post('/n/common/invitationCode/new', function* invitationCodeFunNew() {
  try {
    const body = this.request.body;
    let code = body.code;
    if (!code) {
      this.body = { code: 1, msg: 'code不得为空', data: [] };
      return;
    }

    code = code.toString();

    const result = yield commonController.invitationCodeFunNew(code);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getInvitationCodeFun = router.get('/n/common/inviteCode', function* getInvitationCodeFun() {
  try {
    const userid = this.request._uid;
    const result = yield commonController.getInvitationCodeFunNew(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取短信验证码
const getMsgCode = router.get('/n/common/code', function* getMsgCode() {
  try {
    const query = this.request.query;
    const mobile = parseInt(query.mobile);
    const userAgent = this.request.userAgent;
    const time = 60 * 15;
    if (!mobile) {
      this.body = { code: 1, msg: '请输入手机号码', data: [] };
      return;
    }
    if (mobileArr.indexOf(mobile) !== -1) {
      this.body = { code: 0, msg: 'success', data: [] };
      return;
    }
    const result = yield commonController.sendCodeNew(mobile, time, userAgent);

    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      if (result.code) {
        this.body = {
          code: 1003,
          msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
          data: [],
        };
      } else {
        this.body = { code: 1, msg: '短信验证码获取频繁，请稍后再试', data: [] };
      }
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//短信邀请
const invitedMsgCode = router.post('/n/common/invited', function* invitedMsgCode() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const phoneNum = parseInt(body.phoneNum);
    if (!phoneNum) {
      this.body = { code: 1, msg: '请输入手机号码', data: [] };
      return;
    }
    const result = yield commonController.invitedMsgCode(phoneNum, userid);

    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: '短信验证码获取频繁，请稍后再试', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
// 手机号登陆注册前判断新老用户
const judgeByMobile = router.get('/n/common/mobile/judge', function* judgeByMobile() {
  try {
    const query = this.request.query;
    const mobile = query.mobile;
    if (!mobile) {
      this.body = { code: 1, msg: 'mobile not null', data: [] };
      return;
    }
    let result = yield commonController.judgeByMobile(mobile);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
//注册*
const validateCodeRouter = router.post('/n/common/validate_code', function* validateCodeRouter() {
  try {
    const body = this.request.body;
    const mobile = parseInt(body.mobile);
    const code = parseInt(body.code);
    const ip = this.request._ip;
    const invitationCode = body.invitationCode || 885779;
    const userAgent = this.request.userAgent;
    const type = body.type;

    if (!mobile) {
      this.body = { code: 1, msg: '请输入手机号码', data: [] };
      return;
    }
    if (!code) {
      this.body = { code: 1, msg: '请输入验证码', data: [] };
      return;
    }

    // if (!invitationCode) {
    //     this.body = { 'code': 1, 'msg': 'invitationCode not be null', 'data': [] };
    //     return;
    // }

    const datas = {
      mobile: mobile,
      code: code,
      ip: ip,
      // invitationCode: invitationCode,
      type: type,
      userAgent: userAgent,
    };

    const arr = [];
    const result = yield commonController.validateCode(datas);
    if (result['backSuccess']) {
      //判断用户是否存在，不存在自动注册，已存在返回用户信息
      const result_ = yield commonController.mobileRegisteredFun(datas);
      if (result_['backSuccess']) {
        arr.push(result_['data']);
        this.body = { code: 0, msg: 'success', data: arr };
      } else {
        if (result.code) {
          this.body = {
            code: 1003,
            msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
            data: [],
          };
        } else {
          this.body = { code: 1, msg: result_['msg'], data: [] };
        }
      }
    } else {
      if (result.code) {
        this.body = {
          code: 1003,
          msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
          data: [],
        };
      } else {
        this.body = { code: 1, msg: result['msg'], data: [] };
      }
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const validateCodeRouterNew = router.post('/n/common/validate_code/new', function* validateCodeRouter() {
  try {
    const body = this.request.body;
    const mobile = parseInt(body.mobile);
    const code = parseInt(body.code);
    const ip = this.request._ip;
    const invitationCode = body.invitationCode;
    const userAgent = this.request.userAgent;
    const type = body.type;

    if (!mobile) {
      this.body = { code: 1, msg: '请输入手机号码', data: [] };
      return;
    }
    if (!code) {
      this.body = { code: 1, msg: '请输入验证码', data: [] };
      return;
    }

    if (!type) {
      this.body = { code: 1, msg: 'type not be null', data: [] };
      return;
    }

    if (type === 'new' && !invitationCode) {
      this.body = { code: 1, msg: 'invitationCode not be null', data: [] };
      return;
    }

    const datas = {
      mobile: mobile,
      code: code,
      ip: ip,
      invitationCode: invitationCode,
      type: type,
      userAgent: userAgent,
    };
    const arr = [];
    const result = yield commonController.validateCode(datas);
    if (result['backSuccess']) {
      const result_ = yield commonController.mobileRegisteredFunNew(datas);
      if (result_['backSuccess']) {
        arr.push(result_['data']);
        this.body = { code: 0, msg: 'success', data: arr };
      } else {
        if (result.code) {
          this.body = {
            code: 1003,
            msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
            data: [],
          };
        } else {
          this.body = { code: 1, msg: result_['msg'], data: [] };
        }
      }
    } else {
      if (result.code) {
        this.body = {
          code: 1003,
          msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
          data: [],
        };
      } else {
        this.body = { code: 1, msg: result['msg'], data: [] };
      }
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//微信登陆注册
const wxlogin = router.post('/n/common/wx/login', function* wxlogin() {
  try {
    const body = this.request.body;
    const code = body.code;
    const ip = this.request._ip;
    const invitationCode = body.invitationCode || 885779;
    const userAgent = this.request.userAgent;
    const type = body.type;
    if (!code) {
      this.body = { code: 1, msg: '请上传code', data: [] };
      return;
    }
    if (!invitationCode) {
      this.body = { code: 1, msg: 'invitationCode not be null', data: [] };
      return;
    }
    const datas = {
      code: code,
      ip: ip,
      invitationCode: invitationCode,
      type: type,
      userAgent: userAgent,
    };

    let result = yield commonController.wxLogin(datas);
    const arr = [];
    arr.push(result['data']);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: arr };
    } else {
      if (result.code) {
        this.body = {
          code: 1003,
          msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
          data: [],
        };
      } else {
        this.body = { code: 1, msg: result['msg'], data: [] };
      }
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const wxloginNew = router.post('/n/common/wx/login/new', function* wxlogin() {
  try {
    const body = this.request.body;
    const code = body.code;
    const ip = this.request._ip;
    const invitationCode = 514345;
    const userAgent = this.request.userAgent;
    const type = body.type;
    if (!code) {
      this.body = { code: 1, msg: '请上传code', data: [] };
      return;
    }

    if (!type) {
      this.body = { code: 1, msg: 'type not be null', data: [] };
      return;
    }

    const datas = {
      code: code,
      ip: ip,
      invitationCode: invitationCode,
      type: type,
      userAgent: userAgent,
    };
    let result = yield commonController.wxLoginNew(datas);
    const arr = [];
    arr.push(result['data']);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: arr };
    } else {
      if (result.code) {
        this.body = {
          code: 1003,
          msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
          data: [],
        };
      } else {
        this.body = { code: 1, msg: result['msg'], data: [] };
      }
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const loginWithPassword = router.post('/n/common/login/password', function* loginWithPassword() {
  try {
    const body = this.request.body;
    const account = body.account;
    const password = body.password;
    const ip = this.request._ip;

    if (!account) {
      this.body = { code: 1, msg: '请输入账号或手机号', data: [] };
      return;
    }
    if (!password) {
      this.body = { code: 1, msg: '请输入密码', data: [] };
      return;
    }

    // 手机号正则：以1开头的11位数字
    const mobileRegex = /^1\d{10}$/;
    // 用户名验证：
    // ^[a-zA-Z]          - 必须以字母开头
    // [a-zA-Z0-9_]{5,19}$ - 后面可以是字母数字下划线，总长度6-20
    // .*[a-zA-Z].*       - 必须包含至少一个字母
    const accountRegex = /^[a-zA-Z][a-zA-Z0-9_]{5,19}$/;
    const containsLetterRegex = /.*[a-zA-Z].*/;

    let isMobile = mobileRegex.test(account);
    if (isMobile) {
      // 只校验手机号格式
      if (!mobileRegex.test(account)) {
        this.body = {
          code: 1,
          msg: '手机号格式不正确',
          data: [],
        };
        return;
      }
    } else {
      // 校验用户名规则
      if (!accountRegex.test(account) || !containsLetterRegex.test(account)) {
        this.body = {
          code: 1,
          msg: '用户名不合规',
          data: [],
        };
        return;
      }
    }

    // 密码验证：
    // (?=.*[a-z])        - 必须包含小写字母
    // (?=.*[A-Z])        - 必须包含大写字母
    // (?=.*\d)           - 必须包含数字
    // (?=.*[,.!@#$%^&*]) - 必须包含英文标点符号
    // [a-zA-Z\d,.!@#$%^&*]{10,16} - 总长度10-16位
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[,.!@#$%^&*])[a-zA-Z\d,.!@#$%^&*]{10,16}$/;

    if (!passwordRegex.test(password)) {
      this.body = {
        code: 1,
        msg: '密码不合规',
        data: [],
      };
      return;
    }
    const datas = {
      isMobile,
      account: account,
      password: password,
    };
    const arr = [];
    const result = yield commonController.validateAccount(datas);
    if (result['backSuccess']) {
      const result_ = yield commonController.accountLogin(result.data, ip);
      arr.push(result_['data']);
      this.body = { code: 0, msg: 'success', data: arr };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//登出*
const logoutRouter = router.get('/n/common/logout', function* logoutRouter() {
  try {
    const ssid = this.request.ssid;
    const userid = this.request._uid;
    const datas = yield commonController.logoutFun(ssid, userid);
    if (datas['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取7牛验证
const getCertificate = router.get('/n/common/certificate', function* getCertificate() {
  try {
    const query = this.request.query;
    const type = query.type;
    const image_name = query.image_name;
    if (!type) {
      this.body = { code: 1, msg: '请上传type' };
      return;
    }
    const result = yield commonController.getCertificate(type, image_name);
    const arr = [];
    arr.push(result['data']);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: arr };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const qiniuCallback = router.post('/n/common/qiniu/callback', function* qiniuCallback() {
  try {
    const body = this.request.body;
    // elogger.debug('qiniu_callback:::::', body);
    const name = body['key'];
    if (name) {
      this.body = { code: 200 };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getIpAddress = router.get('/n/common/ip', function* getIpAddress() {
  try {
    const query = this.request.query;
    const ip = query.ip;
    const result = yield commonController.getIpAddress(ip);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const sendMsg = router.post('/n/common/sendMsg', function* sendMsg() {
  try {
    const body = this.request.body;
    const userid = body.userid;
    const fromUserid = body.fromUserid;
    const text = body.text;
    const usersig = body.usersig;
    if (!userid) {
      this.body = { code: 1, msg: '请上传userid', data: [] };
      return;
    }
    if (!text) {
      this.body = { code: 1, msg: '请上传text', data: [] };
      return;
    }

    const data = {
      userid: userid,
      text: text,
      from: 3,
    };
    const result = imService.sendMsg(data);
    // if (result['backSuccess']) {
    //     this.body = { 'code': 0, 'msg': 'success', 'data': result['data'] };
    // } else {
    //     this.body = { 'code': 1, 'msg': result['msg'], 'data': [] };
    // }
    this.body = { code: 0, msg: 'success', data: result['data'] };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const accountImport = router.post('/n/common/accountImport', function* accountImport() {
  try {
    const body = this.request.body;
    const userid = body.userid;
    const nickName = body.nickName;
    const avatar = body.avatar;
    if (!userid) {
      this.body = { code: 1, msg: '请上传userid', data: [] };
      return;
    }
    if (!nickName) {
      this.body = { code: 1, msg: '请上传nickName', data: [] };
      return;
    }
    if (!avatar) {
      this.body = { code: 1, msg: '请上传avatar', data: [] };
      return;
    }

    const data = {
      userid: userid,
      nickName: nickName,
      avatar: avatar,
    };
    const result = yield imService.accountImport(data);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getLocationAndPushStatus = router.get('/n/common/lpstatus', function* getLocationAndPushStatus() {
  try {
    const userid = this.request._uid;
    const result = yield commonController.getLocationAndPushStatus(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateCellStyle = router.post('/n/common/cellstyle', function* updateCellStyle() {
  try {
    const body = this.request.body;
    const style = body.style;
    if (!style) {
      this.body = { code: 1, msg: '参数不完整', data: [] };
      return;
    }
    if ([1, 2].indexOf(parseInt(style)) === -1) {
      this.body = { code: 1, msg: '参数不完整', data: [] };
      return;
    }
    const userid = this.request._uid;
    const result = yield commonController.updateCellStyle(userid, style);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateHomeHide = router.post('/n/common/homeHide', function* updateHomeHide() {
  try {
    const body = this.request.body;
    const hide = body.hide;
    if (!hide) {
      this.body = { code: 1, msg: '参数不完整', data: [] };
      return;
    }
    if ([1, 0].indexOf(parseInt(hide)) === -1) {
      this.body = { code: 1, msg: '参数不完整', data: [] };
      return;
    }
    const userid = this.request._uid;
    const result = yield commonController.updateHomeHide(userid, hide);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateLocationAndPushStatus = router.post('/n/common/lpstatus', function* updateLocationAndPushStatus() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const ip = this.request._ip;
    const locationStatus = body.locationStatus;
    const pushStatus = body.pushStatus;
    const location = body.location;
    if (!locationStatus) {
      this.body = { code: 1, msg: '请上传locationStatus', data: [] };
      return;
    }
    if (!pushStatus) {
      this.body = { code: 1, msg: '请上传pushStatus', data: [] };
      return;
    }
    const data = {
      userid: userid,
      locationStatus: locationStatus,
      pushStatus: pushStatus,
      ip: ip,
      location: location,
    };
    const result = yield commonController.updateLocationAndPushStatus(data);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getNotificationList = router.get('/n/common/notification', function* getNotificationList() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const page = parseInt(query.page) || 1;
    const size = parseInt(query.size) || 10;
    const result = yield commonController.getNotificationList(userid, page, size);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateNotificationStatus = router.post('/n/common/notification', function* updateNotificationStatus() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const noticationId = body.noticationId;
    if (!noticationId) {
      this.body = { code: 1, msg: '请上传noticationId' };
      return;
    }
    const result = yield commonController.updateNotificationStatus(parseInt(noticationId), userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const deleteNotification = router.post('/n/common/notification/del', function* deleteNotification() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const noticationIds = body.noticationIds;
    if (!noticationIds) {
      this.body = { code: 1, msg: '请上传noticationIds' };
      return;
    }
    const result = yield commonController.deleteNotification(noticationIds, userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getUnreadNotification = router.get('/n/common/notification/unread', function* getUnreadNotification() {
  try {
    const userid = this.request._uid;
    const result = yield commonController.getUnreadNotification(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const pushRouter = router.post('/n/common/push', function* pushRouter() {
  try {
    const body = this.request.body;
    const to_userid = body.to_userid;
    const nickname = body.nickname;
    const text = body.text;
    const userid = this.request._uid;
    const to_nickname = body.to_nickname;
    if (!to_userid) {
      this.body = { code: 1, msg: '请上传to_userid', data: [] };
      return;
    }
    if (!to_nickname) {
      this.body = { code: 1, msg: '请上传to_nickname', data: [] };
      return;
    }
    if (!nickname) {
      this.body = { code: 1, msg: '请上传nickname', data: [] };
      return;
    }
    if (!text) {
      this.body = { code: 1, msg: '请上传text', data: [] };
      return;
    }

    const data = {
      to_userid: to_userid,
      nickname: nickname,
      to_nickname: to_nickname,
      text: text,
      userid: userid,
    };
    const result = commonController.pushRouter(data);
    this.body = { code: 0, msg: 'success', data: [] };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const feedBack = router.post('/n/feed/back', function* feedBack() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const text = body.text;
    const type = body.type;
    if (!text) {
      this.body = { code: 1, msg: '建议内容不能为空', data: [] };
      return;
    }
    if (!type) {
      this.body = { code: 1, msg: '请选择反馈类型', data: [] };
      return;
    }
    const result = yield commonController.feedBack(userid, text, type);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const changeMobile = router.post('/n/common/mobile/change', function* changeMobile() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const mobile = parseInt(body.mobile);
    const code = parseInt(body.code);
    if (!mobile) {
      this.body = { code: 1, msg: '手机号不能为空', data: [] };
      return;
    }
    if (!code) {
      this.body = { code: 1, msg: '验证码不能为空', data: [] };
      return;
    }
    const datas = {
      mobile: mobile,
      code: code,
      userid: userid,
    };
    // const result = yield commonController.validateCode(datas);
    // if (result['backSuccess']) {
    const result_ = yield commonController.changeMobile(datas);
    if (result_['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result_['msg'], data: [] };
    }
    // } else {
    //     this.body = { 'code': 1, 'msg': result['msg'], 'data': [] };
    // }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getSmsCode = router.get('/n/sms/code', function* getSmsCode() {
  try {
    const query = this.request.query;
    const pass = query.pass;
    const userAgent = this.request.userAgent;
    const mobile = parseInt(query.mobile);
    if (!mobile) {
      this.body = { code: 1, msg: '手机号不能为空', data: [] };
      return;
    }

    if (!pass) {
      this.body = { code: 1, msg: 'pass不能为空', data: [] };
      return;
    }

    if (pass !== 'woshizhu') {
      this.body = { code: 1, msg: 'unknow', data: [] };
      return;
    }

    const result_ = yield commonController.getSmsCode(mobile, userAgent);
    if (result_['backSuccess']) {
      this.body = { code: 0, code: result_['code'] };
    } else {
      if (result.code) {
        this.body = {
          code: 1003,
          msg: '因经费情况安卓不再支持注册新用户, 老用户仍可使用(安卓APP不再更新), 请见谅, 希望后会有期',
          data: [],
        };
      } else {
        this.body = { code: 1, msg: '出错了', data: [] };
      }
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getFree = router.get('/n/common/free', function* getFree() {
  try {
    const result = yield commonController.getFree();
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  updateUserSig: updateUserSig,
  invitationCodeFun: invitationCodeFun,
  getMsgCode: getMsgCode,
  validateCodeRouter: validateCodeRouter,
  wxlogin: wxlogin,
  logoutRouter: logoutRouter,
  getCertificate: getCertificate,
  qiniuCallback: qiniuCallback,
  getIpAddress: getIpAddress,
  updateSsid: updateSsid,
  sendMsg: sendMsg,
  accountImport: accountImport,
  updateUserSigFun: updateUserSigFun,
  getLocationAndPushStatus: getLocationAndPushStatus,
  updateLocationAndPushStatus: updateLocationAndPushStatus,
  getNotificationList: getNotificationList,
  updateNotificationStatus: updateNotificationStatus,
  invitationStatus: invitationStatus,
  deleteNotification: deleteNotification,
  getUnreadNotification: getUnreadNotification,
  getGuideMapStatus: getGuideMapStatus,
  updateGuideMapStatus: updateGuideMapStatus,
  pushRouter: pushRouter,
  getUserAgreement: getUserAgreement,
  feedBack: feedBack,
  invitedMsgCode: invitedMsgCode,
  invitationCodeFunNew: invitationCodeFunNew,
  getInvitationCodeFun: getInvitationCodeFun,
  validateCodeRouterNew: validateCodeRouterNew,
  wxloginNew: wxloginNew,
  judgeByMobile: judgeByMobile,
  changeMobile: changeMobile,
  getSmsCode: getSmsCode,
  loginWithPassword: loginWithPassword,
  getFree: getFree,
  updateCellStyle: updateCellStyle,
  updateHomeHide: updateHomeHide,
};
