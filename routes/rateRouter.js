'use strict';
/** 标签分组编排
 *  A:[1~1000]
 *  B:[1001~2000]
 *  C:[2001~3000]
 *  D:[3001~4000]
 *  E:[4001~5000]
 */

const router = require('koa-route');
const rateController = require('../controller/rate_controller').rateInstance;

const getAttribute = router.get('/n/rate/attribute', function* getAttribute() {
  try {
    const query = this.request.query;
    const type = parseInt(query.type) || 0; //0:气泡， 1:标签
    const result = yield rateController.getAttribute(type);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const calculateRate = router.post('/n/rate/calculate', function* calculateRate() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const tagArrStr = body.tagArrStr;
    const type = body.type; //type = 0 只计算 type = 1 计算并入库
    const attribute = body.attribute;
    const rate = body.rate;
    const likeTagArrStr = body.likeTagArrStr;
    if (!type) {
      this.body = { code: 1, msg: 'type不得为空', data: [] };
      return;
    }
    if (!tagArrStr) {
      this.body = { code: 1, msg: 'tagArrStr不得为空', data: [] };
      return;
    }
    if (type == 1) {
      if (!attribute) {
        this.body = { code: 1, msg: 'attribute不得为空', data: [] };
        return;
      }
      if (!rate) {
        this.body = { code: 1, msg: 'rate不得为空', data: [] };
        return;
      }
    }

    let tagIntArr = [];
    const allTagArrStr = tagArrStr + likeTagArrStr;
    const tagArr = allTagArrStr.split(',');
    tagArr.forEach(function (data, index, arr) {
      data = +data;
      if (data == 0) {
        this.body = { code: 0, msg: 'success', data: [{ rate: 0 }] };
      }
      tagIntArr.push(+data);
    });
    const data = {
      userid: userid,
      type: type,
      tagIntArr: tagIntArr,
      tagArrStr: tagArrStr,
      likeTagArrStr: likeTagArrStr,
      attribute: attribute,
      rate: parseFloat(rate),
    };
    const result = yield rateController.calculateRate(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const tagFun = router.get('/n/rate/fun', function* tagFun() {
  try {
    const rs = yield rateController.tagFun();
    this.body = { data: rs };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  calculateRate: calculateRate,
  tagFun: tagFun,
  getAttribute: getAttribute,
};
