'use strict';
const router = require('koa-route');
const adminReportController = require('../controller/admin_report_controller').reportInstance;
const { DefaultConfig } = require('../config/default');

const getReportList = router.get('/n/admin/report/list', function* getReportList() {
  try {
    let { from, size, status } = this.request.query;
    from = from || 0;
    size = size || 20;
    const result = yield adminReportController.getReportList(from, size, status);
    return (this.body = { code: 0, msg: 'success', data: result });
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getReportList: getReportList,
};
