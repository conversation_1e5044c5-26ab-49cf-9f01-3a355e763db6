'use strict';
const router = require('koa-route');
const phpController = require('../controller/php_controller').phpInstance;

//获取分享文案
const getShareContent = router.get('/p/hertz/share_content', function* getShareContent() {
  try {
    const userid = this.request._uid;
    const result = yield phpController.getShareContent(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取海洋（舍）

//获取动态详情
const getAffairDetail = router.get('/p/affair/detail', function* getAffairDetail() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const aid = query.aid;
    if (!aid) {
      return (this.body = { code: 1, msg: '动态不存在或者已删除', data: [[]] });
    }
    const result = yield phpController.getAffairDetail({ userid: userid, contentid: aid });
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取点赞列表
const getAffairLikes = router.get('/p/affair/likes', function* getAffairLikes() {
  try {
    const query = this.request.query;
    const aid = query.aid;
    const page = query.page || 1; // 安卓 1 iOS 1
    const size = query.size || 120; // 安卓 120 iOS 20

    if (parseInt(page) === 0) {
      this.body = { code: 1, msg: 'faile', data: [] };
      return;
    }
    if (!aid) {
      return (this.body = { code: 0, msg: '获取成功', data: [{ page: `${page}`, size: `${size}`, count: '0' }] });
    }
    const result = yield phpController.getAffairLikes({ aid, page, size });
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取评论列表
const getAffairComments = router.get('/p/affair/comments', function* getAffairComments() {
  try {
    const query = this.request.query;
    const aid = query.aid;
    const page = query.page || 1; // 安卓 1 iOS
    const size = query.size || 120; // 安卓 120 iOS

    if (parseInt(page) === 0) {
      this.body = { code: 1, msg: 'faile', data: [] };
      return;
    }
    if (!aid) {
      return (this.body = { code: 0, msg: '获取成功', data: [{ page: `${page}`, size: `${size}`, count: '0' }] });
    }
    const result = yield phpController.getAffairComments({ aid, page, size });
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取某个用户的动态列表
const getListByUserid = router.get('/p/affair/list_by_uid', function* getListByUserid() {
  try {
    const query = this.request.query;
    const userid_ = this.request._uid;
    const userid = query.uid;
    let page = query.page || 1; // 安卓 1 iOS

    const user_agent = this.request.header['user-agent'];
    let agent = 0;
    if (user_agent.indexOf('iPhone') > -1 || user_agent.indexOf('iOS') > -1) {
      agent = 1;
    }
    const size = query.size || 20; // 安卓 120 iOS

    if (parseInt(page) === 0) {
      this.body = { code: 1, msg: 'faile', data: [] };
      return;
    }
    if (!userid) {
      return (this.body = { code: 0, msg: '获取成功', data: [{ page: `${page}`, size: `${size}`, count: '0' }] });
    }
    const result = yield phpController.getListByUseridNew({ userid, page, size, userid_, agent });
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取我关注的人的动态列表
const getFollowedList = router.get('/p/affair/followed/list', function* getFollowedList() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const page = query.page || 1; // 安卓 1 iOS
    const size = query.size || 20; // 安卓 120 iOS

    if (parseInt(page) === 0) {
      this.body = { code: 1, msg: 'faile', data: [] };
      return;
    }
    if (!userid) {
      return (this.body = { code: 0, msg: '获取成功', data: [{ page: `${page}`, size: `${size}`, count: '0' }] });
    }
    const result = yield phpController.getFollowedList({ userid, page, size });
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getFollowedListNew = router.get('/p/affair/followed/list/new', function* getFollowedListNew() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const lastId = query.lastId;

    if (!userid) {
      return (this.body = { code: 0, msg: '获取成功', data: [] });
    }
    const result = yield phpController.getFollowedListNew2(userid, lastId);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: '获取成功', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getShareContent: getShareContent,
  getAffairDetail: getAffairDetail,
  getAffairLikes: getAffairLikes,
  getAffairComments: getAffairComments,
  getListByUserid: getListByUserid,
  getFollowedList: getFollowedList,
  getFollowedListNew: getFollowedListNew,
};
