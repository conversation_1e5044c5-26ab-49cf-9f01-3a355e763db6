'use strict';

const router = require('koa-route');
const recommenController = require('../controller/recommen_controller_new2').recommenNewInstance;
const recommenNewController = require('../controller/recommen_controller_new').recommenNewInstance;

const getRecommen = router.get('/n/recommen/sheqi', function* getRecommen() {
  try {
    const userid = this.request._uid;
    const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;
    const result = yield recommenController.getRecommenNew(userid, TODAYTIME);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: 'faile', data: [] };
    console.log(err);
    return;
  }
});

const getRecommenNew = router.get('/n/recommen', function* getRecommen() {
  try {
    const userid = this.request._uid;
    const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;
    let result = null;
    // if (userid === 7) {
    //    console.log(`/n/recommen:::NEW>>>>>>${userid}`)
    //    result = yield recommenController.getRecommenNew(userid, TODAYTIME);
    // } else {
    //    result = yield recommenNewController.getRecommenNew(userid, TODAYTIME);
    // }
    result = yield recommenNewController.getRecommenNew(userid, TODAYTIME);

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: 'faile', data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getRecommen: getRecommen,
  getRecommenNew: getRecommenNew,
};
