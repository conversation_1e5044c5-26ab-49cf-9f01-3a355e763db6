'use strict';
const router = require('koa-route');
const BotController = require('../controller/bot_controller').botInstance;
const encrypt = require('../tools/crypto').encrypt;
const decipher = require('../tools/crypto').decipher;

/**
 * 1: 创建
 * 2: bot名
 */
const createBot = router.put('/n/bot/create', function* createBot() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    let step = body.step;
    if (step) {
      step = decipher(step);
      const user_id = parseInt(step.split(':')[1]);
      step = parseInt(step.split(':')[0]);
      if (userid !== user_id) {
        this.body = { code: 1, msg: '用户信息与步骤存在问题' };
        return;
      }
    }
    const key = body.key;

    const result = yield BotController.botFun(userid, step, key);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const botLogin = router.post('/bot/login', function* botLogin() {
  try {
    const token = this.request.token;
    const body = this.request.body;
    const url = body.url;
    const result = yield BotController.botLogin(token, url);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getUserInfo = router.get('/bot/user/info', function* getUserInfo(params) {
  try {
    const query = this.request.query;
    let userid = query.userid;
    if (!userid) {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }

    userid = parseInt(userid);

    const result = yield BotController.getUserInfoById(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  createBot,
  botLogin,
  getUserInfo,
};
