'use strict';

const router = require('koa-route');
const userController = require('../controller/user_controller').userInstance;
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;

//获取用户信息
const getUserInfo = router.get('/n/user/userinfo', function* getUserInfo() {
  try {
    const userid = this.request._uid;
    let result = null;

    result = yield userController.getUserInfo(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取用户信息
const getOtherUserInfo = router.get('/n/user/otherUserinfo', function* getOtherUserInfo() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const searchUserid = parseInt(query.userid);
    let result = null;

    result = yield userController.getOtherPersonInfo(userid, searchUserid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取im用户列表
const getImUserInfo = router.get('/n/user/imUserinfo', function* getImUserInfo() {
  try {
    const query = this.request.query;
    const userids = query.userids;
    if (!userids) {
      this.body = { code: 1, msg: '请填userids', data: [] };
      return;
    }
    const result = yield userController.getImUserInfo(userids);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//拉黑
const postUserBlack = router.post('/n/user/black', function* postUserBlack() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const blackUserid = body.blackUserid;
    if (!blackUserid) {
      this.body = { code: 1, msg: '请填blackUserid', data: [] };
      return;
    }
    if (userid == parseInt(blackUserid)) {
      this.body = { code: 1, msg: '无法拉黑自己', data: [] };
      return;
    }
    const result = yield userController.postUserBlack(userid, blackUserid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
//获取拉黑列表
const getUserBlack = router.get('/n/user/black', function* getUserBlack() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getUserBlack(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
//取消拉黑
const cancelUserBlack = router.post('/n/user/black/cancel', function* cancelUserBlack() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const blackUserid = body.blackUserid;
    if (!blackUserid) {
      this.body = { code: 1, msg: '请填blackUserid', data: [] };
      return;
    }
    const result = yield userController.cancelUserBlack(userid, blackUserid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取单个用户的拉黑状态
const getUserBlackStatus = router.get('/n/user/black/status', function* getUserBlackStatus() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const blackUserid = query.blackUserid;
    if (!blackUserid) {
      this.body = { code: 1, msg: '请填blackUserid', data: [] };
      return;
    }
    const result = yield userController.getUserBlackStatus(userid, blackUserid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ status: result['data'] }] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//保存用户信息接口
const updateUserInfo = router.post('/n/user/userinfo', function* updateUserInfo() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const birth = parseInt(body.birth);
    const nickname = body.nickname;
    const gender = parseInt(body.gender);
    let avatar = body.avatar;
    const signature = body.signature || '';
    if (!birth) {
      this.body = { code: 1, msg: '请填写生日', data: [] };
      return;
    }
    if (!nickname) {
      this.body = { code: 1, msg: '请填写昵称', data: [] };
      return;
    }
    // 检查昵称长度
    if (nickname.length > 8) {
      this.body = { code: 1, msg: '昵称长度不能超过8个字符', data: [] };
      return;
    }

    // 检查是否包含符号（使用正则表达式）
    // 只允许中文、英文字母、数字
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9]+$/.test(nickname)) {
      this.body = { code: 1, msg: '昵称只能包含中文、英文字母和数字', data: [] };
      return;
    }
    if (
      nickname.includes('52赫兹管理员') ||
      nickname.includes('五十二赫兹管理员') ||
      nickname === '52赫兹管理员' ||
      nickname === '五十二赫兹管理员'
    ) {
      this.body = { code: 1, msg: '昵称不合规', data: [] };
      return;
    }
    if (!gender) {
      this.body = { code: 1, msg: '请选择性别', data: [] };
      return;
    }
    if (!avatar) {
      this.body = { code: 1, msg: '请上传头像', data: [] };
      return;
    }
    if (!signature) {
      this.body = { code: 1, msg: '请填写个性描述', data: [] };
      return;
    }

    if (avatar.indexOf(WAILIAN) != -1) {
      avatar = avatar.replace(WAILIAN, '');
    }
    if (!avatar.length) {
      avatar = '52hz_avatar_new.png';
    }
    if (nickname.length > 20) {
      return (this.body = { code: 1, msg: '昵称应在20个字符以内', data: [] });
    }
    const data = {
      birth: birth,
      nickname: nickname,
      gender: gender,
      avatar: avatar,
      signature: signature,
      userid: parseInt(userid),
    };
    const result = yield userController.updateUserInfo(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取相近频率用户
const getSimilarUser = router.get('/n/user/similar', function* getSimilarUser() {
  try {
    const userid = this.request._uid;
    let result = null;
    // if (userid == 7 || userid == 11) {
    //     result = yield userController.getSimilarUserNew(userid);
    // } else {
    //     result = yield userController.getSimilarUser(userid);
    // }
    // result = yield userController.getSimilarUser(userid);
    result = yield userController.getSimilarUserNew(userid);

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取相近频率用户
const getSimilarUserNew = router.get('/n/user/similar/new', function* getSimilarUser() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getSimilarUserNew(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getFansAttentionContentCount = router.get('/n/user/faccount', function* getFansAttentionContentCount() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getFansAttentionContentCount(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const updateUserDeviceToken = router.post('/n/user/deviceToken', function* updateUserDeviceToken() {
  try {
    const userid = this.request._uid;
    const body = this.request.body;
    const deviceToken = body.deviceToken;
    if (!deviceToken) {
      this.body = { code: 1, msg: '请上传deviceToken', data: [] };
      return;
    }
    const data = {
      userid: userid,
      deviceToken: deviceToken,
    };
    const result = yield userController.updateUserDeviceToken(data);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const removeUserPushCount = router.post('/n/user/removePush', function* removeUserPushCount() {
  try {
    const userid = this.request._uid;
    // yield userController.removeUserPushCount(userid);
    this.body = { code: 0, msg: 'success', data: [] };
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

//获取用户是否被举报超三次 0:未超过 1:超过
const getReportResult = router.get('/n/user/reportrs', function* getReportResult() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getReportResult(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});
//获取用户与另一人的拉黑关系 0:未拉黑 1:拉黑
const getBlackResult = router.get('/n/user/blackrs', function* getBlackResult() {
  try {
    const userid = this.request._uid;
    const query = this.request.query;
    const to_userid = parseInt(query.to_userid);
    if (!to_userid) {
      this.body = { code: 1, msg: 'to_userid不得为空', data: [] };
      return;
    }
    const result = yield userController.getBlackResult(userid, to_userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

// 生成邀请码
const postUserInvitationCode = router.post('/n/user/invit/code', function* postUserInvitationCode() {
  try {
    const userid = this.request._uid;
    const result = yield userController.postUserInvitationCode(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const userVerify = router.get('/n/common/user/verify', function* userVerify() {
  try {
    const query = this.request.query;
    const phone = query.phone;
    if (!phone) {
      this.body = { code: 1, msg: '手机号码不得为空', data: [] };
      return;
    }
    const result = yield userController.userVerify(parseInt(phone));
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['verify']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const userVerifyQA = router.get('/n/common/user/verify/qa', function* userVerifyQA() {
  try {
    const query = this.request.query;
    const phone = query.phone;
    if (!phone) {
      this.body = { code: 1, msg: '手机号码不得为空', data: [] };
      return;
    }
    const result = yield userController.userVerifyQA(parseInt(phone));
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const postUserVerifyQA = router.post('/n/common/user/verify/qa', function* postUserVerifyQA() {
  try {
    const body = this.request.body;
    const qa = body.qa;
    const phone = body.phone;
    if (!qa) {
      this.body = { code: 1, msg: '答案不能为空', data: [] };
      return;
    }
    if (!phone) {
      this.body = { code: 1, msg: '手机号码不得为空', data: [] };
      return;
    }
    const result = yield userController.postUserVerifyQA(parseInt(phone), qa);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ message: result['message'] }] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const setAccount = router.post('/n/user/account', function* setAccount() {
  try {
    const body = this.request.body;
    const account = body.account;
    const userid = this.request._uid;
    if (!account) {
      this.body = { code: 1, msg: '请输入账号', data: [] };
      return;
    }
    // 用户名验证：
    // ^[a-zA-Z]          - 必须以字母开头
    // [a-zA-Z0-9_]{5,19}$ - 后面可以是字母数字下划线，总长度6-20
    // .*[a-zA-Z].*       - 必须包含至少一个字母
    const accountRegex = /^[a-zA-Z][a-zA-Z0-9_]{5,19}$/;
    const containsLetterRegex = /.*[a-zA-Z].*/;

    if (!accountRegex.test(account) || !containsLetterRegex.test(account)) {
      this.body = {
        code: 1,
        msg: '用户名不合规',
        data: [],
      };
      return;
    }
    const result = yield userController.setAccount(userid, account);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getPasswordStatus = router.get('/n/user/password/status', function* getPasswordStatus() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getPasswordStatus(userid);
    if (result && result['backSuccess']) {
      this.body = { code: result['code'], msg: result['msg'], data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const setPassword = router.post('/n/user/password', function* setPassword() {
  try {
    const body = this.request.body;
    const oldPassword = body.oldPwd;
    const newPassword = body.newPwd;
    const userid = this.request._uid;
    // if (!oldPassword) {
    //   this.body = { code: 1, msg: '请输入旧密码', data: [] };
    //   return;
    // }

    if (!newPassword) {
      this.body = { code: 1, msg: '请输入新密码', data: [] };
      return;
    }
    // 密码验证：
    // (?=.*[a-z])        - 必须包含小写字母
    // (?=.*[A-Z])        - 必须包含大写字母
    // (?=.*\d)           - 必须包含数字
    // (?=.*[,.!@#$%^&*]) - 必须包含英文标点符号
    // [a-zA-Z\d,.!@#$%^&*]{10,16} - 总长度10-16位
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[,.!@#$%^&*])[a-zA-Z\d,.!@#$%^&*]{10,16}$/;

    if (!passwordRegex.test(newPassword)) {
      this.body = {
        code: 1,
        msg: '密码不合规',
        data: [],
      };
      return;
    }

    const result = yield userController.setPassword(userid, newPassword, oldPassword);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getUserOnlineStatus = router.get('/n/user/online/status', function* getUserOnlineStatus() {
  try {
    const userid = this.request._uid;
    const result = yield userController.isUserOnline(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [{ status: result['status'] }] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getForgetMsgCode = router.get('/n/user/forget/code', function* getForgetMsgCode() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getForgetMsgCode(userid);

    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: '短信验证码获取频繁，请稍后再试', data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const validateCodeRouter = router.post('/n/user/validate_code', function* validateCodeRouter() {
  try {
    const body = this.request.body;
    const code = parseInt(body.code);
    const userid = this.request._uid;

    if (!code) {
      this.body = { code: 1, msg: '请输入验证码', data: [] };
      return;
    }

    const result = yield userController.validateCode(userid, code);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getUserAFL = router.get('/n/user/afl', function* getUserAFL() {
  try {
    const userid = this.request._uid;
    const result = yield userController.getUserAFL(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const clearUserNotice = router.post('/n/user/clear/notice', function* clearUserNotice() {
  try {
    const userid = this.request._uid;
    const result = yield userController.clearUserNotice(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const coCreation = router.get('/n/user/cocreation', function* coCreation() {
  try {
    const result = yield userController.coCreation();
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getSearchRecommenNew = router.get('/n/user/recommen/by/search', function* getSearchRecommenNew() {
  try {
    const userid = this.request._uid;

    const result = yield userController.getSearchRecommenNew({ userid, limit: 20 });

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: 'faile', data: [] };
    console.log(err);
    return;
  }
});

const getUnreadStatus = router.get('/n/user/unread/status', function* getUnreadStatus() {
  try {
    const userid = this.request._uid;

    const result = yield userController.getUnreadStatus(userid);

    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: 'faile', data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: 'faile', data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getUserInfo: getUserInfo,
  updateUserInfo: updateUserInfo,
  getSimilarUser: getSimilarUser,
  getOtherUserInfo: getOtherUserInfo,
  getImUserInfo: getImUserInfo,
  postUserBlack: postUserBlack,
  getUserBlack: getUserBlack,
  cancelUserBlack: cancelUserBlack,
  getUserBlackStatus: getUserBlackStatus,
  getFansAttentionContentCount: getFansAttentionContentCount,
  updateUserDeviceToken: updateUserDeviceToken,
  removeUserPushCount: removeUserPushCount,
  getSimilarUserNew: getSimilarUserNew,
  getReportResult: getReportResult,
  getBlackResult: getBlackResult,
  postUserInvitationCode: postUserInvitationCode,
  userVerify: userVerify,
  userVerifyQA: userVerifyQA,
  postUserVerifyQA: postUserVerifyQA,
  setPassword: setPassword,
  setAccount: setAccount,
  getPasswordStatus: getPasswordStatus,
  getUserOnlineStatus: getUserOnlineStatus,
  getForgetMsgCode: getForgetMsgCode,
  validateCodeRouter: validateCodeRouter,
  getUserAFL: getUserAFL,
  clearUserNotice: clearUserNotice,
  coCreation: coCreation,
  getSearchRecommenNew: getSearchRecommenNew,
  getUnreadStatus: getUnreadStatus,
};
