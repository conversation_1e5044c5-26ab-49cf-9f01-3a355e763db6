'use strict';
const router = require('koa-route');
const oceanController = require('../controller/ocean_controller').oceanInstance;
const oceanControllerNew = require('../controller/ocean_controller_new').oceanNewInstance;

const oceanGet = router.get('/52Hz/r/ocean', function* oceanGet() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const page = query.page || 1;
    const { hertz } = query;
    let result = null;
    // if (!hertz) {
    //    result = yield oceanControllerNew.getRecommenNew(userid);
    // } else {
    //    result = yield oceanController.getOceanFun(userid, hertz);
    // }
    result = yield oceanControllerNew.getRecommenNew2(hertz, page, userid);

    // result = yield oceanController.getOceanFun(userid, hertz);

    if (result && result['result']) {
      this.body = result['data'];
    } else {
      this.body = result['data'];
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const oceanGetNew = router.get('/52Hz/r/ocean/new', function* oceanGetNew() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const { hertz, lastId } = query;

    const lat = query.lat || 0;
    const lon = query.lon || 0;
    const location = query.location || 0;

    const result = yield oceanControllerNew.getRecommenNew3(hertz, userid, lastId, lat, lon, location);

    if (result && result['result']) {
      this.body = result['data'];
    } else {
      this.body = result['data'];
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const oceanGetV2 = router.get('/n/ocean', function* oceanGetV2() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const page = query.page || 1;
    const hertz = query.hertz;
    if (!hertz) {
      this.body = { code: 1, msg: 'hertz can ont be null', data: [] };
      return;
    }
    const result = yield oceanController.getOceanFunV2(userid, page, hertz);
    if (result && result['result']) {
      this.body = result['data'];
    } else {
      this.body = result['data'];
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  oceanGet: oceanGet,
  oceanGetV2: oceanGetV2,
  oceanGetNew: oceanGetNew,
};
