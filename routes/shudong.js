'use strict';

const router = require('koa-route');
const shudongController = require('../controller/shudong').shudongInstance;

//获取树洞列表
const getShuDongList = router.get('/n/shudong/list', function* getShuDongList() {
  try {
    const query = this.request.query;
    const page = query.page || 1;
    const size = query.size || 10;

    const result = yield shudongController.getList(page, size);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const addShudong = router.post('/n/shudong', function* addShudong() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const msg = body.message;
    if (!msg) {
      return (this.body = { code: 1, msg: '内容不能为空', data: [] });
    }
    const result = yield shudongController.addShudong(userid, msg);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const addLiuyan = router.post('/n/liuyan', function* addLiuyan() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const msg = body.message;
    const to = body.to;
    if (!msg) {
      return (this.body = { code: 1, msg: 'message不得为空', data: [] });
    }
    if (!to) {
      return (this.body = { code: 1, msg: 'to不得为空', data: [] });
    }
    const result = yield shudongController.addLiuyan(userid, to, msg);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: result['data'] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const getLiuyanList = router.get('/n/liuyan/list', function* getLiuyanList() {
  try {
    const query = this.request.query;
    const userid = this.request._uid;
    const page = query.page || 1;
    const size = query.size || 10;

    const result = yield shudongController.getLiuyanList(userid, page, size);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const readLiuyan = router.post('/n/liuyan/read', function* readLiuyan() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const lid = body.lid;
    if (!lid) {
      return (this.body = { code: 1, msg: 'lid不得为空', data: [] });
    }

    const result = yield shudongController.readLiuyan(userid, lid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const unreadCount = router.get('/n/liuyan/unread', function* readLiuyan() {
  try {
    const userid = this.request._uid;
    const result = yield shudongController.unreadCount(userid);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [result['data']] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const deleteLiuyan = router.post('/n/liuyan/delete', function* deleteLiuyan() {
  try {
    const body = this.request.body;
    const userid = this.request._uid;
    const lids = body.lids;
    if (!lids) {
      return (this.body = { code: 1, msg: 'lids不得为空', data: [] });
    }
    const result = yield shudongController.deleteLiuyan(userid, lids);
    if (result && result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
    return;
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

const clearLiuyan = router.post('/n/user/clear/liuyan', function* clearLiuyan() {
  try {
    const userid = this.request._uid;
    const result = yield shudongController.clearUserLiuyan(userid);
    if (result['backSuccess']) {
      this.body = { code: 0, msg: 'success', data: [] };
    } else {
      this.body = { code: 1, msg: result['msg'], data: [] };
    }
  } catch (err) {
    this.body = { code: 1, msg: err, data: [] };
    console.log(err);
    return;
  }
});

module.exports = {
  getShuDongList: getShuDongList,
  addShudong: addShudong,
  addLiuyan: addLiuyan,
  getLiuyanList: getLiuyanList,
  readLiuyan: readLiuyan,
  unreadCount: unreadCount,
  deleteLiuyan: deleteLiuyan,
  clearLiuyan: clearLiuyan,
};
