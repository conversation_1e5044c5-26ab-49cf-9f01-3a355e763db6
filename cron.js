'use strict';

const log4js = require('log4js');

const logConfig = {
  appenders: {
    cronLogs: { type: 'file', filename: 'logs/cron.log' },
    console: { type: 'console' },
  },
  categories: {
    cron: { appenders: ['console', 'cronLogs'], level: 'error' },
    default: { appenders: ['console'], level: 'trace' },
  },
  pm2: true,
  pm2InstanceVar: 'INSTANCE_ID',
};

log4js.configure(logConfig);
global.elogger = log4js.getLogger('cron');
global.slogger = log4js.getLogger('');

slogger.info('定时任务服务启动');

require('./cron/del_shudong');
require('./cron/content_timing');

process.stdin.resume();
