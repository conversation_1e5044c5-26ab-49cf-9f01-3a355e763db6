{"name": "52hz", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js", "cron": "node cron.js", "fix-memory-limit": "cross-env LIMIT=2048 increase-memory-limit"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"co-body": "^4.2.0", "gm": "*", "increase-memory-limit": "^1.0.6", "istanbul": "^0.4.5", "koa": "^1.2.1", "koa-bodyparser": "^2.2.0", "koa-gzip": "^0.1.0", "koa-jsonp": "^2.0.2", "koa-route": "^2.4.2", "koa-static": "^2.0.0", "mysql": "^2.11.1", "redis": "^2.8.0", "request": "^2.74.0", "supertest": "^2.0.0", "thunkify": "*", "underscore": "^1.8.3"}, "dependencies": {"@alicloud/ots2": "^1.5.3", "@alicloud/sms-sdk": "*", "@apla/clickhouse": "^1.6.4", "@sentry/node": "^5.15.2", "ali-oss": "^4.0.1", "aliyun-sdk": "^1.10.12", "async": "^2.0.1", "co": "^4.6.0", "co-redis": "^2.1.1", "crypto-js": "^4.0.0", "fingerprint-container-node-sdk": "^0.1.11", "getmac": "^1.2.1", "hbase": "^0.6.0", "hbase-client": "^1.6.1", "iap": "^1.1.1", "install": "^0.13.0", "ioredis": "^2.4.3", "json-bigint": "^0.2.3", "koa-bodyparser": "^2.2.0", "koa-cors": "0.0.16", "koa-gzip": "^0.1.0", "koa-jsonp": "^2.0.2", "koa-sslify": "^2.1.2", "leancloud-realtime": "^3.5.7", "leancloud-realtime-plugin-typed-messages": "^3.0.0", "leancloud-storage": "^3.4.2", "log4js": "^2.3.3", "moment": "^2.17.1", "node-schedule": "^1.3.0", "npm": "^6.14.10", "pwd": "^1.1.0", "qiniu": "^7.2.1", "qr-image": "^3.2.0", "request": "^2.81.0", "request-promise": "^4.2.6", "swagger-jsdoc": "^3.2.3", "tls-sig-api": "^1.0.1", "urlencode": "^1.1.0", "utf8": "^3.0.0"}}