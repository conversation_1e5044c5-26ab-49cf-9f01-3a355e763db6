var mysql = require('mysql');
const { DefaultConfig } = require('../config/default');
const mysqlSettings = DefaultConfig.mysqlSettings;
const mysqlSettingsDev = DefaultConfig.mysqlSettingsDev;
const CACHE_CONFIG = DefaultConfig.CACHE_CONFIG;
const crypto = require('crypto');
const redisInstance = require('../models/redis').redisInstance;

function handleDisconnect() {
  return mysql.createPool({
    ...mysqlSettings,
    connectionLimit: 10,
    queueLimit: 0,
    waitForConnections: true,
    acquireTimeout: 10000,
  });
}

function Mysql() {
  const clientObj = handleDisconnect();
  const executeQuery = (sql, log = false) => {
    if (log) {
      console.log(sql);
    }
    return new Promise((resolve, reject) => {
      clientObj.query(sql, (err, rows) => {
        if (err) {
          console.log(`SQL Error: ${err.message}, SQL: ${sql}`);
          resolve({
            type: 'error',
            msg: err.message,
            sql: sql,
          });
          return;
        }
        if (sql.trim().toLowerCase().startsWith('insert')) {
          resolve(rows.insertId);
        } else {
          resolve(rows);
        }
      });
    });
  };

  // const executeQuery = (sql, log = false) => {
  //   if (log) {
  //     console.log(sql);
  //   }

  //   return new Promise((resolve, reject) => {
  //     if (sql.trim().toLowerCase().startsWith('select')) {
  //       const explainSql = `EXPLAIN ${sql}`;
  //       clientObj.query(explainSql, (explainErr, explainRows) => {
  //         if (!explainErr && Array.isArray(explainRows)) {
  //           explainRows.forEach((row) => {
  //             const type = row.type;
  //             const key = row.key;
  //             const rows = row.rows;
  //             const extra = row.Extra || '';

  //             if (type === 'ALL') {
  //               console.warn(`【索引警告】查询执行全表扫描: ${sql}`);
  //               console.warn(`表: ${row.table}, 扫描行数: ${rows}`);
  //             } else if (type === 'index' && rows > 100) {
  //               console.warn(`【索引警告】查询使用全索引扫描且行数较多: ${sql}`);
  //               console.warn(`表: ${row.table}, 索引: ${key}, 扫描行数: ${rows}`);
  //             } else if (!key && rows > 50) {
  //               console.warn(`【索引警告】查询未使用索引且扫描行数较多: ${sql}`);
  //               console.warn(`表: ${row.table}, 扫描行数: ${rows}`);
  //             } else if (extra.includes('Using filesort') && rows > 100) {
  //               console.warn(`【索引警告】查询需要文件排序: ${sql}`);
  //               console.warn(`表: ${row.table}, 索引: ${key}, 扫描行数: ${rows}`);
  //             } else if (extra.includes('Using temporary')) {
  //               console.warn(`【索引警告】查询需要临时表: ${sql}`);
  //               console.warn(`表: ${row.table}, 索引: ${key}, 扫描行数: ${rows}`);
  //             }

  //             // if (type === 'ALL' || !key || extra.includes('Using filesort') || extra.includes('Using temporary')) {
  //             //   console.warn('执行计划:', JSON.stringify(row, null, 2));
  //             // }
  //           });
  //         }

  //         executeActualQuery();
  //       });
  //     } else {
  //       executeActualQuery();
  //     }

  //     function executeActualQuery() {
  //       clientObj.query(sql, (err, rows) => {
  //         if (err) {
  //           console.log(`SQL Error: ${err.message}, SQL: ${sql}`);
  //           resolve({
  //             type: 'error',
  //             msg: err.message,
  //             sql: sql,
  //           });
  //           return;
  //         }
  //         if (sql.trim().toLowerCase().startsWith('insert')) {
  //           resolve(rows.insertId);
  //         } else {
  //           resolve(rows);
  //         }
  //       });
  //     }
  //   });
  // };

  // // 解析SQL获取表名
  // function getTableNameFromSQL(sql) {
  //   const normalized = sql.toLowerCase().trim();
  //   let tableName = '';

  //   if (normalized.startsWith('select')) {
  //     const fromIndex = normalized.indexOf(' from ');
  //     if (fromIndex !== -1) {
  //       const afterFrom = normalized.substring(fromIndex + 6);
  //       tableName = afterFrom.split(/[\s,]/)[0];
  //     }
  //   } else if (normalized.startsWith('insert')) {
  //     const match = normalized.match(/insert\s+into\s+([^\s(]+)/i);
  //     tableName = match ? match[1] : '';
  //   } else if (normalized.startsWith('update')) {
  //     const match = normalized.match(/update\s+([^\s]+)/i);
  //     tableName = match ? match[1] : '';
  //   } else if (normalized.startsWith('delete')) {
  //     const match = normalized.match(/delete\s+from\s+([^\s]+)/i);
  //     tableName = match ? match[1] : '';
  //   }

  //   return tableName.replace(/`/g, '').trim();
  // }

  // // 生成缓存key
  // function generateCacheKey(sql) {
  //   return `${CACHE_CONFIG.prefix}${getTableNameFromSQL(sql)}:${crypto.createHash('md5').update(sql).digest('hex')}`;
  // }

  // // 清除表相关的所有缓存
  // async function clearTableCache(tableName) {
  //   const pattern = `${CACHE_CONFIG.prefix}${tableName}:*`;
  //   const keys = await redisInstance.keys(pattern);
  //   if (keys.length > 0) {
  //     await Promise.all(keys.map((key) => redisInstance.delKey(key)));
  //   }
  // }

  // // 查询分析
  // async function analyzeQuery(sql) {
  //   const explainSql = `EXPLAIN ${sql}`;
  //   const explainRows = await executeActualQuery(explainSql);

  //   let shouldCache = true;
  //   if (Array.isArray(explainRows)) {
  //     for (const row of explainRows) {
  //       // 不缓存可能返回大量数据的查询
  //       if (row.rows > 10000 || row.type === 'ALL') {
  //         shouldCache = false;
  //         break;
  //       }
  //     }
  //   }
  //   return { shouldCache };
  // }

  // const analyzenQueryV2 = (sql) => {
  //   return new Promise((resolve, reject) => {
  //     if (sql.trim().toLowerCase().startsWith('select')) {
  //       const explainSql = `EXPLAIN ${sql}`;
  //       clientObj.query(explainSql, (explainErr, explainRows) => {
  //         if (!explainErr && Array.isArray(explainRows)) {
  //           explainRows.forEach((row) => {
  //             const type = row.type;
  //             const key = row.key;
  //             const rows = row.rows;
  //             const extra = row.Extra || '';

  //             if (type === 'ALL') {
  //               console.warn(`【索引警告】查询执行全表扫描: ${sql}`);
  //               console.warn(`表: ${row.table}, 扫描行数: ${rows}`);
  //             } else if (type === 'index' && rows > 100) {
  //               console.warn(`【索引警告】查询使用全索引扫描且行数较多: ${sql}`);
  //               console.warn(`表: ${row.table}, 索引: ${key}, 扫描行数: ${rows}`);
  //             } else if (!key && rows > 50) {
  //               console.warn(`【索引警告】查询未使用索引且扫描行数较多: ${sql}`);
  //               console.warn(`表: ${row.table}, 扫描行数: ${rows}`);
  //             } else if (extra.includes('Using filesort') && rows > 100) {
  //               console.warn(`【索引警告】查询需要文件排序: ${sql}`);
  //               console.warn(`表: ${row.table}, 索引: ${key}, 扫描行数: ${rows}`);
  //             } else if (extra.includes('Using temporary')) {
  //               console.warn(`【索引警告】查询需要临时表: ${sql}`);
  //               console.warn(`表: ${row.table}, 索引: ${key}, 扫描行数: ${rows}`);
  //             }
  //           });
  //         }
  //       });
  //     }
  //   });
  // };

  // // 实际执行查询
  // function executeActualQuery(sql) {
  //   return new Promise((resolve, reject) => {
  //     clientObj.query(sql, (err, rows) => {
  //       if (err) {
  //         console.log(`SQL Error: ${err.message}, SQL: ${sql}`);
  //         resolve({
  //           type: 'error',
  //           msg: err.message,
  //           sql: sql,
  //         });
  //         return;
  //       }
  //       if (sql.trim().toLowerCase().startsWith('insert')) {
  //         resolve(rows.insertId);
  //       } else {
  //         resolve(rows);
  //       }
  //     });
  //   });
  // }

  // const executeQuery = (sql, log = false) => {
  //   if (log) {
  //     console.log(sql);
  //   }

  //   return new Promise(async (resolve, reject) => {
  //     const sqlType = sql.trim().toLowerCase();
  //     const tableName = getTableNameFromSQL(sql);

  //     // 处理非SELECT查询
  //     if (!sqlType.startsWith('select')) {
  //       const result = await executeActualQuery(sql);

  //       // 如果是修改数据的操作,清除相关表的缓存
  //       if (
  //         tableName &&
  //         (sqlType.startsWith('insert') || sqlType.startsWith('update') || sqlType.startsWith('delete'))
  //       ) {
  //         await clearTableCache(tableName);
  //       }

  //       return resolve(result);
  //     }

  //     // 处理SELECT查询
  //     if (CACHE_CONFIG.enabled && tableName) {
  //       const cacheKey = generateCacheKey(sql);

  //       // 尝试从缓存获取
  //       const cachedResult = await redisInstance.get(cacheKey);
  //       if (cachedResult) {
  //         return resolve(JSON.parse(cachedResult));
  //       }

  //       // 执行查询并缓存结果
  //       await analyzenQueryV2(sql);
  //       const result = await executeActualQuery(sql);
  //       if (!result.type || result.type !== 'error') {
  //         await redisInstance.set(cacheKey, JSON.stringify(result), CACHE_CONFIG.expire);
  //       }
  //       return resolve(result);
  //     }

  //     // 直接执行查询
  //     return resolve(await executeActualQuery(sql));
  //   });
  // };

  this.query = function (sql) {
    return new Promise((resolve, reject) => {
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAttribute = function (type) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM attribute_tags WHERE type = ' + type;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserCreateTime = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT create_time, colorFont FROM user WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRank = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT COUNT(id) AS rank FROM user WHERE id <= ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getInvitationCodeFun = function (code) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM invitation WHERE invitation = "' + code + '"';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getInvitationCodeFunV3 = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_invitation_code WHERE userid = ${userid} AND status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateInvitationCodeFun = function (code) {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE invitation SET num = num + 1 WHERE invitation = "' + code + '"';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.insertInvitationCodeFun = function (code) {
    return new Promise((resolve, reject) => {
      const sql = 'INSERT INTO invitation (invitation) VALUES ("' + code + '")';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserExit = function (mobile) {
    return new Promise((resolve, reject) => {
      var sql = 'SELECT * FROM user WHERE mobile = ' + mobile + ' AND state != 1';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserAndAccountExit = function (mobile) {
    return new Promise((resolve, reject) => {
      var sql =
        'SELECT b.account, a.* FROM user a LEFT JOIN accounts b ON a.id = b.user_id WHERE a.mobile = ' +
        mobile +
        ' AND a.state != 1';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserExitByUnionid = function (unionid) {
    return new Promise((resolve, reject) => {
      var sql = 'SELECT * FROM user WHERE unionid = "' + unionid + '" AND state != 1';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserStatus = function (userid, status) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET status = ${status} WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateSSID = function (ssid, userid) {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE user set ssid = "' + ssid + '" WHERE id = ' + userid;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.mobileRegisteredFun = function (args) {
    return new Promise(function (resolve, reject) {
      const createtime = args['createtime'];
      const mobile = args['mobile'];
      const invitationCode = args['invitationCode'] || null;
      let sql = null;
      if (args['ipStatus']) {
        let country = args['country'],
          province = args['province'],
          city = args['city'];

        if (country == 'null' || province == 'null' || city == 'null') {
          const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
          const index = Math.floor(Math.random() * locationArr.length);
          const location_ = locationArr[index];
          country = location_;
          province = location_;
          city = location_;
        }
        sql =
          'INSERT INTO `user` (mobile, birth, create_time, type, country, province, city, invitationCode) VALUES (' +
          mobile +
          ', ' +
          createtime +
          ', ' +
          createtime +
          ', 1, "' +
          country +
          '", "' +
          province +
          '", "' +
          city +
          '", "' +
          invitationCode +
          '")';
      } else {
        sql =
          'INSERT INTO `user` (mobile, birth, create_time, type, invitationCode) VALUES (' +
          mobile +
          ', ' +
          createtime +
          ', ' +
          createtime +
          ', 1, "' +
          invitationCode +
          '")';
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.wxRegisteredFun = function (args) {
    return new Promise(function (resolve, reject) {
      const createtime = args['createtime'];
      const invitationCode = args['invitationCode'] || null;
      let city = args['city'],
        province = args['province'],
        country = args['country'],
        unionid = args['unionid'];
      if (country == 'null' || province == 'null' || city == 'null') {
        const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
        const index = Math.floor(Math.random() * locationArr.length);
        const location_ = locationArr[index];
        country = location_;
        province = location_;
        city = location_;
      }
      const sql =
        'INSERT INTO `user` (city, province, country, create_time, type, unionid, invitationCode) VALUES ("' +
        city +
        '",  "' +
        province +
        '", "' +
        country +
        '", ' +
        createtime +
        ', 2, "' +
        unionid +
        '", "' +
        invitationCode +
        '")';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfoByOpenid = function (openid) {
    return new Promise(function (resolve, reject) {
      var sql = 'SELECT * FROM `user` WHERE openid = "' + openid + '"';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchAttentionByUserid1 = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      var sql = 'SELECT * FROM attention WHERE userid1 = ' + userid1 + ' AND userid2 = ' + userid2;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchAttentionByUserid2 = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      var sql = 'SELECT * FROM attention WHERE userid1 = ' + userid2 + ' AND userid2 = ' + userid1;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addAttentionFun1 = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      var sql =
        'INSERT INTO attention (userid1, userid2, type, create_time) VALUES (' +
        userid1 +
        ', ' +
        userid2 +
        ', 1, ' +
        time +
        ');';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addAttentionFun2 = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      var sql =
        'INSERT INTO attention (userid1, userid2, type, create_time) VALUES (' +
        userid1 +
        ', ' +
        userid2 +
        ', 2, ' +
        time +
        ');';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addAttentionFun2_ = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      var sql =
        'UPDATE attention SET type = 2, update_time = ' +
        time +
        ' WHERE userid1 = ' +
        userid2 +
        ' AND userid2 = ' +
        userid1;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.releaseAttentionFun1 = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      var sql = 'DELETE FROM attention WHERE userid1 = ' + userid1 + ' AND userid2 = ' + userid2;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.releaseAttentionFun2 = function (args) {
    return new Promise(function (resolve, reject) {
      let userid1 = args['userid1'];
      let userid2 = args['userid2'];
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      var sql =
        'UPDATE attention SET type = 1, update_time = ' +
        time +
        ' WHERE userid1 = ' +
        userid2 +
        ' AND userid2 = ' +
        userid1;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFansByUserid = function (userid, lastId) {
    return new Promise(function (resolve, reject) {
      let sql = '';
      if (lastId) {
        sql = `SELECT a.id AS aid, a.type, b.nickname, b.avatar, b.signature, b.id, a.create_time, a.update_time FROM attention a, user b WHERE a.userid2 = ${userid} AND a.userid1 = b.id AND a.id < ${lastId} order by a.id DESC LIMIT 20`;
      } else {
        sql = `SELECT a.id AS aid, a.type, b.nickname, b.avatar, b.signature, b.id, a.create_time, a.update_time FROM attention a, user b WHERE a.userid2 = ${userid} AND a.userid1 = b.id order by a.id DESC LIMIT 20`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.newGetFansByUserid = function (userid, page, size) {
    return new Promise(function (resolve, reject) {
      var sql = `SELECT a.type, b.nickname, b.avatar, b.signature, b.id FROM attention a, user b WHERE a.userid2 = ${userid} AND a.userid1 = b.id ORDER BY a.id ASC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFansCount = function (userid) {
    return new Promise((resolve, reject) => {
      var sql = `SELECT COUNT(id) AS count FROM attention WHERE userid2 = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAttentionByUserid = function (userid, lastId) {
    return new Promise(function (resolve, reject) {
      let sql = '';
      if (lastId) {
        sql = `SELECT a.id AS aid, a.type, b.nickname, b.avatar, b.signature, b.id, a.create_time, a.update_time FROM attention a, user b WHERE a.userid1 = ${userid} AND a.userid2 = b.id AND a.id < ${lastId} order by a.id DESC LIMIT 20`;
      } else {
        sql = `SELECT a.id AS aid, a.type, b.nickname, b.avatar, b.signature, b.id, a.create_time, a.update_time FROM attention a, user b WHERE a.userid1 = ${userid} AND a.userid2 = b.id order by a.id DESC LIMIT 20`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFriends = function (userid, page, keyword) {
    return new Promise(function (resolve, reject) {
      let sql = '';
      if (keyword) {
        sql = `SELECT a.id AS aid, a.type, b.nickname, b.avatar, b.signature, b.id, a.create_time, a.update_time FROM attention a, user b WHERE a.userid1 = ${userid} AND a.userid2 = b.id AND a.type = 2 AND b.nickname LIKE '%${keyword}%' order by b.nickname ASC LIMIT 20 OFFSET ${
          (page - 1) * 20
        }`;
      } else {
        sql = `SELECT a.id AS aid, a.type, b.nickname, b.avatar, b.signature, b.id, a.create_time, a.update_time FROM attention a, user b WHERE a.userid1 = ${userid} AND a.userid2 = b.id AND a.type = 2 order by b.nickname ASC LIMIT 20 OFFSET ${
          (page - 1) * 20
        }`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.newGetAttentionByUserid = function (userid, page, size) {
    return new Promise(function (resolve, reject) {
      var sql = `SELECT a.type, b.nickname, b.avatar, b.signature, b.id FROM attention a, user b WHERE a.userid1 = ${userid} AND a.userid2 = b.id ORDER BY a.id ASC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAttentionCount = function (userid) {
    return new Promise((resolve, reject) => {
      var sql = `SELECT COUNT(id) AS count FROM attention WHERE userid1 = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFriendsCount = function (userid) {
    return new Promise((resolve, reject) => {
      var sql = `SELECT COUNT(id) AS count FROM attention WHERE userid1 = ${userid} AND type = 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getTagAttention = function (userid, tagid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT status FROM tag_attention WHERE userid = ${userid} AND tagid = ${tagid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateTagAttentionFun = function (userid, tagid, status) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE tag_attention SET status = ${status}, create_time = ${time} WHERE userid = ${userid} AND tagid = ${tagid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllTagAttention = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.name, a.slogan, b.tagid FROM content_tag_type a, tag_attention b WHERE b.userid = ${userid} AND b.status = 1 AND a.id = b.tagid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addTagAttentionRs = function (userid, tagid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO tag_attention (userid, tagid, create_time) VALUES (${userid}, ${tagid}, ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfo = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.*, b.tagArr, b.likeTagArr, b.attribute FROM user a, user_tag b WHERE a.id = ${userid} AND a.id = b.userid`;
      // const sql = `SELECT a.*, b.tagArr, b.likeTagArr, b.attribute FROM user a LEFT JOIN user_tag b ON a.id = b.userid WHERE a.id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getCodeRemainTime = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT create_time FROM user WHERE id = ${userid}`;
      clientObj.query(sql, function (err, rows, fields) {
        if (!err) {
          let create_time = rows[0]['create_time'];
          create_time = parseInt(new Date(create_time * 1000).setHours(0, 0, 0, 0) / 1000);
          const now = parseInt(new Date().setHours(0, 0, 0, 0) / 1000);
          const day = 7 - (now - create_time) / (60 * 60 * 24) >= 0 ? 7 - (now - create_time) / (60 * 60 * 24) : 0;
          resolve(day);
        } else {
          resolve({
            type: 'error',
            msg: err,
            sql: sql,
          });
        }
      });
    });
  };

  this.getCodeStatus = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user_invitation_code WHERE userid = ${userid} AND status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getImUserInfo = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT avatar ,nickname, id FROM user WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getImUserInfoV2 = function (arr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT avatar ,nickname, id FROM user WHERE id IN (${arr.toString()})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserRank = function (userid, rank) {
    return new Promise((resolve, reject) => {
      rank = parseInt(rank);
      const sql = `UPDATE user SET rank = ${rank} WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfoByUserid = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserAndAccountInfoByUserid = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.account, a.* FROM user a LEFT JOIN accounts b ON a.id = b.user_id WHERE a.id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfoByMobileV2 = function (phone) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE mobile = ${phone}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserSpecialByUserid = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT special FROM user WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentCount = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT COUNT(id) AS count FROM content WHERE userid = ' + userid + ' AND status = 0';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.insertIntoNotification = function (args) {
    return new Promise((resolve, reject) => {
      let sql = null;
      const userid = args['userid'],
        text = args['text'],
        avatar = args['avatar'],
        nickname = args['nickname'],
        type = args['type'],
        time = parseInt(Math.round(new Date().getTime() / 1000));
      if (type == 1 || type == 2) {
        const contentid = args['contentid'];
        const content = args['content'];
        const images = args['images'];
        sql = `INSERT INTO notification (userid, create_time, text, status, avatar, nickname, type, contentid, content, images) VALUES 
                (${userid}, ${time}, "${text}", 0, "${avatar}", "${nickname}", ${type}, ${contentid}, "${content}", "${images}")`;
      } else if (type == 3) {
        const attention_userid = args['attention_userid'];
        sql = `INSERT INTO notification (userid, create_time, text, status, avatar, nickname, type, attention_userid) VALUES 
                (${userid}, ${time}, "${text}", 0, "${avatar}", "${nickname}", ${type}, ${attention_userid})`;
      } else if (type == 4) {
        const discussId = args['discussId'];
        sql = `INSERT INTO notification (userid, create_time, text, status, avatar, nickname, type, discussid) VALUES 
                (${userid}, ${time}, "${text}", 0, "${avatar}", "${nickname}", ${type}, ${discussId})`;
      } else if (type == 5) {
        const weight = args['weight'];
        const to_user_id = args['to_user_id'];
        sql = `INSERT INTO notification (userid, create_time, text, status, avatar, nickname, type, weight, to_user_id) VALUES 
                (${userid}, ${time}, "${text}", 0, "${avatar}", "${nickname}", ${type}, ${weight}, ${to_user_id})`;
      } else if (type == 6) {
        const payid = args['payid'];
        sql = `INSERT INTO notification (userid, create_time, text, status, avatar, nickname, type, payid) VALUES 
                (${userid}, ${time}, "${text}", 0, "${avatar}", "${nickname}", ${type}, ${payid})`;
      } else if (type == 7) {
        const contentid = args['contentid'];
        const content = args['content'];
        const images = args['images'];
        sql = `INSERT INTO notification (userid, create_time, text, status, avatar, nickname, type, contentid, content, images) VALUES 
                (${userid}, ${time}, "${text}", 0, "${avatar}", "${nickname}", ${type}, ${contentid}, "${content}", "${images}")`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateAllNotificeation = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE notification SET status = 1 WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfoByMobile = function (mobile) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.*, b.tagArr, b.likeTagArr FROM user a, user_tag b WHERE a.mobile = ${mobile} AND a.id = b.userid`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfoByUserid2 = function (user_id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.*, b.tagArr, b.likeTagArr FROM user a, user_tag b WHERE a.id = ${user_id} AND a.id = b.userid`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addUserVerify = function (userid, question) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO user_verify (userid, question) VALUES (${userid}, '${question}')`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserVerifyAnwser = function (userid, anwser) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user_verify SET anwser = '${anwser}' WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserVerifyFrequency = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user_verify SET frequency = frequency + 1 WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserVerify = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_verify WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByMobile = function (mobile) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE mobile = ${mobile}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFree = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM free_active where status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAccountByAccount = function (account) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM accounts WHERE account = '${account}'`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAccountByUserid = function (user_id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM accounts WHERE user_id = ${user_id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.setAccount = function (userid, account) {
    const time = parseInt(Math.round(new Date().getTime() / 1000));
    return new Promise((resolve, reject) => {
      const sql = `UPDATE accounts SET account = '${account}', updated = ${time} WHERE user_id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addAccount = function (userid, account) {
    const time = parseInt(Math.round(new Date().getTime() / 1000));
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO accounts (user_id, account, created, updated) VALUES (${userid}, '${account}', ${time}, ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.setPassword = function (user_id, password, salt) {
    const time = parseInt(Math.round(new Date().getTime() / 1000));
    return new Promise((resolve, reject) => {
      let sql = null;
      if (!password && !salt) {
        sql = `UPDATE accounts SET password = null, salt = null, updated = ${time} WHERE user_id = ${user_id}`;
      } else {
        sql = `UPDATE accounts SET password = '${password}', salt = '${salt}', updated = ${time} WHERE user_id = ${user_id}`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByAccountAndPassword = function (account, password) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM accounts WHERE account = '${account}' AND password = '${password}'`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByUseridAndPassword = function (userid, password) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM accounts WHERE user_id = ${userid} AND password = '${password}'`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInfoByUnionid = function (unionid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.*, b.tagArr, b.likeTagArr FROM user a, user_tag b WHERE a.unionid = "${unionid}" AND a.id = b.userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByUnionid = function (unionid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE unionid = "${unionid}"`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserInfo = function (args) {
    return new Promise((resolve, reject) => {
      const birth = args['birth'],
        nickname = args['nickname'],
        gender = args['gender'],
        avatar = args['avatar'],
        signature = args['signature'],
        userid = args['userid'];

      const sql = `UPDATE user SET birth = ${birth}, nickname = '${nickname}', gender = ${gender}, avatar = '${avatar}', signature = '${signature}', verify = 1 WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getSimilarUserByVaryHertz = function (hz_max, hz_min, userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000) - 2592000);
      // const sql = 'SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM `user` a, user_tag b WHERE a.vary_hertz >= ' + hz_min + ' AND a.vary_hertz <= ' + hz_max + ' AND a.id != ' + userid + ' AND a.id = b.userid AND a.contentState != 0';
      const sql = `SELECT f.* FROM (SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM user a, user_tag b WHERE a.vary_hertz >= ${hz_min} AND a.vary_hertz <= ${hz_max} AND a.id != ${userid} AND a.id = b.userid AND a.contentState != 0) f, (SELECT COUNT(id) AS count, authorid FROM content_like GROUP BY authorid) e ,(SELECT userid FROM content WHERE create_time > ${time} GROUP BY userid) k WHERE f.id = e.authorid AND e.count > 5 AND f.id = k.userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getSimilarUserByLikeTag = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = '';
    });
  };

  this.getRandomUser = function (userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000) - 2592000);
      const sql =
        'SELECT k.id, k.nickname, k.avatar, k.hertz, k.gender, k.signature, k.birth, k.city, k.likeTagArr, k.tagArr FROM (SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM `user` a, user_tag b WHERE a.id >= ((SELECT MAX(id) FROM `user` where id > 99999)-(SELECT MIN(id) FROM `user` where id > 99999)) * RAND() + (SELECT MIN(id) FROM `user` where id > 99999) AND a.id = b.userid AND a.id != ' +
        userid +
        ') k, (SELECT userid FROM content WHERE create_time > ' +
        time +
        ' GROUP BY userid) n WHERE k.id = n.userid LIMIT 1000';
      // const sql = `SELECT e.* FROM (SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM user a, user_tag b WHERE a.id >= ((SELECT MAX(id) FROM user where id > 99999)-(SELECT MIN(id) FROM user where id > 99999)) * RAND() + (SELECT MIN(id) FROM user where id > 99999) AND a.id = b.userid AND a.id != ${userid} LIMIT 100) e, (SELECT COUNT(id) AS count, authorid FROM content_like GROUP BY authorid) f WHERE e.id = f.authorid AND f.count > 5`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRandomUser2 = function (userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000) - 2592000);
      // const sql = `SELECT k.* FROM (SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM user a, user_tag b WHERE a.id != ${userid} AND a.id = b.userid AND a.contentState = 1 ORDER BY RAND() LIMIT 500) k`
      const sql = `SELECT h.* FROM (SELECT k.* FROM (SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM user a, user_tag b WHERE a.id != ${userid} AND a.id = b.userid AND a.contentState = 1 ORDER BY RAND() LIMIT 500) k) h, (SELECT userid FROM content WHERE create_time > ${time} GROUP BY userid) j WHERE h.id = j.userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUsersig = function (userid, sig) {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE user SET usersig = "' + sig + '" WHERE id = ' + userid;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getGuideMapStatus = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT id FROM guide_map WHERE userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateGuideMapStatus = function (userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO guide_map (userid, create_time) VALUES (${userid}, ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getSimilarUserByHertz = function (hz_max, hz_min, userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000) - 2592000);
      // const sql = 'SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM `user` a, user_tag b WHERE a.hertz >= ' + hz_min + ' AND a.hertz <= ' + hz_max + ' AND a.id != ' + userid + ' AND a.id = b.userid AND a.contentState != 0';
      const sql = `SELECT f.* FROM (SELECT a.id, a.nickname, a.avatar, a.hertz, a.gender, a.signature, a.birth, a.city, b.likeTagArr, b.tagArr FROM user a, user_tag b WHERE a.hertz >= ${hz_min} AND a.hertz <= ${hz_max} AND a.id != ${userid} AND a.id = b.userid AND a.contentState != 0) f, (SELECT COUNT(id) AS count, authorid FROM content_like GROUP BY authorid) e ,(SELECT userid FROM content WHERE create_time > ${time} GROUP BY userid) k WHERE f.id = e.authorid AND e.count > 5 AND f.id = k.userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserContentStatus = function (userid, num) {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE user SET contentState = ' + num + ' WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateContentLocation = function (id, location) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE content SET location = "${location}" WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentTag = function () {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT id, name, slogan FROM content_tag_type';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByTag = function (args) {
    return new Promise((resolve, reject) => {
      const tagid = args['tagid'],
        page = args['page'],
        size = args['size'];
      const sql = `SELECT a.*, b.avatar, b.nickname, b.signature, b.gender, b.city, b.hertz FROM content a, user b WHERE a.tagType = ${tagid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByTagNew = function (args) {
    return new Promise((resolve, reject) => {
      const tagid = args['tagid'],
        lastId = args['lastId'];
      const sql = `SELECT a.*, b.avatar, b.nickname, b.signature, b.gender, b.city, b.hertz FROM content a, user b WHERE a.tagType = ${tagid} AND a.status = 0 AND a.userid = b.id ${
        lastId ? `AND a.id < ${lastId}` : ''
      } ORDER BY a.id DESC LIMIT 20`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByTagV2 = function (args) {
    return new Promise((resolve, reject) => {
      const tagid = args['tagid'],
        page = args['page'],
        size = args['size'];
      const sql = `SELECT a.*, b.avatar, b.nickname, b.signature, b.gender, b.city, b.hertz FROM content a, user b WHERE a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByTagV4 = function (args) {
    return new Promise((resolve, reject) => {
      const tagid = args['tagid'],
        page = args['page'],
        size = args['size'],
        arr = args['reportUser'];
      const sql = `SELECT a.*, b.avatar, b.nickname, b.signature, b.gender, b.city, b.hertz FROM content a, user b WHERE a.userid = b.id AND userid IN (${arr.toString()}) AND images != '' ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByTagV3 = function (args) {
    return new Promise((resolve, reject) => {
      const tagid = args['tagid'],
        page = args['page'],
        size = args['size'];
      const sql = `SELECT d.*, c.id AS likeId FROM (SELECT a.*, b.avatar, b.nickname, b.signature, b.gender, b.city, b.hertz FROM content a, user b WHERE a.tagType = ${tagid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }) d LEFT JOIN content_like c ON d.userid = c.userid AND d.id = c.contentid AND c.status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentCountByTag = function (args) {
    return new Promise((resolve, reject) => {
      const tagid = args['tagid'];
      const sql = `SELECT COUNT(id) AS count FROM content WHERE status = 0 AND tagType = ${tagid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentCountByTagV2 = function (args) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM content`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentCountByTagV3 = function (arr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM content WHERE userid IN (${arr.toString()}) AND images != ''`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putContent = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        images = args['images'],
        video = args['video'],
        imageType = args['imageType'],
        tag = args['tag'],
        time = parseInt(Math.round(new Date().getTime() / 1000)),
        location = args['location'],
        status = args['status'],
        atUserIds = args['atUserIds'],
        timing = args['timing'];
      let text = args['text'];
      let type = 1;
      if (video) {
        type = 4;
      }
      const atUserIdsValue = atUserIds ? `"${atUserIds}"` : null;

      const sql = `INSERT INTO content (userid, content, create_time, images, imageType, status, tagType, type, location, video, timing, atUserIds) VALUES
       (${userid}, "${text}", ${time}, "${images}", "${imageType}", ${status}, ${tag}, ${type}, "${location}", "${video}", ${
        timing ? timing : null
      }, ${atUserIdsValue})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putMusicContent = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        musicContent = args['musicContent'],
        images = args['images'],
        imageType = args['imageType'],
        tag = args['tag'],
        time = parseInt(Math.round(new Date().getTime() / 1000)),
        location = args['location'],
        status = args['status'],
        atUserIds = args['atUserIds'],
        timing = args['timing'];
      let text = args['text'];

      const atUserIdsValue = atUserIds ? `"${atUserIds}"` : null;

      const sql = `INSERT INTO content (userid, content, create_time, images, imageType, status, tagType, type, musicContent, location, timing, atUserIds) VALUES
       (${userid}, "${text}", ${time}, "${images}", "${imageType}", ${status}, ${tag}, 2, "${musicContent}", "${location}", ${
        timing ? timing : null
      }, ${atUserIdsValue})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putLinkShareContent = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        linkShareContent = args['linkShareContent'],
        images = args['images'],
        imageType = args['imageType'],
        tag = args['tag'],
        time = parseInt(Math.round(new Date().getTime() / 1000)),
        location = args['location'],
        status = args['status'],
        timing = args['timing'],
        atUserIds = args['atUserIds'],
        text = args['text'];
      const atUserIdsValue = atUserIds ? `"${atUserIds}"` : null;

      const sql = `INSERT INTO content (userid, content, create_time, images, imageType, status, tagType, type, linkshare, location, timing, atUserIds) VALUES
       (${userid}, "${text}", ${time}, "${images}", "${imageType}", ${status}, ${tag}, 3, "${linkShareContent}", "${location}", ${
        timing ? timing : null
      }, ${atUserIdsValue})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putContentTest = function (text) {
    return new Promise((resolve, reject) => {
      const sql = 'INSERT INTO test (mobile, text) VALUES (1, "' + text + '")';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentTest = function (id) {
    return new Promise((resolve, reject) => {
      const sql = 'select * from test where id = ' + id;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentInfoById = function (contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.status, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.location, a.linkshare, a.video, a.sponsor, a.timing, a.top, a.atUserIds FROM content a, user b WHERE a.id = ${contentid} AND a.status != 1 AND a.userid = b.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentInfoByIdV2 = function (contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.status, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.location, a.linkshare, a.video, a.sponsor, a.timing, a.top, a.atUserIds FROM content a, user b WHERE a.id = ${contentid} AND a.userid = b.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchUserAllContent = function (userid, page, size, userid_, agent) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (userid_ === 7) {
        sql = `SELECT k.*, c.name AS tagName FROM (SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.tagType, a.id AS contentid, a.location, a.linkshare, a.video FROM content a, user b WHERE b.id = ${userid} AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
          (page - 1) * size
        }) k LEFT JOIN content_tag_type c ON k.tagType = c.id`;
      } else {
        sql = `SELECT k.*, c.name AS tagName FROM (SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.tagType, a.id AS contentid, a.location, a.linkshare, a.video FROM content a, user b WHERE b.id = ${userid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
          (page - 1) * size
        }) k LEFT JOIN content_tag_type c ON k.tagType = c.id`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchUserAllContentNew = function (userid, page, size, userid_, agent) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (userid_ === 7) {
        sql = `SELECT k.*, c.name AS tagName FROM (SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.tagType, a.id AS contentid, a.location, a.linkshare, a.video, a.status, a.timing, a.sponsor, a.top FROM content a, user b WHERE b.id = ${userid} AND a.userid = b.id ORDER BY a.update_at DESC, a.create_time DESC LIMIT ${size} OFFSET ${
          (page - 1) * size
        }) k LEFT JOIN content_tag_type c ON k.tagType = c.id`;
      } else {
        if (parseInt(userid) === parseInt(userid_)) {
          sql = `SELECT k.*, c.name AS tagName FROM (SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.tagType, a.id AS contentid, a.location, a.linkshare, a.video, a.status, a.timing, a.sponsor, a.top FROM content a, user b WHERE b.id = ${userid} AND a.status != 1 AND a.userid = b.id ORDER BY a.update_at DESC, a.create_time DESC LIMIT ${size} OFFSET ${
            (page - 1) * size
          }) k LEFT JOIN content_tag_type c ON k.tagType = c.id`;
        } else {
          sql = `SELECT k.*, c.name AS tagName FROM (SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType, a.userid AS authorid, a.type, a.musicContent, a.tagType, a.id AS contentid, a.location, a.linkshare, a.video, a.status, a.timing, a.sponsor, a.top FROM content a, user b WHERE b.id = ${userid} AND a.status = 0 AND a.userid = b.id ORDER BY a.update_at DESC, a.create_time DESC LIMIT ${size} OFFSET ${
            (page - 1) * size
          }) k LEFT JOIN content_tag_type c ON k.tagType = c.id`;
        }
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchAttentionUserAllContent = function (arr, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT c.*, d.name AS tagName FROM (SELECT a.nickname, a.id AS userid, a.gender, a.avatar, a.city, a.hertz, b.id AS contentid, b.content, b.images, b.imageType, b.type, b.musicContent, b.likeNum, b.commentNum, b.create_time, b.tagType, b.linkshare, b.video FROM user a, content b WHERE b.userid IN (${arr.toString()}) AND b.status = 0 AND b.userid = a.id ORDER BY b.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }) AS c LEFT JOIN content_tag_type d ON c.tagType = d.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchAttentionUserAllContentNew = function (arr, lastId) {
    return new Promise((resolve, reject) => {
      let sql = `SELECT c.*, d.name AS tagName FROM (SELECT a.nickname, a.id AS userid, a.gender, a.avatar, a.city, a.hertz, b.id AS contentid, b.content, b.images, b.imageType, b.type, b.musicContent, b.likeNum, b.commentNum, b.create_time, b.tagType, b.linkshare, b.video FROM user a, content b WHERE b.userid IN (${arr.toString()}) AND b.status = 0 AND b.userid = a.id`;
      if (lastId && parseInt(lastId) > 0) {
        sql += ` AND b.id < ${lastId}`;
      }
      sql += ` ORDER BY b.create_time DESC LIMIT 20) AS c LEFT JOIN content_tag_type d ON c.tagType = d.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserContentCount = function (arr, userid_) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (userid_ === 7) {
        sql = `SELECT COUNT(*) AS count FROM content WHERE userid IN (${arr.toString()})`;
      } else {
        sql = `SELECT COUNT(*) AS count FROM content WHERE userid IN (${arr.toString()}) AND status = 0`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserContentCountNew = function (userid, userid_) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (userid_ === 7) {
        sql = `SELECT COUNT(*) AS count FROM content WHERE userid = ${userid}`;
      } else {
        if (parseInt(userid) === parseInt(userid_)) {
          sql = `SELECT COUNT(*) AS count FROM content WHERE userid = ${userid} AND status != 1`;
        } else {
          sql = `SELECT COUNT(*) AS count FROM content WHERE userid = ${userid} AND status = 0`;
        }
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentInfo = function (contentid, userid) {
    return new Promise((resolve, reject) => {
      const sql =
        'SELECT b.hertz, b.avatar, b.nickname, b.signature, b.gender, b.city, a.images, a.create_time, a.likeNum, a.commentNum, a.content, a.imageType FROM content a, user b WHERE a.userid = ' +
        userid +
        ' AND a.id = ' +
        contentid +
        ' AND a.status = 0 AND a.userid = b.id';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delContent = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        contentid = args['contentid'],
        time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql =
        'UPDATE content SET status = 1 , del_time = ' + time + ' WHERE userid = ' + userid + ' AND id = ' + contentid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.insertIntoUserBlack = function (userid, blackUserid, status) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO user_black (userid, black_userid, status, create_time) VALUES (${parseInt(
        userid,
      )}, ${parseInt(blackUserid)}, ${parseInt(status)}, ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postUserBlack = function (userid, blackUserid, type) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      let sql = null;
      if (type) {
        sql = `UPDATE user_black SET status = 0, create_time = ${time} WHERE userid = ${parseInt(
          userid,
        )} AND black_userid = ${parseInt(blackUserid)}`;
      } else {
        sql = `INSERT INTO user_black (userid, black_userid, status, create_time) VALUES (${parseInt(
          userid,
        )}, ${parseInt(blackUserid)}, 0, ${time})`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postUserBlack_ = function (userid, blackUserid, type) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      let sql = null;
      if (type) {
        sql = `UPDATE user_black SET status = 2, create_time = ${time} WHERE userid = ${parseInt(
          userid,
        )} AND black_userid = ${parseInt(blackUserid)}`;
      } else {
        sql = `INSERT INTO user_black (userid, black_userid, status, create_time) VALUES (${parseInt(
          userid,
        )}, ${parseInt(blackUserid)}, 2, ${time})`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBlack = function (userid, blackUserid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_black WHERE userid = ${parseInt(userid)} AND black_userid = ${parseInt(
        blackUserid,
      )}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBlackRs = function (userid, blackUserid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_black WHERE userid = ${parseInt(userid)} AND black_userid = ${parseInt(
        blackUserid,
      )} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBlackList_ = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.id, b.nickname, b.avatar, a.create_time, a.black_userid, a.userid FROM user_black a, user b WHERE a.black_userid = ${userid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getBeBlackedList = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_black WHERE status = 0 AND black_userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getBeBlackedRs = function (userid, blackUserid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user_black WHERE status = 0 AND black_userid = ${blackUserid} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBlackList = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.id, b.nickname, b.avatar, a.create_time, a.black_userid, a.userid FROM user_black a, user b WHERE a.userid = ${userid} AND a.status = 0 AND a.black_userid = b.id ORDER BY a.create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBlackFun2 = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_black WHERE status = 0 AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllUserBlack = function () {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM user_black where status != 1';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.cancelUserBlack = function (userid, blackUserid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE user_black SET status = 1, create_time = ${time} WHERE userid = ${parseInt(
        userid,
      )} AND black_userid = ${parseInt(blackUserid)}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.cancelUserBlack_ = function (userid, blackUserid, status) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE user_black SET status = ${status}, create_time = ${time} WHERE userid = ${parseInt(
        userid,
      )} AND black_userid = ${parseInt(blackUserid)}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserReportCount = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM report WHERE reported_userid = ${parseInt(userid)}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserReportCountV2 = function (userid) {
    return new Promise((resolve, reject) => {
      const now = parseInt(new Date() / 1000) - 60 * 60 * 24 * 3;
      const sql = `SELECT COUNT(id) AS count FROM report WHERE reported_userid = ${parseInt(
        userid,
      )} AND create_time >= ${now}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContent = function (contentid, userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM content WHERE id = ' + contentid + ' AND status != 1 AND userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchIslandContentByUserid = function (userid, discussId) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM island_content WHERE id = ' + discussId + ' AND status = 0 AND userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delIslandContent = function (userid, discussId) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE island_content SET status = 1 WHERE id = ${discussId} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchIslandContent = function (discussId) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM island_content WHERE id = ' + discussId + ' AND status = 0';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIslandMainComments = function (discussId, authorid, size, page) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT a.*, b.nickname, b.avatar 
        FROM island_content_comment a 
        JOIN user b ON a.userid = b.id 
        WHERE a.discussId = ${discussId} 
        AND a.authorid = ${authorid} 
        AND a.belongs = 0 
        AND a.status = 0 
        ORDER BY a.likeNum DESC, a.create_time DESC 
        LIMIT ${size} OFFSET ${(page - 1) * size}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchIslandContentComment = function (commentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT belongs, userid FROM island_content_comment WHERE id = ${commentid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putContentLike = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        authorid = args['authorid'],
        contentid = args['contentid'],
        time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql =
        'INSERT INTO content_like (userid, contentid, create_time, authorid, status) VALUES (' +
        userid +
        ', "' +
        contentid +
        '", ' +
        time +
        ', "' +
        authorid +
        '", 0)';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateContentLike = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        contentid = args['contentid'],
        status = args['status'],
        time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql =
        'UPDATE content_like SET `status` = ' +
        status +
        ', update_time = ' +
        time +
        ' WHERE userid = ' +
        userid +
        ' AND contentid = ' +
        contentid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putContentComment = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        authorid = args['authorid'],
        contentid = args['contentid'],
        comment = args['comment'],
        to_userid = args['to_userid'],
        belongs = args['belongs'],
        to_commentid = args['to_commentid'],
        time = args['time'];
      const sql =
        'INSERT INTO content_comment (userid, contentid, create_time, authorid, to_userid, status, comment, belongs, to_commentid) VALUES (' +
        userid +
        ', "' +
        contentid +
        '", ' +
        time +
        ', ' +
        authorid +
        ', ' +
        to_userid +
        ', 0, "' +
        comment +
        '", ' +
        belongs +
        ', ' +
        to_commentid +
        ')';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.putIslandContentComment = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        authorid = args['authorid'],
        discussId = args['discussId'],
        comment = args['comment'],
        to_userid = args['to_userid'],
        belongs = args['belongs'],
        to_commentid = args['to_commentid'],
        time = args['time'],
        type = args['type'],
        images = args['images'] || '',
        voice = args['voice'] || '',
        voice_time = args['voice_time'];
      const sql =
        'INSERT INTO island_content_comment (userid, discussId, create_time, authorid, to_userid, status, comment, belongs, to_commentid, type, images, voice, voice_time) VALUES (' +
        userid +
        ', "' +
        discussId +
        '", ' +
        time +
        ', ' +
        authorid +
        ', ' +
        to_userid +
        ', 0, "' +
        comment +
        '", ' +
        belongs +
        ', ' +
        to_commentid +
        ', ' +
        type +
        ',"' +
        images +
        '", "' +
        voice +
        '", ' +
        voice_time +
        ')';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delComment = function (commentid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = 'UPDATE content_comment SET status = 1 , del_time = ' + time + ' WHERE id = ' + commentid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delIslandComment = function (commentid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = 'UPDATE island_content_comment SET status = 1 , del_time = ' + time + ' WHERE id = ' + commentid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchComment = function (commentid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT userid FROM content_comment WHERE id = ' + commentid + ' AND status = 0';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchIslandComment = function (commentid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT userid FROM island_content_comment WHERE id = ' + commentid + ' AND status = 0';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchCommentFun = function (userid, contentid, commentid) {
    return new Promise((resolve, reject) => {
      const sql =
        'SELECT * FROM content_comment WHERE id = ' +
        commentid +
        ' AND contentid = ' +
        contentid +
        ' AND userid = ' +
        userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchToCommentRs = function (commentid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM content_comment WHERE to_commentid = ' + commentid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchToIslandCommentRs = function (commentid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM island_content_comment WHERE to_commentid = ' + commentid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateContentLikeNum = function (contentid, type) {
    return new Promise((resolve, reject) => {
      let str = '+ 1';
      if (type < 0) {
        str = '- 1';
      }
      const sql = `UPDATE content set likeNum = likeNum ${str} WHERE id = ${contentid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateContentCommentNum = function (contentid, type) {
    return new Promise((resolve, reject) => {
      let str = '+ 1';
      if (type < 0) {
        str = '- 1';
      }
      const sql = `UPDATE content set commentNum = commentNum ${str} WHERE id = ${contentid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateIslandContentCommentNum = function (discussId, type) {
    return new Promise((resolve, reject) => {
      let str = '+ 1';
      if (type < 0) {
        str = '- 1';
      }
      const sql = `UPDATE island_content set commentNum = commentNum ${str} WHERE id = ${discussId}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentLike = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        contentid = args['contentid'];
      const sql = 'SELECT status FROM content_like WHERE userid = ' + userid + ' AND contentid = ' + contentid;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.batchSearchContentLike = function (args) {
    return new Promise((resolve, reject) => {
      const { userid, contentIds } = args;
      if (!contentIds || contentIds.length === 0) {
        return [];
      }

      const contentIdStr = contentIds.join(',');
      const sql = `SELECT contentid, status FROM content_like WHERE userid = ${userid} AND contentid IN (${contentIdStr})`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchLikeFun = function (userid, contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM content_like WHERE userid = ${userid} AND contentid = ${contentid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.batchSearchLikeFun = function (userid, contentIds) {
    return new Promise((resolve, reject) => {
      if (!contentIds || contentIds.length === 0) {
        return Promise.resolve([]);
      }

      const contentIdStr = contentIds.join(',');
      const sql = `SELECT contentid FROM content_like WHERE userid = ${userid} AND contentid IN (${contentIdStr}) AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchLikeFunV2 = function (userid, contentids) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT contentid AS id FROM content_like WHERE userid = ${userid} AND contentid IN (${contentids.toString()}) AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchCommentFun = function (userid, contentid) {
    return new Promise((resolve, reject) => {
      const sql =
        'SELECT id FROM content_comment WHERE userid = ' + userid + ' AND contentid = ' + contentid + ' AND status = 0';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentLikeInfo = function (contentid, userid, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.avatar, b.nickname, b.id AS userid, b.gender, a.create_time FROM content_like a, user b WHERE a.contentid = ${contentid} AND a.authorid = ${userid} AND a.userid = b.id AND a.status = 0 ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentLikeCount = function (contentid, userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) FROM content_like WHERE contentid = ${contentid} AND authorid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentLikeCountV2 = function (contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) AS count FROM content_like WHERE contentid = ${contentid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentCommentCountV2 = function (contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) AS count FROM content_comment WHERE contentid = ${contentid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchContentCommonInfo = function (contentid, userid, size, page) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT obj.* , user.avatar AS to_avatar, user.nickname AS to_nickname FROM user,(SELECT a.*, b.nickname, b.avatar FROM 
                content_comment a, user b WHERE a.contentid = ${contentid} AND a.authorid = ${userid} AND a.userid = b.id AND a.status = 0 
                ORDER BY a.create_time DESC) AS obj WHERE obj.to_userid = user.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateIslandCommentTime = function (discussId) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE island_content SET comment_update_time = ${time} WHERE id = ${discussId}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchIslandContentCommonInfo = function (discussId, userid, size, page) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT obj.* , user.avatar AS to_avatar, user.nickname AS to_nickname FROM user,(SELECT a.*, b.nickname, b.avatar FROM island_content_comment a, user b WHERE a.discussId = ${discussId} AND a.authorid = ${userid} AND a.userid = b.id AND a.status = 0 ORDER BY a.create_time DESC) AS obj WHERE obj.to_userid = user.id ORDER BY likeNum DESC, create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIslandContentCommentTotal = function (discussId, userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM island_content_comment WHERE discussId = ${discussId} AND authorid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIslandMainCommentsTotal = function (discussId, authorid) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT COUNT(*) AS count 
        FROM island_content_comment 
        WHERE discussId = ${discussId} 
        AND authorid = ${authorid} 
        AND belongs = 0 
        AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIslandCommentReplies = function (discussId, commentIds) {
    return new Promise((resolve, reject) => {
      if (!commentIds.length) {
        resolve([]);
        return;
      }
      const commentIdsStr = commentIds.join(',');
      const sql = `
        SELECT a.*, b.nickname, b.avatar, c.nickname as to_nickname, c.avatar as to_avatar
        FROM island_content_comment a
        JOIN user b ON a.userid = b.id
        JOIN user c ON a.to_userid = c.id
        WHERE a.discussId = ${discussId}
        AND a.belongs IN (${commentIdsStr})
        AND a.status = 0
        ORDER BY a.create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIslandContentLike = function (userid, commentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM island_content_comment_like WHERE userid = ${userid} AND commentid = ${commentid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserRateAndVaryRate = function (rate, userid) {
    return new Promise((resolve, reject) => {
      const sql = 'UPDATE `user` SET hertz = ' + rate + ', vary_hertz = ' + rate + ' WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.insertUSerTag = function (userid, tagStr, likeTagStr, attribute, rate) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO user_tag (userid, tagArr, likeTagArr, attribute, rate, create_time) VALUES (${userid}, "${tagStr}", "${likeTagStr}", "${attribute}", "${rate}", ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserTag = function (userid, tagStr, likeTagStr, attribute, rate) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE user_tag SET tagArr = "${tagStr}", attribute = "${attribute}", rate = ${rate}, update_time = ${time}, likeTagArr = "${likeTagStr}" WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchUserTag = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT id, attribute FROM user_tag WHERE userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchTagSQL = function () {
    return new Promise((resolve, reject) => {
      const sql = 'select * from attribute_tags';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postReport = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        reported_userid = args['reported_userid'],
        report_type = args['report_type'],
        type = args['type'],
        rid = args['rid'],
        detail = args['detail'] ? `'${args['detail']}'` : null,
        time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO report (userid, reported_userid, create_time, type, report_type, rid, detail) VALUES (${userid}, ${reported_userid}, ${time}, ${type}, ${report_type}, ${rid}, ${detail})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getReport = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        reported_userid = args['reported_userid'],
        report_type = args['report_type'],
        type = args['type'],
        rid = args['rid'];
      const sql = `SELECT * FROM report WHERE userid = ${userid} AND type = ${type} AND rid = ${rid} AND reported_userid = ${reported_userid} AND report_type = ${report_type} `;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllReport = function () {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT reported_userid, COUNT(*) AS count FROM report GROUP BY reported_userid ORDER BY count DESC';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getReportV2 = function (userid, reported_userid, type, rid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM report WHERE userid = ${userid} AND reported_userid = ${reported_userid} AND type = ${type} AND rid = ${rid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addUserVaryAttribute = function (userid, attributeid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO user_vary_attribute (userid, attributeid, create_time) VALUES (${userid}, "${attributeid}", ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserTag = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM user_tag WHERE userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllUserId = function (page) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT id FROM user ORDER BY id DESC LIMIT 500 offset ' + (page - 1) * 500;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllUserId2 = function (page) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM user';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllUserIdBySignature = function (from, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user LIMIT ${size} OFFSET ${from * size}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBlackFun = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_black WHERE status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserSignature = function (userid, rank) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET signature = '' WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFansFun = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM attention WHERE userid2 = ${userid} ORDER BY id ASC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAttenFun = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM attention WHERE userid1 = ${userid} ORDER BY id ASC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLocationAndPushStatus = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT locationStatus, pushStatus, cellStyle, homeHide FROM `user` WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateCellStyle = function (userid, style) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET cellStyle = ${style} WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateHomeHide = function (userid, hide) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET homeHide = ${hide} WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateLocationAndPushStatus = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'],
        locationStatus = args['locationStatus'],
        pushStatus = args['pushStatus'];
      const sql =
        'UPDATE user SET locationStatus = ' + locationStatus + ', pushStatus = ' + pushStatus + ' WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getNotificationList = function (userid, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM notification WHERE userid = ${userid} AND status != 2 ORDER BY create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussById = function (discussid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM island_content WHERE id = ${discussid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentById = function (id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE id = ${id} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUnreadNotification = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM notification WHERE userid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getNotificationListCount = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM notification WHERE userid = ${userid} AND status != 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateNotificationStatus = function (id, userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE notification SET status = 1 WHERE id = ${id} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.deleteNotification = function (ids, userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE notification SET status = 2 WHERE id IN (${ids}) AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserVaryAttribute = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT attributeid FROM user_vary_attribute WHERE userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delUserVaryAttribute = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM user_vary_attribute WHERE userid = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getOldVaryRateNum = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT tag_num FROM user WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserVaryAttribute = function (userid, varyRate, new_vary_tag_num) {
    return new Promise((resolve, reject) => {
      const sql =
        'UPDATE `user` SET vary_hertz = ' + varyRate + ', tag_num = "' + new_vary_tag_num + '" WHERE id = ' + userid;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserDeviceToken = function (userid, deviceToken) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET deviceToken = '${deviceToken}' WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserLocation = function (args) {
    return new Promise((resolve, reject) => {
      let country = args['country'],
        province = args['province'],
        city = args['city'],
        userid = args['userid'];
      if (country == 'null' || province == 'null' || city == 'null') {
        const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
        const index = Math.floor(Math.random() * locationArr.length);
        const location_ = locationArr[index];
        country = location_;
        province = location_;
        city = location_;
      }
      const sql = `UPDATE user SET country = "${country}", province = "${province}", city = "${city}" WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.scriptFun1 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(c.userid) AS count FROM (SELECT userid FROM content WHERE create_time >= ${startTime} AND create_time <= ${endTime} AND status = 0 GROUP BY userid) c`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.scriptFun2 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) AS count FROM content WHERE create_time >= ${startTime} AND create_time <= ${endTime} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.scriptFun3 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) AS count FROM content WHERE status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.scriptFun4 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(c.userid) AS count FROM (SELECT userid FROM content_like WHERE create_time >= ${startTime} AND create_time <= ${endTime} AND status = 0 GROUP BY userid) c`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.scriptFun5 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(c.userid) AS count FROM (SELECT userid FROM content_comment WHERE create_time >= ${startTime} AND create_time <= ${endTime} AND status = 0 GROUP BY userid) c`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.scriptFun6 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM invitation';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };
  this.scriptFun7 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.scriptFun8 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) AS count FROM user`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.scriptFun9 = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT count(id) AS count FROM user WHERE create_time >= ${startTime} AND create_time <= ${endTime}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getSsid = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT ssid FROM user WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllContent = function () {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM content WHERE `status` = 0';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllShudong = function (time, from, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM shudong WHERE created < ${time} AND status = 1 LIMIT ${size} OFFSET ${from * size}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getTimingContent = function (time, from, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE timing < ${time} AND status = 3 LIMIT ${size} OFFSET ${from * size}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delShudong = function (id) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE shudong SET status = 0 WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentLike = function (contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content_like WHERE contentid = ${contentid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentLikeV2 = function (contentid, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.id, b.nickname, b.gender, b.avatar, b.city, b.hertz, b.signature, a.create_time FROM content_like a, user b WHERE a.contentid = ${contentid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentCommentV2 = function (contentid, page, size) {
    return new Promise((resolve, reject) => {
      // const sql = `SELECT b.id, b.nickname, b.gender, b.avatar, b.city, b.hertz FROM content_comment a, user b WHERE a.contentid = ${contentid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${(page - 1) * size}`;
      const sql = `SELECT d.*, c.nickname AS to_nickname, c.gender AS to_gender, c.avatar AS to_avatar, c.city AS to_city, c.hertz AS to_hertz FROM user c, (SELECT a.userid AS userid, b.nickname, b.gender, b.avatar, b.city, b.hertz, a.comment, a.create_time, a.to_userid FROM content_comment a, user b WHERE a.contentid = ${contentid} AND a.status = 0 AND a.userid = b.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }) AS d WHERE c.id = d.to_userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addFeedBack = function (userid, text, type) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO feed_back (userid, text, create_time, type) VALUES (${userid}, "${text}", ${time}, ${type});`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getWaters = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM island`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postDiscuss = function (args) {
    return new Promise((resolve, reject) => {
      const userid = args['userid'];
      const island_type = args['island_type'];
      const voice_type = args['voice_type'];
      const title = args['title'];
      const content = args['content'];
      const time = args['time'];
      const sql = `INSERT INTO island_content (userid, title, content, island_type, voice_type, create_time, comment_update_time) VALUES (${userid}, "${title}", "${content}", ${island_type}, ${voice_type}, ${time}, ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussByWater = function (args) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (parseInt(args['island_type']) == 1) {
        sql = `SELECT * FROM island_content WHERE status = 0 AND voice_type = 1 ORDER BY comment_update_time DESC LIMIT ${
          args['size']
        } OFFSET ${(args['page'] - 1) * args['size']}`;
      } else {
        sql = `SELECT * FROM island_content WHERE status = 0 AND island_type = ${
          args['island_type']
        } ORDER BY comment_update_time DESC LIMIT ${args['size']} OFFSET ${(args['page'] - 1) * args['size']}`;
      }
      // const sql = `SELECT * FROM island_content WƒHERE status = 0 AND island_type = ${args['island_type']} ORDER BY id DESC LIMIT ${args['size']} OFFSET ${(args['page'] - 1) * args['size']}`
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussByWaterNew = function (args) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (parseInt(args['island_type']) == 1) {
        sql = `SELECT * FROM island_content WHERE status = 0 AND voice_type = 1 ORDER BY ${
          args['sort'] === 1 ? 'id DESC' : 'commentNum DESC, commentNum DESC'
        } LIMIT ${args['size']} OFFSET ${(args['page'] - 1) * args['size']}`;
      } else if (parseInt(args['island_type']) == 7) {
        sql = `SELECT * FROM island_content WHERE status = 0 AND userid = ${args['userid']} ORDER BY ${
          args['sort'] === 1 ? 'id DESC' : 'commentNum DESC, commentNum DESC'
        } LIMIT ${args['size']} OFFSET ${(args['page'] - 1) * args['size']}`;
      } else {
        sql = `SELECT * FROM island_content WHERE status = 0 AND island_type = ${args['island_type']} ORDER BY ${
          args['sort'] === 1 ? 'id DESC' : 'commentNum DESC, commentNum DESC'
        } LIMIT ${args['size']} OFFSET ${(args['page'] - 1) * args['size']}`;
      }
      // const sql = `SELECT * FROM island_content WƒHERE status = 0 AND island_type = ${args['island_type']} ORDER BY id DESC LIMIT ${args['size']} OFFSET ${(args['page'] - 1) * args['size']}`
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussByWaterCount = function (args) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (parseInt(args['island_type']) == 1) {
        sql = `SELECT COUNT(id) AS count FROM island_content WHERE status = 0 AND voice_type = 1`;
      } else {
        sql = `SELECT COUNT(id) AS count FROM island_content WHERE status = 0 AND island_type = ${args['island_type']}`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussCommentNum = function (discussId) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM island_content_comment WHERE discussId = ${discussId} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussCommentNumV2 = function (discussIds) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT discussId, COUNT(id) AS count FROM island_content_comment WHERE discussId IN (${discussIds.toString()}) AND status = 0 GROUP BY discussId`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getPinglunCount = function (discussId) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM island_content_comment WHERE belongs = 0 AND discussId = ${discussId} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getDiscussInfo = function (discussId) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.*, b.avatar, b.nickname, b.gender FROM island_content a, user b WHERE a.status = 0 AND a.id = ${discussId} AND a.userid = b.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchIslandCommentLike = function (commentid, userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM island_content_comment_like WHERE commentid = ${commentid} AND userid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.insertIslandCommentLike = function (discussId, userid, commentid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO island_content_comment_like (discussId, userid, create_time, commentid) VALUES (${discussId}, ${userid}, ${time}, ${commentid})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addIslandCommentLikeNum = function (commentid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE island_content_comment SET likeNum = likeNum + 1 WHERE id = ${commentid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addIslandHeat = function (discussId) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE island_content SET heat_value = heat_value + 1 WHERE id = ${discussId}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserActive = function (userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user_active WHERE userid = ${userid} AND create_time > ${time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserActiveV2 = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_active WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserActive = function (id) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE user_active SET create_time = ${time} WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.insertUserActive = function (userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO user_active (userid, create_time) VALUES (${userid}, ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getinvitationRs = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT invitationCode FROM user GROUP BY invitationCode`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByInvitationCode = function (code) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id, create_time FROM user WHERE invitationCode = ${code}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInvitationCode = function (code) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user_invitation_code WHERE code = "${code}" AND status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserInvitationCodeV2 = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user_invitation_code WHERE userid = ${userid} AND status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postUserInvitationCode = function (args) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO user_invitation_code (userid, code, create_time) VALUES (${args['userid']}, "${args['code']}", ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.userVerify = function (phone) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT verify FROM user WHERE mobile = ${phone}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllINvitCode = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT invitation AS code FROM invitation GROUP BY invitation`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getInvitationCodeFunV2 = function (code) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user_invitation_code WHERE code = "${code}" AND status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserInvitCodeUse = function (code) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user_invitation_code SET use_num = use_num + 1 WHERE code = "${code}" AND status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllUserIdV2 = function (from, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE status = 0 ORDER BY id LIMIT ${from}, ${size}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserAttention = function (user_id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM attention WHERE userid1 = ${user_id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserAttentionV2 = function (user_id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM attention WHERE userid1 = ${user_id} AND type = 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserFans = function (user_id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM attention WHERE userid2 = ${user_id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenByTimeAndUser = function (userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM recommen WHERE userid = ${userid} AND create_time > ${time} ORDER BY id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenByTimeAndUserNew = function (userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT accordid, accord_type, recommenid FROM recommen_new WHERE userid = ${userid} AND create_time > ${time} ORDER BY id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenByTimeAndUserNewV2 = function (userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM recommen WHERE userid = ${userid} AND create_time > ${time} ORDER BY id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getNeighborId = function (arr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE id IN (${arr.toString()})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByIn = function (arr) {
    return new Promise((resolve, reject) => {
      // const sql = `SELECT a.id AS userid, a.nickname, a.avatar, a.signature, b.id AS contentid, b.content, b.images, b.type, b.musicContent, c.tagArr FROM user a, content b, user_tag c WHERE b.id IN (${arr.toString()}) AND b.status = 0 AND b.userid = a.id AND a.id = c.userid`;
      const sql = `SELECT a.gender, a.id AS userid, a.nickname, a.avatar, a.signature, b.id AS contentid, b.content, b.images, b.type, b.musicContent, c.tagArr, a.hertz FROM user a, content b, user_tag c WHERE b.id IN (${arr.toString()}) AND b.userid = a.id AND a.id = c.userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentAccordLike = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT b.id, b.content FROM content_like a, content b WHERE a.status = 0 AND a.userid = ${userid} AND a.contentid = b.id AND b.type = 1 AND b.status = 0 AND b.content != '' AND b.id NOT IN (${arr.toString()}) AND b.userid != ${userid} ORDER BY a.create_time DESC LIMIT 1`;
      } else {
        sql = `SELECT b.id, b.content FROM content_like a, content b WHERE a.status = 0 AND a.userid = ${userid} AND a.contentid = b.id AND b.type = 1 AND b.status = 0 AND b.content != '' AND b.userid != ${userid} ORDER BY a.create_time DESC LIMIT 1`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentAccordComment = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT b.id, b.content FROM content_comment a, content b WHERE a.status = 0 AND a.userid = ${userid} AND a.contentid = b.id AND b.type = 1 AND b.status = 0 AND b.content != '' AND b.id NOT IN (${arr.toString()}) AND b.userid != ${userid} ORDER BY a.create_time DESC LIMIT 1`;
      } else {
        sql = `SELECT b.id, b.content FROM content_comment a, content b WHERE a.status = 0 AND a.userid = ${userid} AND a.contentid = b.id AND b.type = 1 AND b.status = 0 AND b.content != '' AND b.userid != ${userid} ORDER BY a.create_time DESC LIMIT 1`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentAccordContent = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT id, content FROM content WHERE userid = ${userid} AND status = 0 AND content != '' AND id NOT IN (${arr.toString()}) AND userid != ${userid} ORDER BY create_time DESC LIMIT 1`;
      } else {
        sql = `SELECT id, content FROM content WHERE userid = ${userid} AND status = 0 AND content != '' AND userid != ${userid} ORDER BY create_time DESC LIMIT 1`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentAccordRand = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT a.id, a.content FROM content a JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM content WHERE status = 0 AND userid != ${userid} AND content != '' AND id NOT IN (${arr.toString()}))) AS idd) AS b ON a.id > b.idd LIMIT 1`;
      } else {
        sql = `SELECT a.id, a.content FROM content a JOIN (SELECT ROUND(RAND() * (SELECT MAX(id) FROM content WHERE status = 0 AND userid != ${userid} AND content != '')) AS idd) AS b ON a.id > b.idd LIMIT 1`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllRecommenInput = function (userid, time) {
    return new Promise((resolve, reject) => {
      time = time - 60 * 60 * 24 * 30;
      const sql = `SELECT input_filter FROM recommen WHERE userid = ${userid} AND create_time > ${time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllRecommenOutput = function (userid, time) {
    return new Promise((resolve, reject) => {
      time = time - 60 * 60 * 24 * 30;
      const sql = `SELECT recommen FROM recommen WHERE userid = ${userid} AND create_time > ${time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getExcludeBlack = function (arr, userid, time) {
    return new Promise((resolve, reject) => {
      time = time - 60 * 60 * 24 * 30;
      const sql = `SELECT a.id FROM content a, (SELECT black_userid FROM user_black WHERE userid = ${userid} AND status = 0) AS b WHERE a.id IN (${arr.toString()}) AND a.userid != b.black_userid AND a.userid != ${userid} AND a.create_time > ${time} GROUP BY a.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getExcludeAttention = function (arr, userid, time) {
    return new Promise((resolve, reject) => {
      time = time - 60 * 60 * 24 * 30;
      const sql = `SELECT a.id FROM content a, (SELECT userid2 FROM attention WHERE userid1= ${userid}) AS b WHERE a.id IN (${arr.toString()}) AND a.userid != b.userid2 AND a.userid != ${userid} AND a.create_time > ${time} GROUP BY a.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getExcludeSed = function (searchArr, sedArr, time) {
    return new Promise((resolve, reject) => {
      time = time - 60 * 60 * 24 * 30;
      let sql = '';
      if (sedArr.length) {
        sql = `SELECT id FROM content WHERE id IN (${searchArr.toString()}) AND id NOT IN (${sedArr.toString()}) AND create_time > ${time}`;
      } else {
        sql = `SELECT id FROM content WHERE id IN (${searchArr.toString()}) AND create_time > ${time}`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addRecommen = function (userid, inputArr, outPutArr) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO recommen (userid, create_time, recommen, input_filter) VALUES (${userid}, ${time}, '${JSON.stringify(
        outPutArr,
      )}', '${JSON.stringify(inputArr)}')`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addRecommenV2 = function (userid, recommenid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO recommen (userid, create_time, recommenid) VALUES (${userid}, ${time}, "${recommenid}")`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserBadge = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM active_badge WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenContentRandom = function (userid, arr, limit) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT b.id, b.content FROM attention a, content b WHERE a.userid1 = ${userid} AND b.userid = a.userid2 AND b.status = 0 AND b.content != "" AND b.id NOT IN (${arr.toString()}) ORDER BY b.create_time DESC LIMIT ${limit}`;
      } else {
        sql = `SELECT b.id, b.content FROM attention a, content b WHERE a.userid1 = ${userid} AND b.userid = a.userid2 AND b.status = 0 AND b.content != "" ORDER BY b.create_time DESC LIMIT ${limit}`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.judgeByMobile = function (mobile) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user WHERE mobile = ${mobile}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLikeBadge = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count, userid FROM content_like WHERE status = 0 AND create_time >= 1577808000 AND userid NOT IN (2, 3, 4, 6, 7, 8, 11, 13, 101678, 101680, 102569, 105856) GROUP BY userid ORDER BY count DESC LIMIT 52`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getCommentBadge = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count, userid FROM content_comment WHERE status = 0 AND create_time >= 1577808000 AND userid NOT IN (2, 3, 4, 6, 7, 8, 11, 13, 101678, 101680, 102569, 105856) GROUP BY userid ORDER BY count DESC LIMIT 52`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addBadge = function (userid, type, type_name, introduction, icon) {
    return new Promise((resolve, reject) => {
      const create_time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO active_badge (userid, type, type_name, create_time, introduction, icon) VALUES (${userid}, ${type}, '${type_name}', ${create_time}, '${introduction}', '${icon}')`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  /**
   * NEW RECOMMEN
   */
  this.getFromRecommenEd = function (userid, time) {
    return new Promise((resolve, reject) => {
      const accrod_time = time - 60 * 60 * 24 * 90;
      const sql = `SELECT recommenid, accordid, accord_type FROM recommen_new WHERE userid = ${userid} AND create_time > ${accrod_time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByRecommenType = function (userid, time, type) {
    return new Promise((resolve, reject) => {
      const accrod_time = time - 60 * 60 * 24 * 90;
      const sql = `SELECT accordid FROM recommen_new WHERE userid = ${userid} AND create_time > ${accrod_time}`;
      // const sql = `SELECT accordid FROM recommen_new WHERE userid = ${userid} AND create_time > ${accrod_time} AND accord_type = ${type}`;;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentidByLike = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT a.id, a.content FROM content a, content_like b WHERE b.userid = ${userid} AND b.contentid NOT IN (${arr.toString()}) AND b.status = 0 AND a.id = b.contentid AND a.status = 0 AND a.content != '' ORDER BY b.create_time DESC LIMIT 2`;
      } else {
        sql = `SELECT a.id, a.content FROM content a, content_like b WHERE b.userid = ${userid} AND b.status = 0 AND a.id = b.contentid AND a.status = 0 AND a.content != '' ORDER BY b.create_time DESC LIMIT 2`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentidByComment = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT a.id, a.content FROM content a, content_comment b WHERE b.userid = ${userid} AND b.contentid NOT IN (${arr.toString()}) AND b.status = 0 AND a.id = b.contentid AND a.status = 0 AND a.content != '' ORDER BY b.create_time DESC LIMIT 2`;
      } else {
        sql = `SELECT a.id, a.content FROM content a, content_comment b WHERE b.userid = ${userid} AND b.status = 0 AND a.id = b.contentid AND a.status = 0 AND a.content != '' ORDER BY b.create_time DESC LIMIT 2`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentidByContentPush = function (userid, arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT id, content FROM content WHERE id NOT IN (${arr.toString()}) AND userid = ${userid} AND status = 0 AND content != '' ORDER BY id DESC LIMIT 1`;
      } else {
        sql = `SELECT id, content FROM content WHERE userid = ${userid} AND status = 0 AND content != '' ORDER BY id DESC LIMIT 1`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentidByRandom = function (arr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT id, content FROM content WHERE id NOT IN (${arr.toString()}) AND status = 0 AND content != '' ORDER BY create_time DESC LIMIT 1`;
      } else {
        sql = `SELECT id, content FROM content WHERE status = 0 AND content != '' ORDER BY create_time DESC LIMIT 1`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentidByRandomV2 = function (arr, limit) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (arr.length) {
        sql = `SELECT id FROM content WHERE id NOT IN (${arr.toString()}) AND status = 0 AND content != '' ORDER BY RAND() DESC LIMIT ${limit}`;
      } else {
        sql = `SELECT id FROM content WHERE status = 0 AND content != '' ORDER BY RAND() DESC LIMIT ${limit}`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getNotSedRecommen = function (neighborIdArr, sedArr) {
    return new Promise((resolve, reject) => {
      let sql = '';
      if (sedArr.length) {
        sql = `SELECT id, userid AS authorid FROM content WHERE id IN (${neighborIdArr.toString()}) AND id NOT IN (${sedArr.toString()})`;
      } else {
        sql = `SELECT id, userid AS authorid FROM content WHERE id IN (${neighborIdArr.toString()})`;
      }
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllSedRecommen = function (userid, time) {
    return new Promise((resolve, reject) => {
      const accrod_time = time - 60 * 60 * 24 * 30;
      const sql = `SELECT recommenid FROM recommen_new WHERE userid = ${userid} AND create_time > ${accrod_time} ORDER BY create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentInAttention = function (userid, notSedArr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.id FROM attention a, (SELECT id, userid FROM content WHERE id IN (${notSedArr.toString()})) AS b WHERE a.userid1 = ${userid} AND b.userid = a.userid2 GROUP BY b.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentInBlack = function (userid, notSedArr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.id FROM user_black a, (SELECT id, userid FROM content WHERE id IN (${notSedArr.toString()})) AS b WHERE a.userid = ${userid} AND b.userid = a.black_userid AND a.status = 0 GROUP BY b.id`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentInMy = function (userid, notSedArr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM content WHERE id IN (${notSedArr.toString()}) AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentOverOneMonth = function (userid, notSedArr, time) {
    const accrod_time = time - 60 * 60 * 24 * 30;
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE id IN (${notSedArr.toString()}) AND userid != ${userid} AND create_time > ${accrod_time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addRecommenNew = function (userid, args) {
    return new Promise((resolve, reject) => {
      const accordid = args['accordid'];
      const recommen_type = args['recommen_type'];
      const contentid = args['contentid'];
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO recommen_new (userid, create_time, status, accordid, recommenid, accord_type) VALUES (${userid}, ${time}, 1, ${accordid}, ${contentid}, ${recommen_type})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenContentByIn = function (arr, userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.gender, a.id AS userid, a.nickname, a.avatar, a.signature, b.id AS contentid, b.content, b.images, b.type, b.musicContent, c.tagArr, a.hertz, d.accord_type, b.status, d.accordid FROM user a, content b, user_tag c, recommen_new d WHERE b.id IN (${arr.toString()}) AND b.userid = a.id AND a.id = c.userid AND d.recommenid = b.id AND d.userid = ${userid} AND d.create_time > ${time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenContentByInV2 = function (arr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT d.*, c.tagArr FROM (SELECT a.gender, a.id AS userid, a.nickname, a.avatar, a.signature, b.id AS contentid, b.content, b.images, b.type, b.musicContent, a.hertz, b.status FROM content b LEFT JOIN user a ON b.userid = a.id WHERE b.id IN (${arr.toString()})) d, user_tag c WHERE d.userid = c.userid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenTest = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM recommen_new WHERE userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searcUserAttentionUserid = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT userid2 FROM attention WHERE userid1 = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.searchByMobile = function (mobile) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user WHERE mobile = ${mobile}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserMobile = function (userid, mobile) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET mobile = ${mobile} WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByKey = function (keyWord) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM content WHERE (content LIKE '%${keyWord}%' OR content = '${keyWord}') AND status = 0 ORDER BY create_time DESC LIMIT 20`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getSearchContent = function (arr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id AS userid, a.nickname, a.avatar, a.gender, a.hertz, b.imageType, b.id AS contentid, b.create_time, b.content, b.images, b.type, b.musicContent FROM user a, content b WHERE b.id in (${arr.toString()}) AND b.userid = a.id AND b.status = 0 ORDER BY FIND_IN_SET(b.id, '${arr.toString()}') LIMIT 20`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserLikeList = function (userid, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT c.id AS userid, c.avatar, c.nickname, a.create_time, a.update_time, b.content, b.id AS contentid, b.images, b.status, b.type, b.musicContent FROM content_like a, content b, user c WHERE a.userid = ${userid} AND a.status = 0 AND a.contentid = b.id AND b.userid = c.id ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserLikeCount = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(id) AS count FROM content_like WHERE status = 0 AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.clearUserNotice = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE notification SET status = 1 WHERE userid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.clearUserLiuyan = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE liuyan SET status = 1 WHERE touserid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getCoCreation = function () {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT a.*, b.nickname, b.avatar FROM cocreation a LEFT JOIN user b on a.userid = b.id';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postContentHide = function (userid, contentid, status) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE content SET status = ${status} WHERE id = ${contentid} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postContentHideV2 = function (userid, contentid, status) {
    return new Promise((resolve, reject) => {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `UPDATE content SET status = ${status}, timing = null, create_time = ${time} WHERE id = ${contentid} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.postContentTop = function (userid, contentid, top) {
    return new Promise((resolve, reject) => {
      let time = null;
      if (top === 1) {
        time = parseInt(new Date() / 1000);
      }
      const sql = `UPDATE content SET top = ${top}, update_at = ${time} WHERE id = ${contentid} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getNoTimeAtt = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM attention WHERE id < 8263`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserAttention = function (id, startTime) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE attention SET create_time = ${startTime} WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getColorFont = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM color_font WHERE userid = ${userid} ORDER BY expire_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllUser = function (from, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id, rank FROM user WHERE state = 0 AND usersig IS NOT NULL LIMIT ${from}, ${size}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserHertzByUserId = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT hertz FROM user WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserHertz = function (userid, hertz) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET hertz = ${hertz} WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserOldNew = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT old_new FROM user WHERE id = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserTenContent = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE userid = ${userid} AND status = 0 ORDER BY create_time DESC LIMIT 10`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getBotByName = function (name) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM bot WHERE bot_name = "${name}"`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addBot = function (userid, bot_name, token) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO bot (token, userid, bot_name, create_time) VALUES ("${token}", ${userid}, "${bot_name}", ${parseInt(
        new Date() / 1000,
      )})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getBotByToken = function (token) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM bot WHERE token = "${token}"`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateBotUrl = function (token, url) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE bot SET url = "${url}", status = 1 WHERE token = "${token}"`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getBotUrlById = function (id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM bot WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommendedId = function (userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT recommenid FROM recommen_new WHERE userid = ${userid} AND create_time >= ${time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommendedIdV2 = function (userid, time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT recommenid FROM recommen WHERE userid = ${userid} AND create_time >= ${time}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getRecommenContent = function (carr, uarr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE id IN (${carr.toString()}) AND userid NOT IN (${uarr.toString()}) AND status = 0 ORDER BY create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByHertz = function (hertz, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id FROM content a, user b where a.status = 0 AND a.userid = b.id AND b.hertz >= ${
        hertz - 2
      } AND b.hertz < ${hertz + 2} ORDER BY a.create_time DESC LIMIT ${size} OFFSET ${(page - 1) * size}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByHertz2 = function (hertz, lastId) {
    return new Promise((resolve, reject) => {
      let sql = `SELECT a.id 
                 FROM content a 
                 JOIN user b ON a.userid = b.id 
                 WHERE a.status = 0 
                 AND b.hertz >= ${hertz} AND b.hertz <= ${hertz + 0.99}`;

      if (hertz === 9) {
        sql = 'SELECT a.id FROM content a JOIN user b ON a.userid = b.id WHERE a.status = 0';
      }

      if (lastId && parseInt(lastId) > 0) {
        sql += ` AND a.id < ${lastId}`;
      }

      sql += ` ORDER BY a.create_time DESC LIMIT 20`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getTwoNewContent = function (arr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE status = 0 AND userid NOT IN (${arr.toString()}) ORDER BY create_time DESC LIMIT 200`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getGoodContent = function (time) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM content WHERE likeNum + commentNum >= 10 AND create_time >= ${time} AND status = 0 ORDER BY create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getHertzLikePeople = function (minHertz, maxHertz) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM user WHERE hertz >= ${minHertz} AND hertz <= ${maxHertz}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLikeContent = function (carr, uarr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM content WHERE id IN (${carr.toString()}) AND userid NOT IN (${uarr.toString()}) AND status = 0 ORDER BY create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByIdV2 = function (contentid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id AS aid, a.commentNum, a.content, a.create_time, a.imageType, a.images, a.likeNum, a.location, a.musicContent, a.status, a.tagType, a.type, a.userid, b.avatar, b.city, b.gender, b.hertz, b.nickname, b.id AS uid, a.linkshare, a.video FROM content a, user b WHERE a.id = ${contentid} AND a.status = 0 AND a.userid = b.id`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getContentByIdV3 = function (contentids) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id AS aid, a.commentNum, a.content, a.create_time, a.imageType, a.images, a.likeNum, a.location, a.musicContent, a.status, a.tagType, a.type, a.userid, b.avatar, b.city, b.gender, b.hertz, b.nickname, b.id AS uid, a.linkshare, a.video, a.sponsor, b.signature FROM content a, user b WHERE a.id in (${contentids.toString()}) AND a.status = 0 AND a.userid = b.id ORDER BY FIELD(a.id, ${contentids.toString()})`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getCommentByIds = function (userid, contentids) {
    return new Promise((resolve, reject) => {
      if (!contentids || contentids.length === 0) {
        return resolve([]);
      }
      const sql = `SELECT contentid FROM content_comment WHERE userid = ${userid} AND status = 0 AND contentid IN (${contentids.toString()}) GROUP BY contentid`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getSuiJiContent = function (uidArr, cidArr) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT id FROM content WHERE id NOT IN (${cidArr.toString()}) AND userid NOT IN (${uidArr.toString()}) AND status = 0 ORDER BY create_time DESC LIMIT 30`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserAndReporter = function (userid, reported_userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.nickname, b.nickname AS reportName FROM user a, user b WHERE a.id = ${userid} AND b.id = ${reported_userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByName = function (nickname) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE nickname = "${nickname}" AND state = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByNameV2 = function (nickname, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.* FROM user a, user_tag b WHERE (a.nickname = "${nickname}" OR a.nickname LIKE "%${nickname}%") AND a.state = 0 AND a.id = b.userid ORDER BY a.nickname LIMIT 50`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addUserSearchHistory = function (userid, search) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO user_search_history (userid, search_content) VALUES (${userid}, '${search}');`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getAllFood = function (type) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM food WHERE status = 1 AND type = ${type}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFoodById = function (food_id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM food WHERE status = 1 AND id = ${food_id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delBadge = function () {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM active_badge WHERE type != 1';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getPayByReceiptDataAndUserid = function (receiptData, userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM pay WHERE receipt = '${receiptData}' AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addOperations = function (content, images, title) {
    return new Promise((resolve, reject) => {
      const time = parseInt(new Date() / 1000);
      const sql = `INSERT INTO operations (content, images, title, create_time) VALUES ('${content}', '${images}', '${title}', ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getOperations = function (page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM operations WHERE status = 1 ORDER BY id DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getOperationsCount = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM operations WHERE status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getOperationsDetail = function (id) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM operations WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addReceiptData = function (userid, receiptData, product_id, receipt_hash) {
    return new Promise((resolve, reject) => {
      const time = parseInt(new Date() / 1000);
      const sql = `INSERT INTO pay (userid, platform, receipt, product_id, create_time, update_time, receipt_hash) VALUES (${userid}, 1, '${receiptData}', '${product_id}', ${time}, ${time}, ${receipt_hash})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updatePayStatus = function (id, status) {
    return new Promise((resolve, reject) => {
      const time = parseInt(new Date() / 1000);
      const sql = `UPDATE pay SET status = ${status}, update_time = ${time} WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updatePayStatusV2 = function (id, status, response) {
    return new Promise((resolve, reject) => {
      const time = parseInt(new Date() / 1000);
      const sql = `UPDATE pay SET status = ${status}, update_time = ${time}, response = '${response}' WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIncomeStatistics = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT b.price, c.nickname, b.iap_id, a.create_time, a.update_time FROM pay a, food b, user c WHERE a.product_id = b.iap_id AND a.userid = c.id AND a.status = 2 AND a.update_time >= ${startTime} AND a.update_time < ${endTime} ORDER BY a.create_time DESC`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIncomeStatisticsSum = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT SUM(d.price) AS Income FROM (SELECT b.price, c.nickname, b.iap_id FROM pay a, food b, user c WHERE a.product_id = b.iap_id AND a.userid = c.id AND a.status = 2 AND a.update_time >= ${startTime} AND a.update_time < ${endTime}) d`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getIncomeStatisticsCount = function (startTime, endTime) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM (SELECT b.price, c.nickname, b.iap_id FROM pay a, food b, user c WHERE a.product_id = b.iap_id AND a.userid = c.id AND a.status = 2 AND a.update_time >= ${startTime} AND a.update_time < ${endTime}) d`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getReceipt = function () {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM pay';
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addReceiptHash = function (id, receipt_hash) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE pay SET receipt_hash = ${receipt_hash} WHERE id = ${id}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getPayByReceiptHashAndUserid = function (receipt_hash, userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM pay WHERE receipt_hash = ${receipt_hash} AND userid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getUserByPhone = function (phone) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM user WHERE mobile = ${phone}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delUserContent = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE content SET status = 1 WHERE userid = ${userid}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delUserAttention = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `DELETE FROM attention WHERE userid1 = ${userid} OR userid2 = ${userid}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.delUserPhoneOrWechat = function (userid) {
    return new Promise((resolve, reject) => {
      const time = parseInt(new Date() / 1000);
      const sql = `UPDATE user SET mobile = NULL, unionid = NULL, nickname = "已注销_${time}" WHERE id = ${userid}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserWeight = function (userid, weight) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET weight = ${weight} WHERE id = ${userid}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getShuDongList = function (page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id, a.msg, a.created, a.userid, b.hertz, b.gender FROM shudong a, user b WHERE a.status = 1 AND a.userid = b.id ORDER BY a.id DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getShuDongCount = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM shudong WHERE status = 1`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addShudong = function (userid, msg, time) {
    return new Promise((resolve, reject) => {
      time = time ? time : parseInt(Math.round(new Date().getTime() / 1000));
      const sql = `INSERT INTO shudong (userid, msg, created) VALUES (${userid}, '${msg}', ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addLiuyan = function (userid, to, msg, time) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO liuyan (userid, touserid, msg, created) VALUES (${userid}, ${to}, '${msg}', ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.addFankui = function (userid, to, msg, time) {
    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO fankui (userid, touserid, msg, created) VALUES (${userid}, ${to}, '${msg}', ${time})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLiuyanList = function (userid, page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id, a.msg, a.created, a.status, b.nickname, b.avatar, c.nickname AS tonickname, c.avatar AS toavatar, b.id AS userid, b.gender from liuyan a left JOIN user b ON a.userid = b.id LEFT JOIN user c on a.touserid = c.id WHERE a.touserid = ${userid} AND a.status != 2 ORDER BY a.id DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFankuiList = function (page, size) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT a.id, a.msg, a.created, a.status, b.nickname, b.avatar, c.nickname AS tonickname, c.avatar AS toavatar, b.id AS userid, b.gender from fankui a left JOIN user b ON a.userid = b.id LEFT JOIN user c on a.touserid = c.id WHERE a.status != 2 ORDER BY a.id DESC LIMIT ${size} OFFSET ${
        (page - 1) * size
      }`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLiuyanCount = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM liuyan WHERE touserid = ${userid} AND status != 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFankuiCount = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM fankui WHERE status != 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFankuiUnreadCount = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM fankui WHERE status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLiuyan = function (userid, lid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM liuyan WHERE id = ${lid} and touserid = ${userid} AND status != 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFankui = function (lid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT * FROM fankui WHERE id = ${lid} AND status != 2`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateLiuyan = function (lid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE liuyan SET status = 1 WHERE id = ${lid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateFankui = function (lid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE fankui SET status = 1 WHERE id = ${lid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.userInfoVerifyQA = function (phone) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT nickname, avatar, gender, mobile FROM user WHERE mobile = ${phone} UNION SELECT * FROM (SELECT nickname, avatar, gender, mobile FROM user WHERE state = 0 AND mobile != ${phone} AND nickname != "新用户" ORDER BY RAND() LIMIT 3) b`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.updateUserVerify = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE user SET verify = 1 WHERE id = ${userid}`;

      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getLiuyanUnreadCount = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM liuyan WHERE touserid = ${userid} AND status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.getFankuiUnreadCount = function () {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) AS count FROM fankui WHERE status = 0`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.deleteLiuyan = function (userid, lids) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE liuyan SET status = 2 WHERE id IN (${lids}) AND touserid = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  this.deleteFankui = function (lids) {
    return new Promise((resolve, reject) => {
      const sql = `UPDATE fankui SET status = 2 WHERE id IN (${lids})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  // 获取用户最近N条搜索内容
  this.getUserSearchHistory = function (userid, limit) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT search_content FROM user_search_history WHERE userid = ${userid} ORDER BY id DESC LIMIT ${limit}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  // 获取也搜索过这些内容的其他用户
  this.getUsersBySearchContents = function (searchContents, userid) {
    return new Promise((resolve, reject) => {
      if (!searchContents.length) return resolve([]);
      const contents = searchContents.map((c) => `'${c.search_content || c}'`).join(',');
      const sql = `SELECT DISTINCT userid FROM user_search_history WHERE search_content IN (${contents}) AND userid != ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  // 获取这些用户关注的用户
  this.getAttentionsByUserids = function (userids) {
    return new Promise((resolve, reject) => {
      if (!userids.length) return resolve([]);
      const ids = userids.join(',');
      const sql = `SELECT userid2 FROM attention WHERE userid1 IN (${ids})`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  // 获取当前用户已关注的用户
  this.getAttentionsByUserid = function (userid) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT userid2 FROM attention WHERE userid1 = ${userid}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  // 获取粉丝最多的用户（热门用户）
  this.getHotUsers = function (limit) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT u.id, u.nickname, u.avatar, COUNT(a.id) as fans_count FROM user u LEFT JOIN attention a ON u.id = a.userid2 GROUP BY u.id ORDER BY fans_count DESC LIMIT ${limit}`;
      executeQuery(sql).then((result) => {
        resolve(result);
      });
    });
  };

  /**
   * 举报后台
   */

  this.getReportList = function (from, size, status) {
    return new Promise((resolve, reject) => {
      const statusCondition = status !== undefined && status !== null ? `WHERE a.status = ${status}` : '';
      const sql = `SELECT a.*, b.nickname, b.avatar, c.nickname AS rnickname, c.avatar AS ravatar FROM report a LEFT JOIN user b ON a.userid = b.id LEFT JOIN user c ON a.reported_userid = c.id ${statusCondition} ORDER BY a.id DESC LIMIT ${from}, ${size}`;
      executeQuery(sql, true).then((result) => {
        resolve(result);
      });
    });
  };
}

const mysqlInstance = new Mysql();

module.exports.mysqlInstance = mysqlInstance;
