const ClickHouse = require('@apla/clickhouse');
const clickhouse = new ClickHouse({ host: '**********', port: 8123, user: 'default' })

function CH() {
    this.test = async function () {
        const { data } = await clickhouse.querying('SELECT * FROM test', { dataObjects: true })
        return data
    }

    this.addTest = async function () {
        const now = parseInt(new Date() / 1000)
        const { data } = await clickhouse.querying(`INSERT INTO test (mobile, text) VALUES (${now}, ${now})`, { dataObjects: true })
        return data
    }

    this.updateTest = async function (id) {
        const now = parseInt(new Date() / 1000)
        const { data } = await clickhouse.querying(`ALTER TABLE test UPDATE mobile = 111111111 WHERE id = ${id}`, { dataObjects: true })
        return data
    }

    this.delTest = async function (id) {
        const { data } = await clickhouse.querying(`ALTER TABLE test DELETE WHERE id = ${id}`, { dataObjects: true })
        return data
    }
}


const CHInstance = new CH();

module.exports.CHInstance = CHInstance;