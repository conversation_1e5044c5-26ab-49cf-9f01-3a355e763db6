const CryptoJS = require('crypto-js');
const { DefaultConfig } = require('../config/default');
const key = CryptoJS.enc.Utf8.parse(DefaultConfig.crypto.key);
const iv = CryptoJS.enc.Utf8.parse(DefaultConfig.crypto.iv);

//解密方法
module.exports.decipher = (word) => {
  let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decrypt = CryptoJS.AES.decrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
};

//加密方法
module.exports.encrypt = (word) => {
  let srcs = CryptoJS.enc.Utf8.parse(word);
  let encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 });
  return encrypted.ciphertext.toString().toUpperCase();
};

module.exports.decipherV2 = function (encryptedText, key) {
  try {
    const cryptoKey = CryptoJS.enc.Utf8.parse(key);
    const encryptedData = CryptoJS.enc.Base64.parse(encryptedText);

    const decrypted = CryptoJS.AES.decrypt({ ciphertext: encryptedData }, cryptoKey, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    throw error;
  }
};
