const qiniu = require('qiniu');
const co = require('co');
const { DefaultConfig } = require('../config/default');
const accessKey = DefaultConfig.qiniu.accessKey;
const secretKey = DefaultConfig.qiniu.secretKey;
const mac = new qiniu.auth.digest.Mac(accessKey, secretKey);

function Qiniu() {
  this.getCertificate = function (type, image_name) {
    return co(function* () {
      let bucket = null;
      let wailian = null;
      if (type == 1) {
        //type 1:52hz-avatar 2:52hz-images 3:替换头像 4:im图片 5:voice 6:video
        bucket = '52hz-avatar-new';
        wailian = DefaultConfig.wailian.AVATAR_DOMAIN;
      } else if (type == 2) {
        bucket = '52hz-images-new';
        wailian = DefaultConfig.wailian.IMAGE_DOMAIN;
      } else if (type == 3) {
        bucket = `52hz-avatar:${image_name}`;
        wailian = DefaultConfig.wailian.AVATAR_DOMAIN;
      } else if (type == 4) {
        bucket = 'imimage-new';
        wailian = DefaultConfig.wailian.IM_DOMAIN;
      } else if (type == 5) {
        bucket = '52hz-voice-new';
        wailian = DefaultConfig.wailian.VOICE_DOMAIN;
      } else if (type == 6) {
        bucket = '52hz-video-new';
        wailian = DefaultConfig.wailian.VIDEO_DOMAIN;
      }
      // console.log('bucket>>>>>', bucket);
      const options = {
        scope: bucket,
        expires: 300,
        callbackUrl: DefaultConfig.wailian.APP_DOMAIN + '/n/common/qiniu/callback',
        callbackBody: '{"key":"$(key)","hash":"$(etag)","fsize":$(fsize),"bucket":"$(bucket)","name":"$(x:name)"}',
        callbackBodyType: 'application/json',
      };
      const putPolicy = new qiniu.rs.PutPolicy(options);
      const uploadToken = putPolicy.uploadToken(mac);
      return {
        bucket: bucket,
        wailian: wailian,
        token: uploadToken,
      };
    });
  };
}

const qiniuInstance = new Qiniu();
module.exports.qiniuInstance = qiniuInstance;
