name: Publish Docker image on tag

on:
  push:
    tags:
      - '*'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: all

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          install: true

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GXZHAO }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v2
        with:
          context: .
          push: true
          tags: |
            ghcr.io/gxzhao/${{ github.event.repository.name }}:${{ github.ref_name }}
            ghcr.io/gxzhao/${{ github.event.repository.name }}:latest
