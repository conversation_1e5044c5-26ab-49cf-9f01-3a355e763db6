version: '3.7'
services:
  mysql:
    restart: always
    image: mysql:5.7
    container_name: 52mysql
    volumes:
      - /mnt/52mysql:/var/lib/mysql
      - /root/52hz/new52hz/my.cnf:/etc/my.cnf
    environment:
      - 'MYSQL_ROOT_PASSWORD=52hz2023.!@'
      - 'MYSQL_DATABASE=52hz'
      - 'TZ=Asia/Shanghai'
    ports:
      - 3306:3306
    networks:
      webapp-network:
        ipv4_address: ***********
        aliases:
          - mysql-net
  # mysql-slave:
  #   restart: always
  #   image: mysql:5.7
  #   container_name: 52mysql-slave
  #   volumes:
  #     - /mnt/52mysql:/var/lib/mysql
  #     - /root/52hz/new52hz/my.cnf:/etc/myslave.cnf
  #   environment:
  #     - 'MYSQL_ROOT_PASSWORD=52hz2023.!@'
  #     - 'MYSQL_DATABASE=52hz'
  #     - 'TZ=Asia/Shanghai'
  #   ports:
  #     - 3306:3306
  #   networks:
  #     webapp-network:
  #       ipv4_address: ***********
  #       aliases:
  #         - mysql-net
  redis:
    restart: always
    image: redis:7.0.4
    container_name: 52redis
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - 6380:6379
    environment:
      - REDIS_PASSWORD=Xiangqin1993
    volumes:
      - /mnt/redis/db:/data
      - /root/52hz/new52hz/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      webapp-network:
        ipv4_address: ***********
        aliases:
          - redis-net
  node:
    restart: always
    image: oven/bun:1.1.43
    container_name: 52hz
    depends_on:
      - mysql
      - redis
    ports:
      - 3690:3690
    working_dir: /app
    volumes:
      - /root/52hz/new52hz:/app
    command: bun app.js
    extra_hosts:
      - '52hz_service:***********'
      - 'redis:***********'
      - 'mysql:***********'
    networks:
      webapp-network:
        ipv4_address: ***********
  cron:
    restart: always
    image: oven/bun:1.1.43
    container_name: 52hz_cron
    depends_on:
      - mysql
      - redis
    working_dir: /app
    volumes:
      - /root/52hz/new52hz:/app
    command: bun cron.js
    extra_hosts:
      - '52hz_service:***********'
      - 'redis:***********'
      - 'mysql:***********'
    networks:
      webapp-network:
        ipv4_address: ***********
  # mysql8:
  #   restart: always
  #   image: mysql:8.4
  #   container_name: 52mysql8
  #   volumes:
  #     - /mnt/52mysql8:/var/lib/mysql
  #     - /root/52hz/new52hz/my8.cnf:/etc/my.cnf
  #   environment:
  #     - 'MYSQL_ROOT_PASSWORD=52hz2023.!@'
  #     - 'MYSQL_DATABASE=52hz'
  #     - 'TZ=Asia/Shanghai'
  #   ports:
  #     - 3307:3306
  #   networks:
  #     webapp-network:
  #       ipv4_address: ***********
  #       aliases:
  #         - mysql8-net
networks:
  webapp-network:
    name: webapp-network
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: '**********/16'
