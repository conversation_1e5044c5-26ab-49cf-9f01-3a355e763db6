'use strict';
const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;
const URLConfig = require('../config/urlConfig').URLConfig;
const decipherV2 = require('../tools/crypto').decipherV2;
// const key = '10sdlkdx981id0ka83h4df7s01';
const key = '52Hz@2024#Secure';
const { DefaultConfig } = require('../config/default');

const blackUserArr = DefaultConfig.blackUserArr;
const logUserid = DefaultConfig.logUserid;
const uidMapping = DefaultConfig.uidMapping;

function logUser(uid, request, Login = true) {
  if (Login) {
    if (logUserid.indexOf(uid) > -1) {
      if (request.method === 'GET') {
        slogger.debug('UID:', request._uid, '| PATH:', request.path, '| GET参数:', request.query);
      } else if (request.method === 'POST') {
        slogger.debug('UID:', request._uid, '| PATH:', request.path, '| POST参数:::', request.body);
      } else if (request.method === 'PUT') {
        slogger.debug('UID:', request._uid, '| PATH:', request.path, '| PUT参数:::', request.body);
      } else if (request.method === 'DELETE') {
        slogger.debug('UID:', request._uid, '| PATH:', request.path, '| DELETE参数:::', request.query);
      }
    }
  } else {
    if (request.method === 'GET') {
      slogger.debug('PATH:', request.path, '| GET参数:', request.query);
    } else if (request.method === 'POST') {
      slogger.debug('PATH:', request.path, '| POST参数:::', request.body);
    } else if (request.method === 'PUT') {
      slogger.debug('PATH:', request.path, '| PUT参数:::', request.body);
    } else if (request.method === 'DELETE') {
      slogger.debug('PATH:', request.path, '| DELETE参数:::', request.query);
    }
  }
}

function checkLogin(request) {
  const path = request.path;
  const method = request.method;
  const headers = request.headers;
  if (headers.ssid in uidMapping) {
    return true;
  }
  const encryptedData = headers['x-encrypted-data'];
  if (URLConfig.noLogin.indexOf(path) > -1) {
    return true;
  }
  if (!encryptedData) {
    return false;
  }
  try {
    const [timestamp, encryptedString] = encryptedData.split('_');
    const now = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    if (now - requestTime > 300) {
      return false;
    }
    const decryptedData = decipherV2(encryptedString, key);
    if (
      !decryptedData.includes(path) ||
      !decryptedData.includes(method.toLowerCase()) ||
      !decryptedData.includes(timestamp)
    ) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}

const loginMidWare = function* loginMidWare(next) {
  if (this.request.path.startsWith('/n/admin')) {
    if (!this.request.headers.aaid) {
      return (this.body = { code: 1005, msg: '不能进行该操作', data: [] });
    }
    yield next;
  } else {
    const isLogin = checkLogin(this.request);
    if (!isLogin) {
      return (this.body = { code: 1005, msg: '参数校验失败', data: [] });
    }
    const path = this.request.path;

    const ipStr = this.request.headers['X-Real-IP'] || this.request.headers['x-forwarded-for'];
    if (ipStr) {
      const ipArray = ipStr.split(',');
      if (ipArray.length > 1) {
        for (let i = 0; i < ipArray.length; i++) {
          const ipNumArray = ipArray[i].split('.');
          const tmp = ipNumArray[0] + '.' + ipNumArray[1];
          if (
            tmp == '192.168' ||
            (ipNumArray[0] == '172' && ipNumArray[1] >= 16 && ipNumArray[1] <= 32) ||
            tmp == '10.7'
          ) {
            continue;
          }
        }
      }
      this.request._ip = ipArray[0];
    }

    if (URLConfig.noLogin.indexOf(path) > -1) {
      logUser(null, this.request, false);
      const userAgent = this.request.header['user-agent'];
      this.request.userAgent = userAgent;
      yield next;
    } else if (URLConfig.bot.indexOf(path) > -1) {
      yield next;
    } else {
      const unicode = this.request.header.ssid;
      const parsedUnicode = parseInt(unicode);
      if (parsedUnicode in uidMapping) {
        this.request._uid = uidMapping[parsedUnicode];
        yield redisInstance.updateUserOnlineStatus(this.request._uid);
        logUser(this.request._uid, this.request);
        yield next;
      } else {
        this.request.ssid = unicode;
        const ssid = 'STR:52HZ:SESSION:' + unicode;
        if (unicode) {
          const sessionInfo = yield redisInstance.getSessionInfo(ssid);
          if (sessionInfo) {
            if (sessionInfo.split('@') instanceof Array) {
              if (blackUserArr.indexOf(parseInt(sessionInfo.split('@')[0])) > -1) {
                return (this.body = { code: 1003, msg: '帐号封禁 拒绝登录', data: [] });
              }
              this.request._uid = parseInt(sessionInfo.split('@')[0]);
              yield redisInstance.onlySet(`SET:LOGIN:STATUS:${this.request._uid}`, '1');
              logUser(this.request._uid, this.request);
              yield redisInstance.updateUserOnlineStatus(this.request._uid);
            }

            const reportCount = yield redisInstance.getUserReportCount(this.request._uid);
            if (reportCount > 5) {
              if (URLConfig.notUse.indexOf(path) > -1 && this.method === 'POST') {
                return (this.body = {
                  code: 1004,
                  msg: '当前账号被多人举报，涉及恶意使用，暂无该权限。有任何疑问请至 设置-联系管理员 中反馈。',
                  data: [],
                });
              }
            }
            if (URLConfig.notUse.indexOf(path) > -1 && this.method === 'POST') {
              let to_userid = null;
              switch (path) {
                case '/n/content/like':
                  to_userid = this.request.body.authorid;
                  break;
                case '/n/content/comment':
                  to_userid = this.request.body.to_userid;
                  break;
                case '/n/attention/add':
                  to_userid = this.request.body.attentionid;
                  break;
                case '/n/content/like/new':
                  to_userid = this.request.body.authorid;
                  break;
                case '/n/content/like/release':
                  to_userid = this.request.body.authorid;
                  break;
                case '/n/island/comment':
                  to_userid = this.request.body.authorid;
              }
              const rs = yield mysqlInstance.getBeBlackedRs(parseInt(to_userid), this.request._uid);
              if (rs.length) {
                return (this.body = { code: 1005, msg: '不能进行该操作', data: [] });
              }
            }
            // const time = new Date(new Date().toLocaleDateString()).getTime() / 1000;
            // const userActiveRs = yield mysqlInstance.getUserActive(this.request._uid, time);
            // if (userActiveRs.length) {
            //     yield mysqlInstance.updateUserActive(userActiveRs[0].id);
            // } else {
            //     yield mysqlInstance.insertUserActive(this.request._uid);
            // }
            yield next;
          } else {
            return (this.body = { code: 1003, msg: '未登录', data: [] });
          }
        }
      }
    }
  }
};

module.exports = loginMidWare;
