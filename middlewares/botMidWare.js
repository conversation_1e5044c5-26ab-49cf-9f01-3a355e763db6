'use strict';
const mysqlInstance = require('../models/mysql').mysqlInstance;
const URLConfig = require('../config/urlConfig').URLConfig;
const decipher = require('../tools/crypto').decipher;

const botMidWare = function* botMidWare(next) {
    const path = this.request.path;
    if (URLConfig.bot.indexOf(path) > -1) {
        let token = this.request.header['token']
        const botRs = yield mysqlInstance.getBotByToken(token)
        if (!botRs.length) {
            return this.body = { 'code': 1005, 'msg': 'Invalid bot token', 'data': [] };
        }
        this.request.token = token;
        token = decipher(token);
        const botName = token.split(':')[1];
        if (this.method === 'GET') {
            slogger.trace('BOT NAME:', botName, '>>>PATH:', path, '>>> GET参数:', this.request.query);
        } else if (this.method === 'POST') {
            slogger.trace('BOT NAME:', botName, '>>>PATH:', path, '>>> POST参数:::', this.request.body);
        } else if (this.method === 'PUT') {
            slogger.trace('BOT NAME:', botName, '>>>PATH:', path, '>>> PUT参数:::', this.request.query);
        } else if (this.method === 'DELETE') {
            slogger.trace('BOT NAME:', botName, '>>>PATH:', path, '>>> DELETE参数:::', this.request.query);
        }
        yield next;
    } else {
        yield next;
    }
}

module.exports = botMidWare;
