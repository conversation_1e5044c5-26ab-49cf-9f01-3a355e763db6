'use strict';

const { mysqlInstance, getPoolStatus, resetPoolStats } = require('../models/mysql');
const { DefaultConfig } = require('../config/default');

/**
 * MySQL主从读写分离测试用例
 */
class MySQLReadWriteSplitTest {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('=== MySQL主从读写分离测试开始 ===');
    console.log(`测试时间: ${new Date().toISOString()}`);
    console.log('');

    try {
      // 重置统计信息
      resetPoolStats();
      
      await this.testReadOperations();
      await this.testWriteOperations();
      await this.testMixedOperations();
      await this.testConnectionPoolStatus();
      await this.testHealthMonitoring();
      
      this.printTestSummary();
    } catch (error) {
      console.error('测试执行失败:', error);
    }
  }

  /**
   * 测试读操作（应该路由到从库）
   */
  async testReadOperations() {
    console.log('--- 测试读操作路由 ---');
    
    const readQueries = [
      'SELECT COUNT(*) as count FROM user',
      'SELECT * FROM user LIMIT 5',
      'SELECT id, nickname FROM user WHERE id = 1',
      'SHOW TABLES',
      'EXPLAIN SELECT * FROM user WHERE id = 1',
    ];

    for (const sql of readQueries) {
      try {
        const startTime = Date.now();
        const result = await mysqlInstance.query(sql);
        const responseTime = Date.now() - startTime;
        
        this.addTestResult('READ', sql, true, responseTime, result);
        console.log(`✓ 读操作成功: ${sql} (${responseTime}ms)`);
      } catch (error) {
        this.addTestResult('READ', sql, false, 0, error);
        console.log(`✗ 读操作失败: ${sql} - ${error.message}`);
      }
    }
    
    console.log('');
  }

  /**
   * 测试写操作（应该路由到主库）
   */
  async testWriteOperations() {
    console.log('--- 测试写操作路由 ---');
    
    const writeQueries = [
      'INSERT INTO test (mobile, text) VALUES (12345678901, "测试数据1")',
      'INSERT INTO test (mobile, text) VALUES (12345678902, "测试数据2")',
      'UPDATE test SET text = "更新测试数据" WHERE mobile = 12345678901',
      'DELETE FROM test WHERE mobile = 12345678902',
    ];

    for (const sql of writeQueries) {
      try {
        const startTime = Date.now();
        const result = await mysqlInstance.query(sql);
        const responseTime = Date.now() - startTime;
        
        this.addTestResult('WRITE', sql, true, responseTime, result);
        console.log(`✓ 写操作成功: ${sql} (${responseTime}ms)`);
      } catch (error) {
        this.addTestResult('WRITE', sql, false, 0, error);
        console.log(`✗ 写操作失败: ${sql} - ${error.message}`);
      }
    }
    
    console.log('');
  }

  /**
   * 测试混合操作
   */
  async testMixedOperations() {
    console.log('--- 测试混合操作 ---');
    
    const operations = [
      { type: 'READ', sql: 'SELECT COUNT(*) FROM content' },
      { type: 'WRITE', sql: 'INSERT INTO test (mobile, text) VALUES (12345678903, "混合测试")' },
      { type: 'READ', sql: 'SELECT * FROM test WHERE mobile = 12345678903' },
      { type: 'WRITE', sql: 'UPDATE test SET text = "混合测试更新" WHERE mobile = 12345678903' },
      { type: 'READ', sql: 'SELECT text FROM test WHERE mobile = 12345678903' },
    ];

    for (const op of operations) {
      try {
        const startTime = Date.now();
        const result = await mysqlInstance.query(op.sql);
        const responseTime = Date.now() - startTime;
        
        this.addTestResult(op.type, op.sql, true, responseTime, result);
        console.log(`✓ ${op.type}操作成功: ${responseTime}ms`);
      } catch (error) {
        this.addTestResult(op.type, op.sql, false, 0, error);
        console.log(`✗ ${op.type}操作失败: ${error.message}`);
      }
    }
    
    console.log('');
  }

  /**
   * 测试连接池状态
   */
  async testConnectionPoolStatus() {
    console.log('--- 连接池状态检查 ---');
    
    try {
      const status = getPoolStatus();
      
      console.log('主库连接池状态:');
      console.log(`  - 总连接数: ${status.pools.master.connected}`);
      console.log(`  - 空闲连接数: ${status.pools.master.free}`);
      console.log(`  - 连接限制: ${status.pools.master.config.connectionLimit}`);
      console.log(`  - 主机: ${status.pools.master.config.host}`);
      
      console.log('从库连接池状态:');
      status.pools.slaves.forEach((slave, index) => {
        console.log(`  从库${index}:`);
        console.log(`    - 总连接数: ${slave.connected}`);
        console.log(`    - 空闲连接数: ${slave.free}`);
        console.log(`    - 连接限制: ${slave.config.connectionLimit}`);
        console.log(`    - 主机: ${slave.config.host}`);
      });
      
      console.log('查询统计:');
      console.log(`  - 总查询数: ${status.stats.totalQueries}`);
      console.log(`  - 主库查询数: ${status.stats.masterQueries}`);
      console.log(`  - 从库查询数: ${status.stats.slaveQueries}`);
      console.log(`  - 错误数: ${status.stats.errors}`);
      console.log(`  - 慢查询数: ${status.stats.slowQueries}`);
      console.log(`  - 平均响应时间: ${status.stats.avgResponseTime.toFixed(2)}ms`);
      
      if (status.health) {
        console.log('健康状态:');
        console.log(`  - 主库健康: ${status.health.master.healthy ? '✓' : '✗'}`);
        console.log(`  - 健康从库数: ${status.health.totalHealthySlaves}/${status.health.totalSlaves}`);
      }
      
      this.addTestResult('STATUS', '连接池状态检查', true, 0, status);
    } catch (error) {
      this.addTestResult('STATUS', '连接池状态检查', false, 0, error);
      console.log(`✗ 状态检查失败: ${error.message}`);
    }
    
    console.log('');
  }

  /**
   * 测试健康监控
   */
  async testHealthMonitoring() {
    console.log('--- 健康监控测试 ---');
    
    try {
      const status = getPoolStatus();
      
      if (status.monitoring.enabled) {
        console.log('✓ 监控已启用');
        console.log(`  - 健康检查间隔: ${status.monitoring.healthCheckInterval}ms`);
        console.log(`  - 慢查询阈值: ${status.monitoring.slowQueryThreshold}ms`);
        
        if (status.health) {
          console.log('✓ 健康状态监控正常');
          console.log(`  - 最后检查时间: ${status.health.master.lastCheck}`);
          console.log(`  - 主库错误计数: ${status.health.master.errorCount}`);
        }
        
        this.addTestResult('HEALTH', '健康监控检查', true, 0, status.health);
      } else {
        console.log('⚠ 监控未启用');
        this.addTestResult('HEALTH', '健康监控检查', false, 0, '监控未启用');
      }
    } catch (error) {
      this.addTestResult('HEALTH', '健康监控检查', false, 0, error);
      console.log(`✗ 健康监控测试失败: ${error.message}`);
    }
    
    console.log('');
  }

  /**
   * 添加测试结果
   */
  addTestResult(type, operation, success, responseTime, result) {
    this.testResults.push({
      type,
      operation,
      success,
      responseTime,
      result: success ? 'SUCCESS' : result?.message || result,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 打印测试总结
   */
  printTestSummary() {
    const totalTime = Date.now() - this.startTime;
    const totalTests = this.testResults.length;
    const successTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - successTests;
    
    console.log('=== 测试总结 ===');
    console.log(`总测试数: ${totalTests}`);
    console.log(`成功: ${successTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${((successTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`总耗时: ${totalTime}ms`);
    
    // 按类型统计
    const typeStats = {};
    this.testResults.forEach(result => {
      if (!typeStats[result.type]) {
        typeStats[result.type] = { total: 0, success: 0 };
      }
      typeStats[result.type].total++;
      if (result.success) {
        typeStats[result.type].success++;
      }
    });
    
    console.log('\n按操作类型统计:');
    Object.entries(typeStats).forEach(([type, stats]) => {
      console.log(`  ${type}: ${stats.success}/${stats.total} (${((stats.success / stats.total) * 100).toFixed(1)}%)`);
    });
    
    // 显示失败的测试
    const failedResults = this.testResults.filter(r => !r.success);
    if (failedResults.length > 0) {
      console.log('\n失败的测试:');
      failedResults.forEach(result => {
        console.log(`  ✗ [${result.type}] ${result.operation}: ${result.result}`);
      });
    }
    
    console.log('\n=== 测试完成 ===');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const test = new MySQLReadWriteSplitTest();
  test.runAllTests().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = MySQLReadWriteSplitTest;
