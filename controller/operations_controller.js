'use strict';
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const OPERATIONS_WAILIAN = DefaultConfig.wailian.OPERATIONS_DOMAIN;

class operations<PERSON><PERSON>roller extends BaseController {
  constructor(props) {
    super(props);
  }

  async addOperations(text, images, title) {
    await mysqlInstance.addOperations(text, images, title);
    return {
      backSuccess: true,
      data: [],
    };
  }

  async getOperations(page, size) {
    const count = await mysqlInstance.getOperationsCount();
    const result = await mysqlInstance.getOperations(page, size);
    for (let i = 0; i < result.length; i++) {
      let imageArrStr = '';
      if (result[i].images) {
        const images = result[i].images;
        const imagesArr_ = images.split(',');
        if (imagesArr_.length) {
          imageArrStr += `${WAILIAN}${imagesArr_[0]}`;
        }
      }
      result[i].images = imageArrStr;
      delete result[i].title_set;
      delete result[i].content_set;
    }

    const data = {
      page: `${page}`,
      size: `${size}`,
      count: `${count[0].count}`,
      data: result,
    };

    return {
      backSuccess: true,
      data: data,
    };
  }

  async getOperationsDetail(id) {
    const result = await mysqlInstance.getOperationsDetail(id);
    let imageArrStr = '';
    if (result.length && result[0].images) {
      const images = result[0].images;
      const imagesArr_ = images.split(',');
      for (let i = 0; i < imagesArr_.length; i++) {
        if (i === imagesArr_.length - 1) {
          imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
        } else {
          imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
        }
      }
      result[0].images = imageArrStr;
      if (result[0].title_set) {
        result[0].title_set = JSON.parse(result[0].title_set);
      }
      if (result[0].content_set) {
        result[0].content_set = JSON.parse(result[0].content_set);
      }
    }
    return {
      backSuccess: true,
      data: result.length ? result[0] : {},
    };
  }
}

module.exports.operationsInstance = new operationsController();
