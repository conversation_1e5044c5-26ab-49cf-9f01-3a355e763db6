'use strict';
const co = require('co');
const { redisInstance } = require('../models/redis');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const { DefaultConfig } = require('../config/default');
// const CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
const CHARS = DefaultConfig.redis.CHARS;
const CONTENTADD = DefaultConfig.redis.CONTENTADD;
const LIKEADD = DefaultConfig.redis.LIKEADD;
const algorithm = DefaultConfig.redis.ALGORITHM;
const LIKE = DefaultConfig.redis.LIKE;
const CONTENT = DefaultConfig.redis.CONTENT;
const imService = require('../services/IM_service').IMInstance;

module.exports = class BaseController {
  getRandomCode(min = 1000, max = 9999) {
    return Math.round(Math.random() * (max - min) + min);
  }

  async batchGetColorFont(userIds) {
    if (!userIds || userIds.length === 0) {
      return {};
    }

    // 假设现有的 getColorFont 是单用户查询
    // 这里实现一个批量版本
    const colorFontMap = {};
    const promises = userIds.map(async (userId) => {
      const colorFont = await this.getColorFont(userId);
      colorFontMap[userId] = colorFont;
    });

    await Promise.all(promises);
    return colorFontMap;
  }

  getColorFont(userid) {
    return co(function* () {
      userid = parseInt(userid);
      // const rs = yield mysqlInstance.getColorFont(userid);
      const body = {
        type: 0,
        expire_time: 0,
        status: 0,
      };

      // const now = parseInt(new Date() / 1000);
      // if (rs.length) {
      //   body['type'] = rs[0].type;
      //   body['expire_time'] = rs[0].expire_time;
      //   body['status'] = rs[0].expire_time > now ? 1 : 0;
      // }

      return body;
    });
  }

  baseVaryHertz(userid, otherid) {
    return co(function* () {
      userid = parseInt(userid);
      otherid = parseInt(otherid);
      const rs = yield mysqlInstance.getUserTag(otherid);
      if (rs && rs['type'] === 'error') {
        return;
      }
      if (rs.length) {
        const tagArrStr = rs[0]['tagArr'];
        const likeTagArrStr = rs[0]['likeTagArr'];
        const tag = `${tagArrStr},${likeTagArrStr}`;
        yield mysqlInstance.addUserVaryAttribute(userid, tag);
      }
    });
  }

  toLiteral(str) {
    var dict = { '\b': 'b', '\t': 't', '\n': 'n', '\v': 'v', '\f': 'f', '\r': 'r' };
    return str.replace(/([\\'"\b\t\n\v\f\r])/g, function ($0, $1) {
      return '\\' + (dict[$1] || $1);
    });
  }

  getCaption(obj) {
    const index1 = obj.lastIndexOf('/song/');
    let index2 = obj.lastIndexOf('/?');
    if (index2 < 0) {
      index2 = obj.lastIndexOf('?');
    }
    obj = obj.substring(index1 + 6, index2);
    return obj;
  }

  httpString(s) {
    var reg = /(http:\/\/|https:\/\/)((\w|=|\?|\.|\/|&|-)+)/g;
    var reg = /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
    s = s.match(reg);
    return s;
  }

  getCaptionFun(obj) {
    const index = obj.lastIndexOf('<metaitemprop="name"content="');
    const index2 = obj.indexOf('"/><metaitemprop="description"content="');

    const index3 = obj.lastIndexOf('"/><metaitemprop="description"content="');
    const index4 = obj.indexOf('"/><metaitemprop="image"content="');

    const index5 = obj.lastIndexOf('"/><metaitemprop="image"content="');
    const index6 = obj.indexOf('"/><scripttype="application/ld+json">');
    const index7 = obj.lastIndexOf('"m4aUrl":"');
    const index8 = obj.indexOf('","singer":[{"');
    const name = obj.substring(index + 29, index2);
    const name2 = obj.substring(index3 + 39, index4);
    const name3 = obj.substring(index5 + 33, index6);
    const name4 = obj.substring(index7 + 10, index8);
    const data = {
      musicName: name || null,
      albumName: '',
      artists: name2 || null,
      imgUrl: name3 || null,
      musicMp3Url: name4 || null,
      from: 3,
    };
    return data;
  }

  generateRandomNum(min, max) {
    if (max === undefined || min === undefined) {
      return false;
    }
    let num = Math.floor(Math.random() * (max - min + 1) + min);
    return num;
  }

  generateCode(len) {
    len = len || 32;
    const maxPos = CHARS.length;
    let code = '';
    for (let i = 0; i < len; i++) {
      //0~61的整数
      const randomNum = this.generateRandomNum(0, maxPos - 1);
      const char = CHARS.charAt(randomNum);
      code += char;
    }
    return code;
  }

  getInvitCode() {
    const self = this;
    return co(function* () {
      let rs = true;
      let code = null;
      do {
        code = self.generateCode(6);
        const codeRs = yield mysqlInstance.getUserInvitationCode(code);
        if (!codeRs.length) {
          rs = false;
        }
        if ([514345, 885779, 462013].indexOf(code) > 0) {
          rs = true;
        }
      } while (rs);
      return code;
    });
  }

  returnFloat(value) {
    const s = value.toString().split('.');
    if (s.length == 1) {
      value = value.toString() + '.00';
      return value;
    }
    if (s.length > 1) {
      if (s[1].length < 2) {
        value = value.toString() + '0';
      }
      return value;
    }
  }

  /**
   *
   * @param {*} userid
   * @param {*} type 1: 发表评论 2: 点赞
   * 用户发表动态10-20次动态的时候，hz频率 ± 0.01-0.2 用户点赞20-40次 hz频率± 0.01-0.05 不能多于89.99和小于10.00
   */
  changeUserHertz(userid, type) {
    return co(function* () {
      const key = `HASH:USER:HERTZ:TIME:${userid}`;
      const redisRS = yield redisInstance.hashGet(key);
      const userHertzRs = yield mysqlInstance.getUserHertzByUserId(userid);
      let userHertz = parseFloat(userHertzRs[0].hertz);
      if (type === 1) {
        if (!redisRS) {
          yield redisInstance.hashAdd(key, ['contentnum', 1, 'likenum', 0]);
        } else {
          const contentnum = parseInt(redisRS['contentnum']);
          if (contentnum >= 20 && contentnum <= 30) {
            if (contentnum === 30) {
              if (userHertz > 11 && userHertz < 88) {
                const algorithmType = algorithm[Math.floor(Math.random() * algorithm.length)];
                const contentFen = CONTENT[Math.floor(Math.random() * CONTENT.length)];
                if (algorithmType) {
                  userHertz = (userHertz + contentFen).toFixed(2);
                } else {
                  userHertz = (userHertz - contentFen).toFixed(2);
                }
                yield mysqlInstance.updateUserHertz(userid, userHertz);
                yield redisInstance.hashAdd(`HASH:USER:INFO:${userid}`, ['hertz', `${userHertz}`]);
                yield redisInstance.hashAdd(key, ['contentnum', 0]);
                const msg =
                  'hi，你的频率有了一点变化。\n你在海底的吐泡与互动行为，将让我们更了解你，不断调整，贴近你的真实频率。';
                yield imService.sendBulk(msg, [`${userid}`]);
              }
            } else {
              const add = CONTENTADD[Math.floor(Math.random() * CONTENTADD.length)];
              if (add === 1) {
                if (userHertz > 11 && userHertz < 88) {
                  const algorithmType = algorithm[Math.floor(Math.random() * algorithm.length)];
                  const contentFen = CONTENT[Math.floor(Math.random() * CONTENT.length)];
                  if (algorithmType) {
                    userHertz = (userHertz + contentFen).toFixed(2);
                  } else {
                    userHertz = (userHertz - contentFen).toFixed(2);
                  }
                  yield mysqlInstance.updateUserHertz(userid, userHertz);
                  yield redisInstance.hashAdd(`HASH:USER:INFO:${userid}`, ['hertz', `${userHertz}`]);
                  yield redisInstance.hashAdd(key, ['contentnum', 0]);
                  const msg =
                    'hi，你的频率有了一点变化。\n你在海底的吐泡与互动行为，将让我们更了解你，不断调整，贴近你的真实频率。';
                  yield imService.sendBulk(msg, [`${userid}`]);
                } else {
                  yield redisInstance.hashAdd(key, ['contentnum', contentnum + 1]);
                }
              } else {
                yield redisInstance.hashAdd(key, ['contentnum', contentnum + 1]);
              }
            }
          } else {
            yield redisInstance.hashAdd(key, ['contentnum', contentnum + 1]);
          }
        }
      } else if (type === 2) {
        if (!redisRS) {
          yield redisInstance.hashAdd(key, ['contentnum', 0, 'likenum', 1]);
        } else {
          const likenum = parseInt(redisRS['likenum']);
          if (likenum >= 50 && likenum <= 60) {
            if (likenum === 60) {
              if (userHertz > 11 && userHertz < 88) {
                const algorithmType = algorithm[Math.floor(Math.random() * algorithm.length)];
                const likeFen = LIKE[Math.floor(Math.random() * LIKE.length)];
                if (algorithmType) {
                  userHertz = (userHertz + likeFen).toFixed(2);
                } else {
                  userHertz = (userHertz - likeFen).toFixed(2);
                }
                yield mysqlInstance.updateUserHertz(userid, userHertz);
                yield redisInstance.hashAdd(`HASH:USER:INFO:${userid}`, ['hertz', `${userHertz}`]);
                yield redisInstance.hashAdd(key, ['likenum', 0]);
                const msg =
                  'hi，你的频率有了一点变化。\n你在海底的吐泡与互动行为，将让我们更了解你，不断调整，贴近你的真实频率。';
                yield imService.sendBulk(msg, [`${userid}`]);
              }
            } else {
              const add = LIKEADD[Math.floor(Math.random() * LIKEADD.length)];
              if (add === 1) {
                if (userHertz > 11 && userHertz < 88) {
                  const algorithmType = algorithm[Math.floor(Math.random() * algorithm.length)];
                  const likeFen = LIKE[Math.floor(Math.random() * LIKE.length)];
                  if (algorithmType) {
                    userHertz = (userHertz + likeFen).toFixed(2);
                  } else {
                    userHertz = (userHertz - likeFen).toFixed(2);
                  }
                  yield mysqlInstance.updateUserHertz(userid, userHertz);
                  yield redisInstance.hashAdd(`HASH:USER:INFO:${userid}`, ['hertz', `${userHertz}`]);
                  yield redisInstance.hashAdd(key, ['likenum', 0]);
                  const msg =
                    'hi，你的频率有了一点变化。\n你在海底的吐泡与互动行为，将让我们更了解你，不断调整，贴近你的真实频率。';
                  yield imService.sendBulk(msg, [`${userid}`]);
                } else {
                  yield redisInstance.hashAdd(key, ['likenum', likenum + 1]);
                }
              } else {
                yield redisInstance.hashAdd(key, ['likenum', likenum + 1]);
              }
            }
          } else {
            yield redisInstance.hashAdd(key, ['likenum', likenum + 1]);
          }
        }
      }
    });
  }
  /**
   *
   * @param {*} userid
   * @param {*} nickname
   * @param {*} avatar
   * SOCKET:USER:NAMESPACE:A
   * SOCKET:USER:NAMESPACE:ALL
   * SET:ALL:SOCKET:FOOD:STATISTICS
   * SOCKET:NAMESPACE:ALL
   */
  updateWebRedis(userid, nickname, avatar, gender) {
    return co(function* () {
      gender = `${gender}`;
      avatar = `${DefaultConfig.wailian.AVATAR_DOMAIN}${avatar}`;
      const key1 = 'SOCKET:NAMESPACE:ALL';
      const key2 = 'SOCKET:USER:NAMESPACE:';
      const key3 = 'SOCKET:USER:NAMESPACE:ALL';
      const key4 = 'SET:ALL:SOCKET:FOOD:STATISTICS';
      const key5 = 'HASH:SOCKET:FOOD:STATISTICS';
      let path = null;
      const redisRS = yield redisInstance.hashGet(key1);
      for (const key in redisRS) {
        let value = JSON.parse(redisRS[key]);
        for (let i = 0; i < value.length; i++) {
          const userid_ = parseInt(value[i]);
          if (userid === userid_) {
            path = key;
          }
        }
      }
      if (path) {
        const redisRS = yield redisInstance.hmget(`${key2}${path}`, `${userid}`);
        if (redisRS && redisRS[0]) {
          const userInfo = JSON.parse(redisRS[0]);
          userInfo.nickname = nickname;
          userInfo.avatar = avatar;
          userInfo.gender = gender;
          yield redisInstance.hashAdd(`${key2}${path}`, [`${userid}`, JSON.stringify(userInfo)]);
        }
      }
      const redisRS_ = yield redisInstance.hmget(key3, `${userid}`);
      if (redisRS_ && redisRS_[0]) {
        const userInfo = JSON.parse(redisRS_[0]);
        userInfo.nickname = nickname;
        userInfo.avatar = avatar;
        userInfo.gender = gender;
        yield redisInstance.hashAdd(key3, [`${userid}`, JSON.stringify(userInfo)]);
      }

      let allRs = yield redisInstance.get(key4);
      allRs = JSON.parse(allRs);
      for (let i = 0; i < allRs.length; i++) {
        if (parseInt(allRs[i].userid) === userid) {
          allRs[i].avatar = avatar;
          allRs[i].nickname = nickname;
          allRs[i].gender = gender;
        }
      }

      const allArr__ = JSON.stringify(allRs);
      yield redisInstance.delKey(key4);
      yield redisInstance.onlySet(key4, allArr__);

      const redisRS__ = yield redisInstance.hmget(key5, `${userid}`);
      if (redisRS__ && redisRS__[0]) {
        const userInfo = JSON.parse(redisRS__[0]);
        userInfo.nickname = nickname;
        userInfo.avatar = avatar;
        userInfo.gender = gender;
        yield redisInstance.hashAdd(key5, [`${userid}`, JSON.stringify(userInfo)]);
      }
    });
  }

  sendCodeBase(mobile, code) {
    return new Promise(function (resolve, reject) {
      //发送短信
      smsClient
        .sendSMS({
          PhoneNumbers: mobile,
          SignName: '博味',
          TemplateCode: 'SMS_121857568', // 'SMS_149097393', //SMS_166080190
          TemplateParam: '{"code":"' + code + '"}',
        })
        .then(
          function (res) {
            const { Code } = res;
            if (Code === 'OK') {
              //处理返回参数
              // elogger.debug(res);
              resolve(true);
            }
          },
          function (err) {
            elogger.error(`${mobile} sendCodeBase ERROR : ${err}`);
            // resolve(false);
            resolve(true); // TODO 记得改回来
          },
        );
    });
  }
};
