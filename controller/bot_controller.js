'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const encrypt = require('../tools/crypto').encrypt;
const decipher = require('../tools/crypto').decipher;
const request = require('request');
const { DefaultConfig } = require('../config/default');
const AVATAR_WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;

class BotController extends BaseController {
  constructor(props) {
    super(props);
  }

  botFun(userid, step, key) {
    const self = this;
    return co(function* () {
      if (!step) {
        if (key === '/start') {
          const msg =
            '我可以帮助您创建和管理Telegram机器人。\n您可以通过发送以下命令来控制我：\n\n/newbot 创建一个新的机器人\n\n编辑机器人\nsetname 更改机器人的名称\n/setdescription 更改机器人说明\n/setcommands 更改命令列表';
          step = encrypt(`2:${userid}:${parseInt(new Date() / 1000)}`);
          const data = { msg, step };
          return {
            backSuccess: true,
            data: data,
          };
        } else {
          return {
            backSuccess: false,
            msg: '请输入正确的命令',
          };
        }
      } else if (step === 2) {
        const msg =
          'OK, 现在有一个新的机器人。我们该怎么称呼它？请为您的机器人选择一个名称。(名称仅限于字母数字，长度不能超过20字节)';
        step = encrypt(`3:${userid}:${parseInt(new Date() / 1000)}`);
        const data = { msg, step };
        return {
          backSuccess: true,
          data: data,
        };
      } else if (step === 3) {
        if (!key) {
          return {
            backSuccess: false,
            msg: '机器人名称不能为空！',
          };
        }
        const isletter = /^[a-zA-Z0-9]+$/.test(key);
        if (!isletter) {
          return {
            backSuccess: false,
            msg: '机器人名称不符合规则',
          };
        }
        if (key.length > 20) {
          return {
            backSuccess: false,
            msg: '机器人名称不符合规则',
          };
        }

        const botNameRs = yield mysqlInstance.getBotByName(key);
        if (botNameRs.length) {
          return {
            backSuccess: false,
            msg: '该名称已被占用',
          };
        }
        const token = encrypt(`${userid}:${key}:${parseInt(new Date() / 1000)}`);
        yield mysqlInstance.addBot(userid, key, token);
        step = encrypt(`3:${userid}:${parseInt(new Date() / 1000)}`);
        const data = {
          msg: `祝贺您成功创建新机器人。\n\n使用此令牌访问HTTP API:\n${token}\n确保令牌安全并安全存储，任何人都可以使用它来控制您的机器人。\n\n有关Bot API的描述，请参见以下页面: \nhttps://core.telegram.org/bots/api`,
          step: null,
        };
        return {
          backSuccess: true,
          data: data,
        };
      }
    });
  }

  botLogin(token, url) {
    return co(function* () {
      const botRs = yield mysqlInstance.getBotByToken(token);
      if (!botRs.length) {
        return {
          backSuccess: false,
          msg: '不存在该机器人',
        };
      }
      yield mysqlInstance.updateBotUrl(token, url);
      return {
        backSuccess: true,
      };
    });
  }

  getUserInfoById(userid) {
    return co(function* () {
      const user = yield mysqlInstance.getUserInfoByUserid(userid);
      if (!user.length) {
        return {
          backSuccess: false,
          msg: '不存在该用户',
        };
      }
      const data = {
        userid: userid,
        nick_name: user[0].nickname,
        gender: user[0].gender,
        avatar: `${AVATAR_WAILIAN}${user[0].avatar}`,
        signature: user[0].signature,
        hertz: user[0].hertz,
      };

      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  botTest() {
    return co(function* () {
      const botRs = yield mysqlInstance.getBotUrlById(2);
      const url = botRs[0].url;
      const rs = yield createRequest(url);
      if (rs['result']) {
        return {
          backSuccess: true,
        };
      } else {
        return {
          backSuccess: false,
        };
      }
    });
  }
}

function createRequest(url) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'POST',
      url: url,
      headers: {
        'cache-control': 'no-cache',
      },
      body: {
        function: 'searchByContents',
        params: { data: [{ item_id: parseInt(data['id']), content: data['content'] }] },
      },
      json: true,
    };
    request(options, function (error, response, body) {
      if (error) {
        resolve({
          result: false,
        });
      }

      if (body && typeof body === 'string') {
        body = JSON.parse(body);
      }

      if (body && parseInt(body.code) === 0) {
        resolve({
          result: true,
        });
      } else {
        resolve({
          result: false,
          error: body['msg'],
        });
      }
    });
  });
}

module.exports.botInstance = new BotController();
