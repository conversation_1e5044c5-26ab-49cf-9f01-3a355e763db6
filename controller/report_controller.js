'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;

class ReportController extends BaseController {
  constructor(props) {
    super(props);
  }

  getUserAndReporter(userid, reported_userid) {
    return co(function* () {
      const rs = yield mysqlInstance.getUserAndReporter(userid, reported_userid);
      return {
        nickname: rs[0].nickname,
        reportName: rs[0].reportName,
      };
    });
  }

  getReport(userid, reported_userid, type, rid) {
    return co(function* () {
      const rs = yield mysqlInstance.getReportV2(userid, reported_userid, type, rid);
      if (rs.length) {
        return false;
      }
      return true;
    });
  }

  postReport(args) {
    return co(function* () {
      const userinfo = yield mysqlInstance.getUserInfo(args['reported_userid']);
      if (userinfo && userinfo['type'] === 'error') {
        elogger.error('Report Error 1001 ' + userinfo['msg']);
        return {
          backSuccess: false,
          msg: 'Report Error 1001',
        };
      }
      if (!userinfo.length) {
        return {
          backSuccess: false,
          msg: 'Report Error 1002 该用户不存在',
        };
      }

      const rs = yield mysqlInstance.getReport(args);
      if (rs.length) {
        return {
          backSuccess: true,
        };
      } else {
        const result = yield mysqlInstance.postReport(args);
        if (result && result['type'] === 'error') {
          elogger.error('Report Error 1003 ' + result['msg']);
          return {
            backSuccess: false,
            msg: 'Report Error 1003',
          };
        }
        yield redisInstance.addReportCount(args['reported_userid']);
        return {
          backSuccess: true,
        };
      }
    });
  }
}

module.exports.reportInstance = new ReportController();
