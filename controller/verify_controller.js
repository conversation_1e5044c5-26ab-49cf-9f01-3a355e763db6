'use strict';
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const moment = require('moment');
class VerifyController extends BaseController {
  constructor(props) {
    super(props);
  }

  async incomeStatistics(startTime, endTime) {
    const result = await mysqlInstance.getIncomeStatistics(startTime, endTime);
    const arr = [];
    for (let i = 0; i < result.length; i++) {
      const data = {
        nickname: result[i].nickname,
        iap_id: result[i].iap_id,
        price: result[i].price,
        create_time: moment(result[i].create_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        update_time: moment(result[i].update_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
      };
      arr.push(data);
    }

    const result_ = await mysqlInstance.getIncomeStatisticsSum(startTime, endTime);
    const income = result_[0].Income;

    const result__ = await mysqlInstance.getIncomeStatisticsCount(startTime, endTime);
    const count = result__[0].count;
    const args = {
      record: arr,
      count: count,
      income: income || 0,
    };
    return {
      backSuccess: true,
      data: args,
    };
  }
}

module.exports.verifyInstance = new VerifyController();
