'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');
const VOICEWAILIAN = DefaultConfig.wailian.VOICE_DOMAIN;
const IMAGESWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const AVATARWAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const ISLANDWAILIAN = DefaultConfig.wailian.DOC_DOMAIN;
const imService = require('../services/IM_service').IMInstance;
const youmengService = require('../services/youmeng').YMInstance;

class IslandController extends BaseController {
  constructor(props) {
    super(props);
  }

  async getWaters() {
    const rs = await mysqlInstance.getWaters();
    if (rs && rs['type'] === 'error') {
      elogger.error('Island Error 1001 ' + userinfo['msg']);
      return {
        backSuccess: false,
        msg: 'Island Error 1001',
      };
    }
    const arr = [];
    for (let i = 0; i < rs.length; i++) {
      const data = {
        islandId: `${rs[i].id}`,
        name: rs[i].name,
        describe: rs[i].describe,
        type: `${rs[i].type}`,
        warp_latitude: rs[i].warp_latitude,
        slogan: rs[i].slogan,
        picture: `${ISLANDWAILIAN}${rs[i].picture}`,
      };
      arr.push(data);
    }
    for (let i = 0; i < arr.length; i++) {
      for (let j = i; j < arr.length; j++) {
        if (parseFloat(arr[i]['warp_latitude']) > parseFloat(arr[j]['warp_latitude'])) {
          let min = arr[j];
          arr[j] = arr[i];
          arr[i] = min;
        }
      }
    }

    return {
      backSuccess: true,
      data: arr,
    };
  }

  async postDiscuss(args) {
    const self = this;
    args['title'] = self.toLiteral(args['title']);
    args['content'] = self.toLiteral(args['content']);
    args['time'] = parseInt(Math.round(new Date().getTime() / 1000));

    const discussId = await mysqlInstance.postDiscuss(args);
    if (discussId && discussId['type'] === 'error') {
      elogger.error('Island Error 1002 ' + discussId['sql']);
      return {
        backSuccess: false,
        msg: 'Island Error 1002',
      };
    }
    const userinfo = await mysqlInstance.getUserInfoByUserid(args['userid']);
    if (userinfo && userinfo['type'] === 'error') {
      elogger.error('Island Error 1002-3 ' + userinfo['sql']);
      return {
        backSuccess: false,
        msg: 'Island Error 1002-3',
      };
    }
    const redisRs = await redisInstance.hashAdd('HASH:DISSCUSS:INFO:' + discussId, [
      'userid',
      args['userid'],
      'title',
      args['title'],
      'content',
      args['content'],
      'commentNum',
      '0',
      'create_time',
      args['time'],
      'island_type',
      args['island_type'],
      'voice_type',
      args['voice_type'],
      'status',
      '0',
      'heat_value',
      '0',
      'nickname',
      userinfo[0].nickname,
      'avatar',
      userinfo[0].avatar,
      'gender',
      userinfo[0].gender,
    ]);
    if (redisRs && redisRs['type'] === 'error') {
      elogger.error(`HASH:DISSCUSS:INFO:>>${redisRs['msg']},discussId>>${discussId}`);
      return {
        backSuccess: false,
        msg: 'Island Error 1002-2',
      };
    }

    const data = {
      discussId: `${discussId}`,
      anthorId: `${args['userid']}`,
      island_type: `${args['island_type']}`,
      voice_type: `${args['voice_type']}`,
      title: args['title'],
      content: args['content'],
      create_time: `${args['time']}`,
      status: '0',
      heat_value: '0',
    };
    return {
      backSuccess: true,
      data: [data],
    };
  }

  async delIslandContent(args) {
    const searchRs = await mysqlInstance.searchIslandContentByUserid(args['userid'], args['discussId']);
    if (searchRs && searchRs['type'] === 'error') {
      elogger.error('Island Error 1015-1 ' + searchRs['msg']);
      return {
        backSuccess: false,
        msg: 'Island Error 1015-1',
      };
    }
    if (!searchRs.length) {
      return {
        backSuccess: false,
        msg: 'Island Error 1015-2 没有此条声波',
      };
    }
    const rs = await mysqlInstance.delIslandContent(args['userid'], args['discussId']);
    if (rs && rs['type'] === 'error') {
      elogger.error('Island Error 1015-3 ' + rs['msg']);
      return {
        backSuccess: false,
        msg: 'Island Error 1015-3',
      };
    }
    const redisRs = await redisInstance.hashDel('HASH:DISSCUSS:INFO:' + args['discussId']);
    if (redisRs && redisRs['type'] === 'error') {
      elogger.error(`HASH:DISSCUSS:INFO:>>${redisRs['msg']},discussId>>${args['discussId']}`);
      return {
        backSuccess: false,
        msg: 'Island Error 1015-4',
      };
    }
    return {
      backSuccess: true,
      data: [],
    };
  }

  async getDiscussByWater(args) {
    const rs = await mysqlInstance.getDiscussByWater(args);
    if (rs && rs['type'] === 'error') {
      elogger.error('Island Error 1003 ' + rs['sql']);
      return {
        backSuccess: false,
        msg: 'Island Error 1003',
      };
    }
    const countRs = await mysqlInstance.getDiscussByWaterCount(args);
    const count = countRs[0].count;
    const arr = [];
    for (let i = 0; i < rs.length; i++) {
      const commentNumRs = await mysqlInstance.getDiscussCommentNum(rs[i].id);
      if (commentNumRs && commentNumRs['type'] === 'error') {
        elogger.error('Island Error 1003-2 ' + commentNumRs['sql']);
        return {
          backSuccess: false,
          msg: 'Island Error 1003-2',
        };
      }
      const pinglunCount = await mysqlInstance.getPinglunCount(rs[i].id);
      if (pinglunCount && pinglunCount['type'] === 'error') {
        elogger.error('Island Error 1003-3 ' + pinglunCount['sql']);
        return {
          backSuccess: false,
          msg: 'Island Error 1003-3',
        };
      }
      const commentCount = pinglunCount[0].count || 0;
      const data = {
        discussId: `${rs[i].id}`,
        title: rs[i].title,
        content: rs[i].content,
        anthorId: `${rs[i].userid}`,
        create_time: rs[i].create_time,
        island_type: `${rs[i].island_type}`,
        voice_type: `${rs[i].voice_type}`,
        status: `${rs[i].status}`,
        heat_value: `${rs[i].heat_value}`,
        commentNum: `${commentCount}`,
      };
      arr.push(data);
    }
    const resData = {
      page: `${args['page']}`,
      size: `${args['size']}`,
      count: `${count}`,
      data: arr,
    };
    return {
      backSuccess: true,
      data: resData,
    };
  }

  async getDiscussByWaterNew(args) {
    const rs = await mysqlInstance.getDiscussByWaterNew(args);
    if (rs && rs['type'] === 'error') {
      elogger.error('Island Error 1003 ' + rs['sql']);
      return {
        backSuccess: false,
        msg: 'Island Error 1003',
      };
    }
    const discussIds = rs.map((item) => item.id);

    const commentCountRs = await mysqlInstance.getDiscussCommentNumV2(discussIds);
    if (commentCountRs && commentCountRs['type'] === 'error') {
      elogger.error('Island Error 1003-2 ' + commentCountRs['sql']);
      return {
        backSuccess: false,
        msg: 'Island Error 1003-2',
      };
    }

    const commentCountMap = {};
    commentCountRs.forEach((item) => {
      commentCountMap[item.discussId] = item.count;
    });

    const countRs = await mysqlInstance.getDiscussByWaterCount(args);
    const count = countRs[0].count;
    const arr = rs.map((item) => {
      // 从映射中获取评论数，如果没有则为0
      const commentCount = commentCountMap[item.id] || 0;

      return {
        discussId: `${item.id}`,
        title: item.title,
        content: item.content,
        anthorId: `${item.userid}`,
        create_time: item.create_time,
        island_type: `${item.island_type}`,
        voice_type: `${item.voice_type}`,
        status: `${item.status}`,
        heat_value: `${item.heat_value}`,
        commentNum: `${commentCount}`,
      };
    });
    const resData = {
      page: `${args['page']}`,
      size: `${args['size']}`,
      count: `${count}`,
      data: arr,
    };
    return {
      backSuccess: true,
      data: resData,
    };
  }

  async getDiscussInfo(discussId) {
    const self = this;
    const rs = await mysqlInstance.getDiscussInfo(discussId);
    if (rs && rs['type'] === 'error') {
      elogger.error('Island Error 1004-1 ' + rs['sql']);
      return {
        backSuccess: false,
        msg: 'Island Error 1004-1',
      };
    }
    if (!rs.length) {
      return {
        backSuccess: false,
        msg: '该条声波不存在',
      };
    }

    const data = {
      discussId: `${rs[0].id}`,
      content: rs[0].content,
      title: rs[0].title,
      create_time: `${rs[0].create_time}`,
      status: `${rs[0].status}`,
      island_type: `${rs[0].island_type}`,
      voice_type: `${rs[0].voice_type}`,
      userid: `${rs[0].userid}`,
      nickname: rs[0].nickname,
      avatar: AVATARWAILIAN + rs[0].avatar,
      gender: `${rs[0].gender}`,
      commentNum: rs[0].commentNum,
    };
    const colorFont = await self.getColorFont(rs[0].userid);
    data['colorFont'] = colorFont;
    return {
      backSuccess: true,
      data: [data],
    };
  }

  putIslandContentComment(args) {
    const self = this;
    return co(function* () {
      try {
        const searchIslandContentRs = yield mysqlInstance.searchIslandContent(args['discussId']);
        if (searchIslandContentRs && searchIslandContentRs['type'] === 'error') {
          elogger.error('Island Error 1005-1 ' + searchIslandContentRs['msg']);
          return {
            backSuccess: false,
            msg: 'Island Error 1005-1',
          };
        }
        if (!searchIslandContentRs.length) {
          return {
            backSuccess: false,
            msg: 'Island Error 1005-2 没有此条声波',
          };
        }
        if (args['comment'].length) {
          args['comment'] = self.toLiteral(args['comment']);
        }

        const rs = yield mysqlInstance.putIslandContentComment(args);
        if (rs && rs['type'] === 'error') {
          elogger.error('Island Error 1006 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'Island Error 1006',
          };
        }

        const commentid = rs;
        const redisRs = yield redisInstance.hashAdd('HASH:ISLAND:COMMENT:' + commentid, [
          'userid',
          args['userid'],
          'authorid',
          args['authorid'],
          'discussId',
          args['discussId'],
          'comment',
          args['comment'],
          'to_userid',
          args['to_userid'],
          'to_commentid',
          args['to_commentid'],
          'belongs',
          args['belongs'],
          'create_time',
          args['time'],
          'type',
          args['type'],
          'images',
          args['images'] || '',
          'voice',
          args['voice'] || '',
          'voice_time',
          args['voice_time'],
        ]);
        if (redisRs && redisRs['type'] === 'error') {
          elogger.error(`HASH:ISLAND:COMMENT:>>${redisRs['msg']},commentid>>${commentid}`);
        }
        const key = 'ZSET:ISLAND:COMMENT:' + args['discussId'];
        const score = yield redisInstance.zsetScoreGet(key);
        const userArrRs = yield redisInstance.zsetAdd(key, score, commentid);
        if (userArrRs && userArrRs['type'] === 'error') {
          elogger.error(`ZSET:ISLAND:COMMENT:>>${userArrRs['msg']},discussId>>${args['discussId']}`);
        }

        yield mysqlInstance.updateIslandContentCommentNum(args['discussId'], 1);
        const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);
        const nickName = userInfo[0]['nickname'];
        const data = {
          userid: args['to_userid'],
          text: `${nickName} 评论了你的声波`,
          from: 2,
          nickname: nickName,
        };

        if (args['belongs'] == 0 && parseInt(args['authorid']) !== parseInt(args['userid'])) {
          imService.sendMsg(data);

          // const key__ = 'HASH:USER:INFO:' + args['to_userid']
          // const redisRS__ = yield redisInstance.hashGet(key__);
          // if (redisRS__['deviceToken']) {
          //     const deviceToken = redisRS__['deviceToken'];

          //     const key2__ = `STR:USER:PUSH:COUNT:${args['to_userid']}`
          //     const redisRS2__ = yield redisInstance.get(key2__);
          //     const pushCount = redisRS2__

          //     const body__ = {
          //         text: `${nickName} 评论了你的声波`,
          //         device_token: deviceToken,
          //         pushCount: pushCount,
          //         to_userid: args['to_userid']
          //     }
          //     youmengService.msgPush(body__)
          // }

          const contentRs = searchIslandContentRs[0]['content'];
          let content = '';
          if (contentRs) {
            content = contentRs.slice(0, 50);
          }
          const images = searchIslandContentRs[0]['images'];
          let image = '';
          if (images) {
            const imagesArr = images.split(',');
            image = imagesArr[0];
          }

          const notificationData = {
            userid: args['to_userid'],
            text: ' 评论了你的声波',
            avatar: userInfo[0]['avatar'],
            nickname: userInfo[0]['nickname'],
            type: 4,
            discussId: args['discussId'],
            content: content,
            images: image,
          };
          notificationData['content'] = self.toLiteral(notificationData['content']);
          yield mysqlInstance.insertIntoNotification(notificationData);
          const redislist = yield redisInstance.lpush(` LIST:USER:ISLAND:CONMENT:${args['userid']}`, args['to_userid']);
          if (parseInt(redislist) == 30) {
            yield redisInstance.rpop(` LIST:USER:ISLAND:CONMENT:${args['userid']}`);
          }
        } else if (args['belongs'] !== 0 && parseInt(args['to_userid']) !== parseInt(args['userid'])) {
          imService.sendMsg(data);

          // const key__ = 'HASH:USER:INFO:' + args['to_userid']
          // const redisRS__ = yield redisInstance.hashGet(key__);
          // if (redisRS__['deviceToken']) {
          //     const deviceToken = redisRS__['deviceToken'];

          //     const key2__ = `STR:USER:PUSH:COUNT:${args['to_userid']}`
          //     const redisRS2__ = yield redisInstance.get(key2__);
          //     const pushCount = redisRS2__

          //     const body__ = {
          //         text: `${nickName} 评论了你的声波`,
          //         device_token: deviceToken,
          //         pushCount: pushCount,
          //         to_userid: args['to_userid']
          //     }
          //     youmengService.msgPush(body__)
          // }

          const contentRs = searchIslandContentRs[0]['content'];
          let content = '';
          if (contentRs) {
            content = contentRs.slice(0, 50);
          }
          const images = searchIslandContentRs[0]['images'];
          let image = '';
          if (images) {
            const imagesArr = images.split(',');
            image = imagesArr[0];
          }

          const notificationData = {
            userid: args['to_userid'],
            text: ' 评论了你的声波',
            avatar: userInfo[0]['avatar'],
            nickname: userInfo[0]['nickname'],
            type: 4,
            discussId: args['discussId'],
            content: content,
            images: image,
          };
          notificationData['content'] = self.toLiteral(notificationData['content']);
          yield mysqlInstance.insertIntoNotification(notificationData);
          const redislist = yield redisInstance.lpush(` LIST:USER:ISLAND:CONMENT:${args['userid']}`, args['to_userid']);
          if (parseInt(redislist) == 30) {
            yield redisInstance.rpop(` LIST:USER:ISLAND:CONMENT:${args['userid']}`);
          }
        }

        const redisContentRs = yield redisInstance.hashGet(`HASH:DISSCUSS:INFO:${args['discussId']}`);
        let commentNum = redisContentRs ? redisContentRs['commentNum'] : 0;
        commentNum = parseInt(commentNum) + 1;
        yield redisInstance.hashAdd(`HASH:DISSCUSS:INFO:${args['discussId']}`, ['commentNum', commentNum]);
        yield mysqlInstance.updateIslandCommentTime(args['discussId']);
        return {
          backSuccess: true,
          data: rs,
        };
      } catch (error) {
        console.log(error);
      }
    });
  }

  delIslandComment(args) {
    const self = this;
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchIslandContent(args['discussId']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 5001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1007-1',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Island Error 1007-1 没有此条声波',
        };
      }
      const searchCommentRs = yield mysqlInstance.searchIslandComment(args['commentid']);
      if (searchCommentRs && searchCommentRs['type'] === 'error') {
        elogger.error('Island Error 1008-1 ' + searchCommentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1008-1 ',
        };
      }
      if (!searchCommentRs.length) {
        return {
          backSuccess: false,
          msg: 'Island Error 1008-2 没有此条评论/回复',
        };
      }
      let delArrSet = new Set();
      delArrSet.add(args['commentid']);

      let total = 1;
      let to_commentidArr = [];
      to_commentidArr.push(args['commentid']);
      do {
        let commentSet = new Set();
        for (let i = 0; i < to_commentidArr.length; i++) {
          const searchToCommentRs = yield mysqlInstance.searchToIslandCommentRs(to_commentidArr[i]);
          if (searchToCommentRs && searchToCommentRs['type'] === 'error') {
            elogger.error('Island Error 1009-1 ' + searchToCommentRs['msg']);
            return {
              backSuccess: false,
              msg: 'Island Error 1009-1',
            };
          }
          if (!searchToCommentRs.length) {
            continue;
          }
          for (let j = 0; j < searchToCommentRs.length; j++) {
            const commentid = searchToCommentRs[j]['id'];
            commentSet.add(commentid);
            delArrSet.add(commentid);
          }
        }
        const arr = Array.from(commentSet);
        if (!arr.length) {
          total = 0;
        }
        to_commentidArr = arr;
      } while (total != 0);
      const delArr = Array.from(delArrSet);
      for (let i = 0; i < delArr.length; i++) {
        const commentid = delArr[i];
        const delRs = yield mysqlInstance.delIslandComment(commentid);
        if (delRs && delRs['type'] === 'error') {
          elogger.error('Island Error 1010-1 ' + delRs['msg']);
          return {
            backSuccess: false,
            msg: 'Island Error 1010-1',
          };
        }
        yield redisInstance.hashDel(`HASH:ISLAND:COMMENT:${commentid}`);
        yield redisInstance.zsetDel(`ZSET:ISLAND:COMMENT:${args['discussId']}`, commentid);
        yield mysqlInstance.updateIslandContentCommentNum(args['discussId'], -1);
      }
      const redisContentRs = yield redisInstance.hashGet(`HASH:DISSCUSS:INFO:${args['discussId']}`);
      let commentNum = redisContentRs['commentNum'] || 0;
      commentNum = parseInt(commentNum) + 1;
      yield redisInstance.hashAdd(`HASH:DISSCUSS:INFO:${args['discussId']}`, ['commentNum', commentNum]);
      return {
        backSuccess: true,
        data: '删除成功',
      };
    });
  }

  getIslandContentComment(args) {
    const self = this;
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchIslandContent(args['discussId']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Island Error 1011-1 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1011-1',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Island Error 1012-1 没有此条声波',
        };
      }

      const mainComments = yield mysqlInstance.getIslandMainComments(
        args['discussId'],
        args['authorid'],
        args['size'],
        args['page'],
      );
      if (mainComments && mainComments['type'] === 'error') {
        elogger.error('Island Error 1013-1 ' + mainComments['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1013-1',
        };
      }

      const total = yield mysqlInstance.getIslandContentCommentTotal(args['discussId'], args['authorid']);

      const mainCommentsTotal = yield mysqlInstance.getIslandMainCommentsTotal(args['discussId'], args['authorid']);

      const arr = [];
      if (mainComments.length) {
        const commentIds = mainComments.map((comment) => comment.id);

        const replies = yield mysqlInstance.getIslandCommentReplies(args['discussId'], commentIds);

        const repliesByCommentId = {};
        if (replies && !replies['type']) {
          replies.forEach((reply) => {
            if (!repliesByCommentId[reply.belongs]) {
              repliesByCommentId[reply.belongs] = [];
            }
            repliesByCommentId[reply.belongs].push(reply);
          });
        }

        for (let i = 0; i < mainComments.length; i++) {
          const comment = mainComments[i];

          const likeStatusRs = yield mysqlInstance.getIslandContentLike(args['uid'], comment.id);
          let like_status = false;
          if (likeStatusRs.length && likeStatusRs[0].status === 0) {
            like_status = true;
          }

          const commentData = {
            belongs: 0,
            commentid: comment.id,
            nickName: comment.nickname,
            userid: comment.userid,
            to_userid: comment.to_userid,
            avatar: AVATARWAILIAN + comment.avatar,
            comment: comment.comment,
            create_time: `${comment.create_time}`,
            type: `${comment.type}`,
            images: comment.images && comment.images.length !== 0 ? IMAGESWAILIAN + comment.images : '',
            voice: comment.voice && comment.voice.length !== 0 ? VOICEWAILIAN + comment.voice : '',
            voice_time: `${comment.voice_time || 0}`,
            likeNum: `${comment.likeNum || 0}`,
            like_status: like_status,
            data: [],
          };

          const colorFont = yield self.getColorFont(comment.userid);
          commentData.colorFont = colorFont;

          const commentReplies = repliesByCommentId[comment.id] || [];
          for (let j = 0; j < commentReplies.length; j++) {
            const reply = commentReplies[j];
            const replyData = {
              belongs: reply.belongs,
              commentid: reply.id,
              nickName: reply.nickname,
              avatar: AVATARWAILIAN + reply.avatar,
              userid: reply.userid,
              to_userid: reply.to_userid,
              comment: reply.comment,
              create_time: `${reply.create_time}`,
              to_nickName: reply.to_nickname,
              to_avatar: AVATARWAILIAN + reply.to_avatar,
              type: `${reply.type}`,
              images: reply.images && reply.images.length !== 0 ? IMAGESWAILIAN + reply.images : '',
              voice: reply.voice && reply.voice.length !== 0 ? VOICEWAILIAN + reply.voice : '',
              voice_time: `${reply.voice_time || 0}`,
              likeNum: '0',
            };

            const replyColorFont = yield self.getColorFont(reply.userid);
            replyData.colorFont = replyColorFont;

            commentData.data.push(replyData);
          }

          commentData.data = commentData.data.sort((a, b) => b.create_time - a.create_time);
          arr.push(commentData);
        }
      }

      return {
        backSuccess: true,
        data: arr,
        count: mainCommentsTotal[0].count,
        totalCount: total[0].count,
      };
    });
  }

  postIslandLike(args) {
    const self = this;
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchIslandContent(args['discussId']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 1010 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1010-1',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Island Error 1010-1 没有此条声波',
        };
      }
      const searchContentCommentRs = yield mysqlInstance.searchIslandContentComment(args['commentid']);
      if (searchContentCommentRs && searchContentCommentRs['type'] === 'error') {
        elogger.error('Content Error 1010-5 ' + searchContentCommentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1010-5',
        };
      }
      if (!searchContentCommentRs.length) {
        return {
          backSuccess: false,
          msg: 'Island Error 1010-1 没有此条评论',
        };
      }
      const belongs = searchContentCommentRs[0].belongs;
      if (belongs != 0) {
        return {
          backSuccess: false,
          msg: '回复不能进行喜欢操作',
        };
      }
      const searchLikeRs = yield mysqlInstance.searchIslandCommentLike(args['commentid'], args['userid']);
      if (searchLikeRs && searchLikeRs['type'] === 'error') {
        elogger.error('Content Error 1010-2 ' + searchLikeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Island Error 1010-2',
        };
      }
      if (searchLikeRs.length) {
        return {
          backSuccess: false,
          msg: '已经喜欢过该声波',
        };
      } else {
        const rs = yield mysqlInstance.insertIslandCommentLike(args['discussId'], args['userid'], args['commentid']);
        if (rs && rs['type'] === 'error') {
          elogger.error('Content Error 1010-3 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'Island Error 1010-3',
          };
        }
        const rs_ = yield mysqlInstance.addIslandCommentLikeNum(args['commentid']);
        if (rs_ && rs_['type'] === 'error') {
          elogger.error('Content Error 1010-4 ' + rs_['msg']);
          return {
            backSuccess: false,
            msg: 'Island Error 1010-4',
          };
        }
        const rs__ = yield mysqlInstance.addIslandHeat(args['discussId']);
        if (rs_ && rs_['type'] === 'error') {
          elogger.error('Content Error 1010-5 ' + rs_['msg']);
          return {
            backSuccess: false,
            msg: 'Island Error 1010-5',
          };
        }
      }
      // const contentRs = searchContentRs[0]['content'];
      // let content = '';
      // if (contentRs) {
      //     content = contentRs.slice(0, 50);
      // }
      // const images = searchContentRs[0]['images'];
      // let image = '';
      // if (images) {
      //     const imagesArr = images.split(',');
      //     image = imagesArr[0]
      // }
      // const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid'])
      // const notificationData = {
      //     userid: searchContentCommentRs[0].userid,
      //     text: ' 喜欢了你的声波回复',
      //     avatar: userInfo[0]['avatar'],
      //     nickname: userInfo[0]['nickname'],
      //     type: 4,
      //     discussId: args['discussId'],
      //     content: content,
      //     images: image
      // }
      // notificationData['content'] = self.toLiteral(notificationData['content']);
      // yield mysqlInstance.insertIntoNotification(notificationData)
      return {
        backSuccess: true,
        anthorId: searchContentCommentRs[0].userid,
        data: [],
      };
    });
  }
}

module.exports.islandInstance = new IslandController();
