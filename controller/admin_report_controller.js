'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const { DefaultConfig } = require('../config/default');
const AVATAR_WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;

class AdminReportController extends BaseController {
  constructor(props) {
    super(props);
  }

  getReportList(from, size, status) {
    return co(function* () {
      const reportRs = yield mysqlInstance.getReportList(from, size, status);
      console.log(reportRs);
      const arr = [];
      for (let i = 0; i < reportRs.length; i++) {
        const report = reportRs[i];
        const user = {
          userid: report.userid,
          nickname: report.nickname,
          avatar: `${AVATAR_WAILIAN}${report.avatar}`,
        };

        const ruser = {
          userid: report.reported_userid,
          nickname: report.rnickname,
          avatar: `${AVATAR_WAILIAN}${report.ravatar}`,
        };

        const detail = {
          id: report.id,
          created: report.create_time,
          type: report.type,
          report_type: report.report_type,
          rid: report.rid,
          status: report.status,
          detail: report.detail,
        };
        const body = {
          user,
          ruser,
          detail,
        };
        arr.push(body);
      }
      return arr;
    });
  }
}

module.exports.reportInstance = new AdminReportController();
