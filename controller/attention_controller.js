'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const imService = require('../services/IM_service').IMInstance;
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const youmengService = require('../services/youmeng').YMInstance;

class Attention extends BaseController {
  constructor(props) {
    super(props);
  }
  //添加关注
  addAttention(args) {
    return co(function* () {
      //查询对方黑名单有没有自己
      const blackRs = yield mysqlInstance.getUserBlack(args['userid2'], args['userid1']);
      if (blackRs.length && blackRs[0].status == 0) {
        return {
          backSuccess: false,
          msg: '当前用户不支持关注',
        };
      }

      //查询自己是否已关注对方
      let searchAttentionByUserid1Result = yield mysqlInstance.searchAttentionByUserid1(args);
      if (searchAttentionByUserid1Result && searchAttentionByUserid1Result['type'] === 'error') {
        elogger.error('Attention Error 1001 ' + searchAttentionByUserid1Result['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 1001 ',
        };
      }
      //已关注对方
      if (searchAttentionByUserid1Result.length) {
        return {
          backSuccess: false,
          msg: '已关注对方',
        };
      }
      //查询对方是否关注自己
      let searchAttentionByUserid2Result = yield mysqlInstance.searchAttentionByUserid2(args);
      if (searchAttentionByUserid2Result && searchAttentionByUserid2Result['type'] === 'error') {
        elogger.error('Attention Error 1002 ' + searchAttentionByUserid2Result['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 1002 ',
        };
      }
      //对方未关注自己
      if (!searchAttentionByUserid2Result.length) {
        let addAttentionResult = yield mysqlInstance.addAttentionFun1(args);
        if (addAttentionResult && addAttentionResult['type'] === 'error') {
          elogger.error('Attention Error 1003 ' + addAttentionResult['msg']);
          return {
            backSuccess: false,
            msg: 'Attention Error 1003 ',
          };
        }
      } else {
        //对方关注自己
        let addAttentionResult_ = yield mysqlInstance.addAttentionFun2(args);
        if (addAttentionResult_ && addAttentionResult_['type'] === 'error') {
          elogger.error('Attention Error 1004 ' + addAttentionResult_['msg']);
          return {
            backSuccess: false,
            msg: 'Attention Error 1004 ',
          };
        }

        let addAttentionResult__ = yield mysqlInstance.addAttentionFun2_(args);
        if (addAttentionResult__ && addAttentionResult__['type'] === 'error') {
          elogger.error('Attention Error 1005 ' + addAttentionResult__['msg']);
          return {
            backSuccess: false,
            msg: 'Attention Error 1005 ',
          };
        }
      }

      // const attentionKey = 'ZSET:USER:ATTENTION:' + args['userid1'];
      // const fansKey = 'ZSET:USER:FANS:' + args['userid2'];
      // const redisAttentionRs = yield redisInstance.setAdd(attentionKey, args['userid2'])
      // if (redisAttentionRs && redisAttentionRs['type'] === 'error') {
      //     elogger.error(`ZSET:USER:ATTENTION:>>${redisAttentionRs['msg']},userid>>${args['userid1']}`)
      // }

      // const redisFansRs = yield redisInstance.setAdd(fansKey, args['userid1'])
      // if (redisFansRs && redisFansRs['type'] === 'error') {
      //     elogger.error(`ZSET:USER:FANS:>>${redisFansRs['msg']},userid>>${args['userid2']}`)
      // }

      const attentionKey = 'ZSET:USER:ATTENTION:' + args['userid1'];
      const fansKey = 'ZSET:USER:FANS:' + args['userid2'];
      const attentionCount = yield redisInstance.zsetScoreGet(attentionKey);
      const fansCount = yield redisInstance.zsetScoreGet(fansKey);
      const redisAttentionRs = yield redisInstance.zsetAdd(attentionKey, attentionCount + 1, args['userid2']);
      if (redisAttentionRs && redisAttentionRs['type'] === 'error') {
        elogger.error(`ZSET:USER:ATTENTION:>>${redisAttentionRs['msg']},userid>>${args['userid1']}`);
      }

      const redisFansRs = yield redisInstance.zsetAdd(fansKey, fansCount + 1, args['userid1']);
      if (redisFansRs && redisFansRs['type'] === 'error') {
        elogger.error(`ZSET:USER:FANS:>>${redisFansRs['msg']},userid>>${args['userid2']}`);
      }

      const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid1']);
      const nickName = userInfo[0]['nickname'];
      const data = {
        userid: args['userid2'],
        text: `${nickName} 关注了你`,
        from: 1,
        nickname: nickName,
      };
      if (args['userid1'] !== 7) {
        imService.sendMsg(data);
      }

      // const key__ = 'HASH:USER:INFO:' + args['userid2']
      // const redisRS__ = yield redisInstance.hashGet(key__);

      // const key2__ = `STR:USER:PUSH:COUNT:${args['userid2']}`
      // const redisRS2__ = yield redisInstance.get(key2__);
      // const pushCount = redisRS2__

      // const deviceToken = redisRS__['deviceToken'];
      // const body__ = {
      //     text: `${nickName} 关注了你`,
      //     device_token: deviceToken,
      //     pushCount: pushCount,
      //     to_userid: args['userid2']
      // }
      // if (args['userid1'] !== 7) {
      //     youmengService.msgPush(body__)
      // }

      const notificationData = {
        userid: args['userid2'],
        text: ' 关注了你',
        avatar: userInfo[0]['avatar'],
        nickname: userInfo[0]['nickname'],
        type: 3,
        attention_userid: args['userid1'],
      };
      yield mysqlInstance.insertIntoNotification(notificationData);
      const attentionUserKey = 'SET:ATTENTION:USER:' + args['userid1'];
      yield redisInstance.setAdd(attentionUserKey, args['userid2']);
      return {
        backSuccess: true,
        data: '关注成功',
      };
    });
  }

  releaseAttention(args) {
    return co(function* () {
      let searchAttentionByUserid1Result = yield mysqlInstance.searchAttentionByUserid1(args);
      if (searchAttentionByUserid1Result && searchAttentionByUserid1Result['type'] === 'error') {
        elogger.error('Attention Error 2001 ' + searchAttentionByUserid1Result['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 2001 ',
        };
      }
      if (!searchAttentionByUserid1Result.length) {
        return {
          backSuccess: false,
          msg: '未关注对方，无法取消关注',
        };
      }
      let searchAttentionByUserid2Result = yield mysqlInstance.searchAttentionByUserid2(args);
      if (searchAttentionByUserid2Result && searchAttentionByUserid2Result['type'] === 'error') {
        elogger.error('Attention Error 2002 ' + searchAttentionByUserid2Result['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 2002 ',
        };
      }
      if (!searchAttentionByUserid2Result.length) {
        let releaseAttentionResult = yield mysqlInstance.releaseAttentionFun1(args);
        if (releaseAttentionResult && releaseAttentionResult['type'] === 'error') {
          elogger.error('Attention Error 2003 ' + releaseAttentionResult['msg']);
          return {
            backSuccess: false,
            msg: 'Attention Error 2003',
          };
        }
      } else {
        let releaseAttentionResult_ = yield mysqlInstance.releaseAttentionFun1(args);
        if (releaseAttentionResult_ && releaseAttentionResult_['type'] === 'error') {
          elogger.error('Attention Error 2004 ' + releaseAttentionResult_['msg']);
          return {
            backSuccess: false,
            msg: 'Attention Error 2004',
          };
        }
        let releaseAttentionResult__ = yield mysqlInstance.releaseAttentionFun2(args);
        if (releaseAttentionResult__ && releaseAttentionResult__['type'] === 'error') {
          elogger.error('Attention Error 2005 ' + releaseAttentionResult__['msg']);
          return {
            backSuccess: false,
            msg: 'Attention Error 2005',
          };
        }
      }

      const attentionKey = 'ZSET:USER:ATTENTION:' + args['userid1'];
      const fansKey = 'ZSET:USER:FANS:' + args['userid2'];
      const redisAttentionRs = yield redisInstance.zsetDel(attentionKey, args['userid2']);
      if (redisAttentionRs && redisAttentionRs['type'] === 'error') {
        elogger.error(`ZSET:USER:ATTENTION:>>${redisAttentionRs['msg']},userid>>${args['userid1']}`);
      }

      const redisFansRs = yield redisInstance.zsetDel(fansKey, args['userid1']);
      if (redisFansRs && redisFansRs['type'] === 'error') {
        elogger.error(`ZSET:USER:FANS:>>${redisFansRs['msg']},userid>>${args['userid2']}`);
      }
      return {
        backSuccess: true,
        data: '取关成功',
      };
    });
  }

  getFans(userid, lastId) {
    const self = this;
    return co(function* () {
      const arr = [];
      // const fansKey = 'ZSET:USER:FANS:' + userid;
      // const fansArr = yield redisInstance.zsetGet(fansKey);
      // if (fansArr && fansArr['type'] === 'error') {
      //     elogger.error(`ZSET:USER:FANS:>>${fansArr['msg']},userid>>${userid}`)
      // }
      // if (fansArr.length) {
      //     const attentionKey = 'ZSET:USER:ATTENTION:' + userid
      //     const attentionArr = yield redisInstance.zsetGet(attentionKey);
      //     if (attentionArr && attentionArr['type'] === 'error') {
      //         elogger.error(`ZSET:USER:ATTENTION:>>${attentionArr['msg']},userid>>${userid}`)
      //     }
      //     for (let i = 0; i < fansArr.length; i++) {
      //         const key = 'HASH:USER:INFO:' + fansArr[i];
      //         const redisRS = yield redisInstance.hashGet(key);
      //         const data = {
      //             userid: redisRS['userid'].toString(),
      //             nickname: redisRS['nickname'],
      //             avatar: WAILIAN + redisRS['avatar'],
      //             signature: redisRS['signature'],
      //             type: "0"
      //         }
      //         const colorFont = yield self.getColorFont(redisRS['userid'])
      //         data['colorFont'] = colorFont
      //         if (attentionArr.indexOf(fansArr[i]) > -1) {
      //             data['type'] = "2";
      //         }
      //         arr.push(data);
      //     }

      //     return {
      //         backSuccess: true,
      //         data: arr
      //     }
      // }
      let getFansResult = yield mysqlInstance.getFansByUserid(userid, lastId);
      if (getFansResult && getFansResult['type'] === 'error') {
        elogger.error('Attention Error 3001 ' + getFansResult['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 3001 ',
        };
      }
      for (let i = 0; i < getFansResult.length; i++) {
        const data = {
          nickname: getFansResult[i]['nickname'],
          avatar: WAILIAN + getFansResult[i]['avatar'],
          signature: getFansResult[i]['signature'] === '' ? '连接那些遥远的相似性。' : getFansResult[i]['signature'],
          type: getFansResult[i]['type'].toString(),
          userid: getFansResult[i]['id'].toString(),
          create_time: getFansResult[i]['update_time']
            ? `${getFansResult[i]['update_time']}`
            : `${getFansResult[i]['create_time']}`,
        };
        const colorFont = yield self.getColorFont(getFansResult[i]['id']);
        data['colorFont'] = colorFont;
        arr.push(data);
      }
      const fanscount = yield mysqlInstance.getFansCount(userid);
      return {
        backSuccess: true,
        data: arr,
        count: fanscount[0].count.toString(),
        lastId: getFansResult.length === 20 ? getFansResult[getFansResult.length - 1]['aid'] : 0,
      };
    });
  }

  //获取关注
  getAttention(userid, lastId) {
    const self = this;
    return co(function* () {
      const arr = [];
      // const attentionKey = 'ZSET:USER:ATTENTION:' + userid
      // const attentionArr = yield redisInstance.zsetGet(attentionKey);
      // if (attentionArr && attentionArr['type'] === 'error') {
      //     elogger.error(`ZSET:USER:ATTENTION:>>${attentionArr['msg']},userid>>${userid}`)
      // }
      // if (attentionArr.length) {
      //     const fansKey = 'ZSET:USER:FANS:' + userid;
      //     const fansArr = yield redisInstance.zsetGet(fansKey);
      //     if (fansArr && fansArr['type'] === 'error') {
      //         elogger.error(`ZSET:USER:FANS:>>${fansArr['msg']},userid>>${userid}`)
      //     }
      //     for (let i = 0; i < attentionArr.length; i++) {
      //         const key = 'HASH:USER:INFO:' + attentionArr[i];
      //         const redisRS = yield redisInstance.hashGet(key);
      //         const data = {
      //             userid: redisRS['userid'].toString(),
      //             nickname: redisRS['nickname'],
      //             avatar: WAILIAN + redisRS['avatar'],
      //             signature: redisRS['signature'],
      //             type: "1"
      //         }
      //         const colorFont = yield self.getColorFont(redisRS['userid'])
      //         data['colorFont'] = colorFont
      //         if (fansArr.indexOf(attentionArr[i]) > -1) {
      //             data['type'] = "2";
      //         }
      //         arr.push(data);
      //     }

      //     return {
      //         backSuccess: true,
      //         data: arr
      //     }
      // }
      let getAttentionResult = yield mysqlInstance.getAttentionByUserid(userid, lastId);
      if (getAttentionResult && getAttentionResult['type'] === 'error') {
        elogger.error('Attention Error 4001 ' + getAttentionResult['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 4001',
        };
      }
      for (let i = 0; i < getAttentionResult.length; i++) {
        const data = {
          nickname: getAttentionResult[i]['nickname'],
          avatar: WAILIAN + getAttentionResult[i]['avatar'],
          signature:
            getAttentionResult[i]['signature'] === '' ? '连接那些遥远的相似性。' : getAttentionResult[i]['signature'],
          type: getAttentionResult[i]['type'].toString(),
          userid: getAttentionResult[i]['id'].toString(),
          create_time: getAttentionResult[i]['update_time']
            ? `${getAttentionResult[i]['update_time']}`
            : `${getAttentionResult[i]['create_time']}`,
        };
        const colorFont = yield self.getColorFont(getAttentionResult[i]['id']);
        data['colorFont'] = colorFont;
        arr.push(data);
      }
      const attentioncount = yield mysqlInstance.getAttentionCount(userid);
      return {
        backSuccess: true,
        data: arr,
        count: attentioncount[0].count.toString(),
        lastId: getAttentionResult.length === 20 ? getAttentionResult[getAttentionResult.length - 1]['aid'] : 0,
      };
    });
  }

  async batchGetColorFont(userIds) {
    if (!userIds || userIds.length === 0) {
      return {};
    }

    // 假设现有的 getColorFont 是单用户查询
    // 这里实现一个批量版本
    const colorFontMap = {};
    const promises = userIds.map(async (userId) => {
      const colorFont = await this.getColorFont(userId);
      colorFontMap[userId] = colorFont;
    });

    await Promise.all(promises);
    return colorFontMap;
  }

  getFriends(userid, page, keyword) {
    const self = this;
    return co(function* () {
      const arr = [];
      const getAttentionResult = yield mysqlInstance.getFriends(userid, page, keyword);
      if (getAttentionResult && getAttentionResult['type'] === 'error') {
        elogger.error('Attention Error 4001 ' + getAttentionResult['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 4001',
        };
      }
      for (let i = 0; i < getAttentionResult.length; i++) {
        const data = {
          nickname: getAttentionResult[i]['nickname'],
          avatar: WAILIAN + getAttentionResult[i]['avatar'],
          signature:
            getAttentionResult[i]['signature'] === '' ? '连接那些遥远的相似性。' : getAttentionResult[i]['signature'],
          type: getAttentionResult[i]['type'].toString(),
          userid: getAttentionResult[i]['id'].toString(),
          create_time: getAttentionResult[i]['update_time']
            ? `${getAttentionResult[i]['update_time']}`
            : `${getAttentionResult[i]['create_time']}`,
        };
        const colorFont = yield self.getColorFont(getAttentionResult[i]['id']);
        data['colorFont'] = colorFont;
        arr.push(data);
      }
      const attentioncount = yield mysqlInstance.getFriendsCount(userid);
      return {
        backSuccess: true,
        data: arr,
        count: attentioncount[0].count.toString(),
      };
    });
  }

  newGetFans(args) {
    const self = this;
    return co(function* () {
      const arr = [];
      const userid = args['userid'];
      const page = args['page'];
      const size = args['size'];
      const fansKey = 'ZSET:USER:FANS:' + userid;
      const fansArr = yield redisInstance.zsetGet(fansKey);
      if (fansArr && fansArr['type'] === 'error') {
        elogger.error(`ZSET:USER:FANS:>>${fansArr['msg']},userid>>${userid}`);
      }
      if (fansArr.length) {
        const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
        const attentionArr = yield redisInstance.zsetGet(attentionKey);
        if (attentionArr && attentionArr['type'] === 'error') {
          elogger.error(`ZSET:USER:ATTENTION:>>${attentionArr['msg']},userid>>${userid}`);
        }
        for (let i = 0; i < fansArr.length; i++) {
          const key = 'HASH:USER:INFO:' + fansArr[i];
          const redisRS = yield redisInstance.hashGet(key);
          const data = {
            userid: redisRS['userid'].toString(),
            nickname: redisRS['nickname'],
            avatar: WAILIAN + redisRS['avatar'],
            signature: redisRS['signature'],
            type: '0',
          };
          const colorFont = yield self.getColorFont(redisRS['userid']);
          data['colorFont'] = colorFont;
          if (attentionArr.indexOf(fansArr[i]) > -1) {
            data['type'] = '2';
          }
          arr.push(data);
        }
        const start = (page - 1) * size;
        const end = page * size;
        const fansArr_ = arr.slice(start, end);
        return {
          backSuccess: true,
          data: fansArr_,
          count: fansArr.length,
        };
      }
      let getFansResult = yield mysqlInstance.newGetFansByUserid(userid, page, size);
      if (getFansResult && getFansResult['type'] === 'error') {
        elogger.error('Attention Error 5001 ' + getFansResult['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 5001',
        };
      }
      for (let i = 0; i < getFansResult.length; i++) {
        const data = {
          nickname: getFansResult[i]['nickname'],
          avatar: WAILIAN + getFansResult[i]['avatar'],
          signature: getFansResult[i]['signature'],
          type: getFansResult[i]['type'].toString(),
          userid: getFansResult[i]['id'].toString(),
        };
        const colorFont = yield self.getColorFont(getFansResult[i]['id']);
        data['colorFont'] = colorFont;
        arr.push(data);
      }
      const count = yield mysqlInstance.getFansCount(userid);
      if (count && count['type'] === 'error') {
        elogger.error('Attention Error 5002 ' + count['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 5002',
        };
      }
      return {
        backSuccess: true,
        data: arr,
        count: count[0].count,
      };
    });
  }

  newGetAttention(args) {
    const self = this;
    return co(function* () {
      const arr = [];
      const userid = args['userid'];
      const page = args['page'];
      const size = args['size'];
      const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
      const attentionArr = yield redisInstance.zsetGet(attentionKey);
      if (attentionArr && attentionArr['type'] === 'error') {
        elogger.error(`ZSET:USER:ATTENTION:>>${attentionArr['msg']},userid>>${userid}`);
      }
      if (attentionArr.length) {
        const fansKey = 'ZSET:USER:FANS:' + userid;
        const fansArr = yield redisInstance.zsetGet(fansKey);
        if (fansArr && fansArr['type'] === 'error') {
          elogger.error(`ZSET:USER:FANS:>>${fansArr['msg']},userid>>${userid}`);
        }
        for (let i = 0; i < attentionArr.length; i++) {
          const key = 'HASH:USER:INFO:' + attentionArr[i];
          const redisRS = yield redisInstance.hashGet(key);
          const data = {
            userid: redisRS['userid'].toString(),
            nickname: redisRS['nickname'],
            avatar: WAILIAN + redisRS['avatar'],
            signature: redisRS['signature'],
            type: '1',
          };
          const colorFont = yield self.getColorFont(redisRS['userid']);
          data['colorFont'] = colorFont;
          if (fansArr.indexOf(attentionArr[i]) > -1) {
            data['type'] = '2';
          }
          arr.push(data);
        }
        const start = (page - 1) * size;
        const end = page * size;
        const attentionArr_ = arr.slice(start, end);
        return {
          backSuccess: true,
          data: attentionArr_,
          count: attentionArr.length,
        };
      }
      let getAttentionResult = yield mysqlInstance.newGetAttentionByUserid(userid, page, size);
      if (getAttentionResult && getAttentionResult['type'] === 'error') {
        elogger.error('Attention Error 6001 ' + getAttentionResult['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 6001',
        };
      }
      for (let i = 0; i < getAttentionResult.length; i++) {
        const data = {
          nickname: getAttentionResult[i]['nickname'],
          avatar: WAILIAN + getAttentionResult[i]['avatar'],
          signature: getAttentionResult[i]['signature'],
          type: getAttentionResult[i]['type'].toString(),
          userid: getAttentionResult[i]['id'].toString(),
        };
        const colorFont = yield self.getColorFont(getAttentionResult[i]['id']);
        data['colorFont'] = colorFont;
        arr.push(data);
      }
      const count = yield mysqlInstance.getAttentionCount(userid);
      if (count && count['type'] === 'error') {
        elogger.error('Attention Error 6002 ' + count['msg']);
        return {
          backSuccess: false,
          msg: 'Attention Error 6002',
        };
      }
      return {
        backSuccess: true,
        data: arr,
        count: count[0].count,
      };
    });
  }

  addTagAttention(userid, tagid) {
    return co(function* () {
      const attentionRs = yield mysqlInstance.getTagAttention(userid, tagid);
      if (attentionRs && attentionRs['type'] === 'error') {
        elogger.error(`addTagAttention:>>${attentionRs['msg']},userid>>${userid}`);
        return {
          backSuccess: false,
          msg: attentionRs['msg'],
        };
      }
      if (!attentionRs.length) {
        const addTagAttentionRs = yield mysqlInstance.addTagAttentionRs(userid, tagid);
        if (addTagAttentionRs && addTagAttentionRs['type'] === 'error') {
          elogger.error(`addTagAttentionRs:>>${addTagAttentionRs['msg']},userid>>${userid}`);
          return {
            backSuccess: false,
            msg: addTagAttentionRs['msg'],
          };
        }
        yield redisInstance.setAdd(`SET:USER:TAG:${userid}`, tagid);
      } else if (attentionRs.length && attentionRs[0]['status'] == 1) {
        return {
          backSuccess: false,
          msg: '已关注该主题',
        };
      } else if (attentionRs.length && attentionRs[0]['status'] == 0) {
        const updateTagAttentionRs = yield mysqlInstance.updateTagAttentionFun(userid, tagid, 1);
        if (updateTagAttentionRs && updateTagAttentionRs['type'] === 'error') {
          elogger.error(`updateTagAttentionRs:>>${updateTagAttentionRs['msg']},userid>>${userid}`);
          return {
            backSuccess: false,
            msg: updateTagAttentionRs['msg'],
          };
        }
        yield redisInstance.setAdd(`SET:USER:TAG:${userid}`, tagid);
      }
      return {
        backSuccess: true,
        data: 'success',
      };
    });
  }

  releaseTagAttention(userid, tagid) {
    return co(function* () {
      const attentionRs = yield mysqlInstance.getTagAttention(userid, tagid);
      if (attentionRs && attentionRs['type'] === 'error') {
        elogger.error(`addTagAttention:>>${attentionRs['msg']},userid>>${userid}`);
        return {
          backSuccess: false,
          msg: attentionRs['msg'],
        };
      }
      if (!attentionRs.length || (attentionRs.length && attentionRs[0]['status'] == 0)) {
        return {
          backSuccess: false,
          msg: '未关注该主题',
        };
      } else if (attentionRs.length && attentionRs[0]['status'] == 1) {
        const updateTagAttentionRs = yield mysqlInstance.updateTagAttentionFun(userid, tagid, 0);
        if (updateTagAttentionRs && updateTagAttentionRs['type'] === 'error') {
          elogger.error(`updateTagAttentionRs:>>${updateTagAttentionRs['msg']},userid>>${userid}`);
          return {
            backSuccess: false,
            msg: updateTagAttentionRs['msg'],
          };
        }
        yield redisInstance.setDel(`SET:USER:TAG:${userid}`, tagid);
      }
      return {
        backSuccess: true,
        data: 'success',
      };
    });
  }

  getTagAttention(userid) {
    return co(function* () {
      const attentionRs = yield mysqlInstance.getAllTagAttention(userid);
      if (attentionRs && attentionRs['type'] === 'error') {
        elogger.error(`getTagAttention:>>${attentionRs['msg']},userid>>${userid}`);
        return {
          backSuccess: false,
          msg: attentionRs['msg'],
        };
      }
      const arr = [];
      for (let i = 0; i < attentionRs.length; i++) {
        const data = {
          tagId: attentionRs[i].tagid,
          tagName: attentionRs[i].name,
          solgan: attentionRs[i].slogan,
        };
        arr.push(data);
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  getAttentionUserContent(args) {
    return co(function* () {});
  }
}

module.exports.attentionInstance = new Attention();
