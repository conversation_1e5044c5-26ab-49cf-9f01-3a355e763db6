'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;
const imService = require('../services/IM_service').IMInstance;
const youmengService = require('../services/youmeng').YMInstance;
const { DefaultConfig } = require('../config/default');
class FeedController extends BaseController {
  constructor(props) {
    super(props);
  }

  getAllFood(type) {
    return co(function* () {
      const rs = yield mysqlInstance.getAllFood(type);
      if (rs && rs['type'] === 'error') {
        elogger.error('Feed Error 1001 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'Feed Error 1001-1',
        };
      }
      const arr = [];
      for (let i = 0; i < rs.length; i++) {
        const data = {
          food_id: rs[i].id,
          weight: rs[i].weight,
        };
        if (type === 2) {
          data['iap_id'] = rs[i].iap_id;
          data['price'] = rs[i].price;
        }
        arr.push(data);
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  feed(userid, toUserid, food_id) {
    return co(function* () {
      try {
        let userInfo = yield redisInstance.hmget('SOCKET:USER:NAMESPACE:ALL', `${userid}`);
        if (userInfo && userInfo[0]) {
          userInfo = JSON.parse(userInfo[0]);
        }
        const userWeight_ = userInfo['weight'];
        const foodRs = yield mysqlInstance.getFoodById(food_id);
        if (foodRs && foodRs['type'] === 'error') {
          elogger.error('Feed Error 2001 ' + foodRs['msg']);
          return {
            backSuccess: false,
            msg: 'Feed Error 2001-1',
          };
        }
        if (parseFloat(userWeight_) < parseFloat(foodRs[0].weight)) {
          return {
            backSuccess: false,
            msg: '你的体重不够投喂',
          };
        }

        let statisticsWeight = userWeight_;
        const userWeight = parseInt(parseFloat(userInfo.weight) * 100);
        userInfo.weight = ((userWeight - parseFloat(foodRs[0].weight) * 100) / 100).toFixed(2);

        yield redisInstance.hashAdd('SOCKET:USER:NAMESPACE:ALL', [`${userid}`, JSON.stringify(userInfo)]);

        const redisRS = yield redisInstance.hashGet('HASH:SOCKET:FOOD:STATISTICS');
        for (const key in redisRS) {
          if (parseInt(key) === parseInt(userid)) {
            statisticsWeight =
              parseInt(parseFloat(JSON.parse(redisRS[key]).weight) * 100) - parseFloat(foodRs[0].weight) * 100;
            break;
          }
        }

        userInfo.weight = (statisticsWeight / 100).toFixed(2);
        for (const key in userInfo) {
          if (typeof userInfo[key] === 'number') {
            userInfo[key] = `${userInfo[key]}`;
          }
        }
        yield redisInstance.hashAdd('HASH:SOCKET:FOOD:STATISTICS', [`${userid}`, JSON.stringify(userInfo)]);

        let toUserInfo = yield redisInstance.hmget('SOCKET:USER:NAMESPACE:ALL', `${toUserid}`);
        if (toUserInfo && toUserInfo[0]) {
          toUserInfo = JSON.parse(toUserInfo[0]);
        }
        const toUserWeight_ = toUserInfo['weight'];

        let toStatisticsWeight = toUserWeight_;
        const toUserWeight = parseInt(parseFloat(toUserInfo.weight) * 100);
        toUserInfo.weight = ((toUserWeight + parseFloat(foodRs[0].weight) * 100) / 100).toFixed(2);

        yield redisInstance.hashAdd('SOCKET:USER:NAMESPACE:ALL', [`${toUserid}`, JSON.stringify(toUserInfo)]);

        const toRedisRS = yield redisInstance.hashGet('HASH:SOCKET:FOOD:STATISTICS');
        for (const key in toRedisRS) {
          if (parseInt(key) === parseInt(toUserid)) {
            toStatisticsWeight =
              parseInt(parseFloat(JSON.parse(toRedisRS[key]).weight) * 100) + parseFloat(foodRs[0].weight) * 100;
            break;
          }
        }

        toUserInfo.weight = (toStatisticsWeight / 100).toFixed(2);
        for (const key in toUserInfo) {
          if (typeof toUserInfo[key] === 'number') {
            toUserInfo[key] = `${toUserInfo[key]}`;
          }
        }
        yield redisInstance.hashAdd('HASH:SOCKET:FOOD:STATISTICS', [`${toUserid}`, JSON.stringify(toUserInfo)]);

        const nickName = userInfo['nickname'];
        const data = {
          userid: toUserid,
          text: `${nickName} 投喂了你`,
          from: 4,
          nickname: nickName,
        };
        imService.sendMsg(data);
        const key__ = 'HASH:USER:INFO:' + toUserid;
        const redisRS__ = yield redisInstance.hashGet(key__);
        const deviceToken = redisRS__['deviceToken'];

        const key2__ = `STR:USER:PUSH:COUNT:${toUserid}`;
        const redisRS2__ = yield redisInstance.get(key2__);
        const pushCount = redisRS2__;

        const body__ = {
          text: `${nickName} 投喂了你`,
          device_token: deviceToken,
          pushCount: pushCount,
          to_userid: toUserid,
        };
        youmengService.msgPush(body__);

        const notificationData = {
          userid: toUserid,
          text: ' 投喂了你',
          avatar: userInfo['avatar'].split(DefaultConfig.wailian.AVATAR_DOMAIN)[1],
          nickname: userInfo['nickname'],
          type: 5,
          weight: parseFloat(foodRs[0].weight),
          to_user_id: userid,
        };
        yield mysqlInstance.insertIntoNotification(notificationData);
        const redislist = yield redisInstance.lpush(`LIST:USER:CONMENT:${userid}`, toUserid);
        if (parseInt(redislist) == 30) {
          yield redisInstance.rpop(`LIST:USER:CONMENT:${toUserid}`);
        }

        return {
          backSuccess: true,
        };
      } catch (err) {
        console.log(`<><><><><><><><>${JSON.stringify(err)}`);
      }
    });
  }

  getUserWeight(userid) {
    return co(function* () {
      let userInfo = yield redisInstance.hmget('SOCKET:USER:NAMESPACE:ALL', `${userid}`);
      if (userInfo && userInfo[0]) {
        userInfo = JSON.parse(userInfo[0]);
      }
      const userWeight = userInfo['weight'];
      return {
        backSuccess: true,
        userWeight: userWeight,
      };
    });
  }

  toUserWeight() {
    return co(function* () {
      const userInfo = yield redisInstance.hashGet('SOCKET:USER:NAMESPACE:ALL');
      for (const key in userInfo) {
        if (userInfo[key]) {
          const userWeight = JSON.parse(userInfo[key])['weight'];
          if (userWeight) {
            yield mysqlInstance.updateUserWeight(key, userWeight);
          }
        }
      }
      return;
    });
  }
}

module.exports.feedInstance = new FeedController();
