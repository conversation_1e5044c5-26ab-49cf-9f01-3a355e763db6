'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
class MapController extends BaseController {
  constructor(props) {
    super(props);
  }

  getFoodStatistics(userid) {
    const self = this;
    return co(function* () {
      const redisRS = yield redisInstance.hashGet('HASH:SOCKET:FOOD:STATISTICS');
      let allArr = [];
      for (const key in redisRS) {
        const rs = JSON.parse(redisRS[key]);
        if (rs['nickname']) {
          delete rs['coordinates'];
          allArr.push(rs);
        }
      }
      allArr.sort(compare('weight'));
      let num = 0;
      let myWeight = 0;
      // let allWeight = 0

      for (let i = 0; i < allArr.length; i++) {
        const userid_ = parseInt(allArr[i].userid);
        if (userid === userid_) {
          num = i + 1;
          myWeight = parseFloat(allArr[i].weight).toFixed(2);
          break;
        }
        // allWeight += parseFloat(allArr[i].weight) * 100
      }
      const allArr_ = allArr.splice(0, 100);
      const todayArr = [];
      const allDayArr = [];
      for (let i = 0; i < allArr_.length; i++) {
        if (!allArr_[i].userid) {
          continue;
        }
        const userRs = yield mysqlInstance.getUserInfoByUserid(parseInt(allArr_[i].userid));
        const data = {
          nickname: userRs[0].nickname,
          avatar: `${WAILIAN}${userRs[0].avatar}`,
          gender: typeof allArr_[i].gender === 'string' ? allArr_[i].gender : `${allArr_[i].gender}`,
          userid: allArr_[i].userid,
          whale: allArr_[i].whale,
          weight: parseFloat(allArr_[i].weight).toFixed(2),
        };

        const colorFont = yield self.getColorFont(allArr_[i].userid);
        data['colorFont'] = colorFont;
        if (data.weight > 0) {
          todayArr.push(data);
        }
      }

      const allRedisRs = yield redisInstance.hashGet('SOCKET:USER:NAMESPACE:ALL');
      const allArr___ = [];
      const userArr = [];
      for (const key in allRedisRs) {
        allRedisRs[key] = JSON.parse(allRedisRs[key]);
        allRedisRs[key].weight = parseFloat(allRedisRs[key].weight);
        if (allRedisRs[key].userid) {
          allArr___.push(allRedisRs[key]);
          userArr.push(parseInt(allRedisRs[key].userid));
        }
      }
      const allArr____ = allArr___.sort(compare('weight'));
      let num_ = 0;
      let allMyWeight = 0;
      for (let i = 0; i < allArr____.length; i++) {
        const userid_ = parseInt(allArr____[i].userid);
        if (userid === userid_) {
          num_ = i + 1;
          allMyWeight = parseFloat(allArr____[i].weight).toFixed(2);
        }
      }
      const allArr__ = JSON.stringify(allArr___.sort(compare('weight')).splice(0, 100));
      yield redisInstance.delKey('SET:ALL:SOCKET:FOOD:STATISTICS');
      yield redisInstance.onlySet('SET:ALL:SOCKET:FOOD:STATISTICS', allArr__);

      let allRs = yield redisInstance.get('SET:ALL:SOCKET:FOOD:STATISTICS');
      allRs = JSON.parse(allRs);
      for (let i = 0; i < allRs.length; i++) {
        if (!allRs[i].userid) {
          continue;
        }
        const userRs = yield mysqlInstance.getUserInfoByUserid(parseInt(allRs[i].userid));
        const data = {
          nickname: userRs[0].nickname,
          avatar: `${WAILIAN}${userRs[0].avatar}`,
          gender: typeof allRs[i].gender === 'string' ? allRs[i].gender : `${allRs[i].gender}`,
          userid: allRs[i].userid,
          whale: allRs[i].whale,
          weight: parseFloat(allRs[i].weight).toFixed(2),
        };
        const colorFont = yield self.getColorFont(allRs[i].userid);
        data['colorFont'] = colorFont;
        allDayArr.push(data);
      }
      const data = {
        // todayAllWeight: (allWeight / 100).toFixed(2),
        todayMyWeight: myWeight,
        todayRank: num,
        allMyWeight: allMyWeight,
        allRank: num_,
        todayStatistics: todayArr,
        allStatistics: allDayArr,
      };
      return data;
    });
  }
}

function compare(property) {
  return function (a, b) {
    var value1 = a[property];
    var value2 = b[property];
    return value2 - value1;
  };
}

module.exports.mapInstance = new MapController();
