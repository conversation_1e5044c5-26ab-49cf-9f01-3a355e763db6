'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const whiteKey = 'SET:WHITE:LIST'
class scriptController extends BaseController {
    constructor(props) {
        super(props);
    }

    getInfo(args) {
        const self = this;
        return co(function* () {
            const startTime = args['startTime'];
            const endTime = args['endTime']
            // const time = self.getDayTime();
            const resultData = {};
            //总用户数
            const result8 = yield mysqlInstance.scriptFun8(startTime, endTime)
            const allUserNum = result8[0].count
            resultData['allUserNum'] = allUserNum
            //每天新增用户
            const result9 = yield mysqlInstance.scriptFun9(startTime, endTime)
            const newUserCount = result9[0].count;
            resultData['newUserCount'] = newUserCount
            //每天多少用户发布内容
            const result1 = yield mysqlInstance.scriptFun1(startTime, endTime)
            resultData['putContentUserCount'] = result1[0].count;
            //每天多少内容
            const result2 = yield mysqlInstance.scriptFun2(startTime, endTime)
            resultData['putContentCount'] = result2[0].count;
            //发动态用户占比
            resultData['putContentProportion'] = result1[0].count / allUserNum
            //总动态数
            const result3 = yield mysqlInstance.scriptFun3(startTime, endTime)
            resultData['allContentCount'] = result3[0].count;
            //每天点赞人数
            const result4 = yield mysqlInstance.scriptFun4(startTime, endTime)
            resultData['likeUserCount'] = result4[0].count;
            //每天评论人数
            const result5 = yield mysqlInstance.scriptFun5(startTime, endTime)
            resultData['commentCount'] = result5[0].count;
            //邀请码统计
            const result6 = yield mysqlInstance.scriptFun6(startTime, endTime)
            resultData['invitation'] = []
            for (let i = 0; i < result6.length; i++) {
                const data = {
                    invitation: result6[i].invitation,
                    num: result6[i].num
                }
                resultData['invitation'].push(data)
            }
            //用户性别、年龄、城市、频率分布
            // const result7 = yield mysqlInstance.scriptFun7(startTime, endTime)

            return {
                backSuccess: true,
                data: resultData
            }
        })
    }

    getDayTime() {
        let date = new Date()
        let times = date.getTime()
        let hour = date.getHours()
        let minute = date.getMinutes()
        let second = date.getSeconds()
        let dayTime = Math.round((times - hour * 3600 * 1000 - minute * 60 * 1000 - second * 1000) / 1000)
        let dayTimeStr = `${dayTime}`
        dayTimeStr = dayTimeStr.substring(0, 9) + '0'
        dayTime = parseInt(dayTimeStr)
        return dayTime
    }

    getWhiteList() {
        return co(function* () {
            const whiteRs = yield redisInstance.setGet(whiteKey)
            if (whiteRs && whiteRs['type'] === 'error') {
                elogger.error(`SET:WHITE:LIST:GET>>${whiteRs}}`)
                return {
                    backSuccess: false,
                    msg: whiteRs
                }
            }
            return {
                backSuccess: true,
                data: whiteRs
            }
        })
    }

    addWhiteList(whiteUserid) {
        return co(function* () {
            const getWhiteRs = yield redisInstance.setGet(whiteKey)
            if (getWhiteRs && getWhiteRs['type'] === 'error') {
                elogger.error(`SET:WHITE:LIST:GET>>${getWhiteRs}}`)
                return {
                    backSuccess: false,
                    msg: getWhiteRs
                }
            }
            if (getWhiteRs.indexOf(`${whiteUserid}`) > -1) {
                return {
                    backSuccess: true,
                    data: []
                }
            }
            const whiteRs = yield redisInstance.setAdd(whiteKey, whiteUserid)
            if (whiteRs && whiteRs['type'] === 'error') {
                elogger.error(`SET:WHITE:LIST:ADD>>${whiteRs},whiteUserid>>${whiteUserid}`)
                return {
                    backSuccess: false,
                    msg: whiteRs
                }
            }
            return {
                backSuccess: true,
                data: []
            }
        })
    }
    delWhiteList(whiteUserid) {
        return co(function* () {
            const getWhiteRs = yield redisInstance.setGet(whiteKey)
            if (getWhiteRs && getWhiteRs['type'] === 'error') {
                elogger.error(`SET:WHITE:LIST:GET>>${getWhiteRs}}`)
                return {
                    backSuccess: false,
                    msg: getWhiteRs
                }
            }
            if (getWhiteRs.indexOf(`${whiteUserid}`) < 0) {
                return {
                    backSuccess: false,
                    msg: '白名单不存在该用户'
                }
            }
            const whiteRs = yield redisInstance.setDel(whiteKey, whiteUserid)
            if (whiteRs && whiteRs['type'] === 'error') {
                elogger.error(`SET:WHITE:LIST:DEL>>${whiteRs},whiteUserid>>${whiteUserid}`)
                return {
                    backSuccess: false,
                    msg: whiteRs
                }
            }
            return {
                backSuccess: true,
                data: []
            }
        })
    }

    userActive() {
        return co(function* () {
            const invitationRs = yield mysqlInstance.getinvitationRs();
            const data = {}
            for (let i = 0; i < invitationRs.length; i++) {
                let one = 0;
                let seven = 0;
                let thirty = 0;
                let ninety = 0;

                let oneDayUser = 0;
                let sevenDayUser = 0;
                let thirtyDayUser = 0;
                let ninetyDayUser = 0;

                const code = invitationRs[i]['invitationCode'];
                const userByCode = yield mysqlInstance.getUserByInvitationCode(code);
                for (let j = 0; j < userByCode.length; j++) {
                    const userid = userByCode[j].id;
                    const create_time = userByCode[j].create_time;
                    const now = parseInt(Math.round(new Date().getTime() / 1000));

                    if (now - create_time > 60 * 60 * 24) {
                        oneDayUser++;
                    }
                    if (now - create_time > 60 * 60 * 24 * 7) {
                        sevenDayUser++;
                    }
                    if (now - create_time > 60 * 60 * 24 * 30) {
                        thirtyDayUser++;
                    }
                    if (now - create_time > 60 * 60 * 24 * 90) {
                        ninetyDayUser++;
                    }

                    const oneDayTime = create_time + (60 * 60 * 24);
                    const sevenDayTime = create_time + (60 * 60 * 24 * 7);
                    const thirtyDayTime = create_time + (60 * 60 * 24 * 30);
                    const ninetyDayTime = create_time + (60 * 60 * 24 * 90);

                    const userActiveRs = yield mysqlInstance.getUserActiveV2(userid);
                    for (let z = 0; z < userActiveRs.length; z++) {
                        const active_time = userActiveRs[z].create_time;
                        if (active_time >= oneDayTime) {
                            one++;
                            break;
                        }
                    }
                    for (let z = 0; z < userActiveRs.length; z++) {
                        const active_time = userActiveRs[z].create_time;
                        if (active_time >= sevenDayTime) {
                            seven++;
                            break;
                        }
                    }
                    for (let z = 0; z < userActiveRs.length; z++) {
                        const active_time = userActiveRs[z].create_time;
                        if (active_time >= thirtyDayTime) {
                            thirty++;
                            break;
                        }
                    }
                    for (let z = 0; z < userActiveRs.length; z++) {
                        const active_time = userActiveRs[z].create_time;
                        if (active_time >= ninetyDayTime) {
                            ninety++;
                            break;
                        }
                    }
                }
                const data2 = {
                    one: toPercent(one / oneDayUser),
                    seven: toPercent(seven / sevenDayUser),
                    thirty: toPercent(thirty / thirtyDayUser),
                    ninety: toPercent(ninety / ninetyDayUser)
                }
                data[code] = data2;
            }
            return {
                backSuccess: true,
                data: data
            }
        })
    }
}

function toPercent(point){
    if (!point) {
        return null;
    }
    var str=Number(point*100).toFixed(3);
    str+="%";
    return str;
}



module.exports.scriptInstance = new scriptController();