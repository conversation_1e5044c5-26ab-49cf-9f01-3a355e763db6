'use strict';
const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');
const VIDEOWAILIAN = DefaultConfig.wailian.VIDEO_DOMAIN;
const Attribute = require('../config/tagConfig').attribute;
const WAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const AVATAR_WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;

class PHPController extends BaseController {
  constructor(props) {
    super(props);
  }

  async getShareContent(userid) {
    const rs = await mysqlInstance.searchUserTag(userid);
    if (rs && rs['type'] === 'error') {
      elogger.error('User Error 1001 ' + rs['msg']);
      return {
        backSuccess: false,
        msg: 'User Error 1001',
      };
    }
    if (!rs.length) {
      return {
        backSuccess: false,
        msg: '未完成测算',
      };
    }
    const attribute = rs[0]['attribute'];
    const data = {
      whaleName: null,
      content: null,
    };
    for (const key in Attribute) {
      if (key == attribute) {
        data['whaleName'] = Attribute[key]['whale'];
        data['content'] = Attribute[key]['depict'];
      }
    }
    return {
      backSuccess: true,
      data: data,
    };
  }

  async getAffairDetail(args) {
    const searchContentRs = await mysqlInstance.searchContentInfoById(args['contentid']);
    if (searchContentRs && searchContentRs['type'] === 'error') {
      elogger.error('Content Error 6001 ' + searchContentRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 6001',
      };
    }
    if (!searchContentRs.length) {
      return {
        backSuccess: false,
        msg: '没有此条动态',
      };
    }
    if (searchContentRs[0].type == 1) {
      let imageArrStr = '';
      if (searchContentRs[0].images) {
        const images = searchContentRs[0].images;
        const imagesArr_ = images.split(',');
        for (let i = 0; i < imagesArr_.length; i++) {
          if (i === imagesArr_.length - 1) {
            imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
          } else {
            imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
          }
        }
      }

      const likeRs = await mysqlInstance.searchLikeFun(args['userid'], args['contentid']);
      if (likeRs && likeRs['type'] === 'error') {
        elogger.error('Content Error 6003 ' + likeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 6003',
        };
      }

      const commentRs = await mysqlInstance.searchCommentFun(args['userid'], args['contentid']);
      if (commentRs && commentRs['type'] === 'error') {
        elogger.error('Content Error 60034 ' + commentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 60034',
        };
      }

      const contentInfo = {
        user: {
          uid: `${searchContentRs[0].authorid}`,
          nickname: searchContentRs[0].nickname,
          gender: `${searchContentRs[0].gender}`,
          avatar: AVATAR_WAILIAN + searchContentRs[0].avatar,
          city: searchContentRs[0].location == 'null' ? '北冰洋' : searchContentRs[0].location,
          hertz: `${searchContentRs[0].hertz}`,
        },
        affair: {
          aid: `${args['contentid']}`,
          content: searchContentRs[0].content || '',
          images: imageArrStr,
          video: '',
          imageType: searchContentRs[0].imageType,
          type: '1',
          musicContent: [],
          likeNum: `${searchContentRs[0].likeNum}`,
          commentNum: `${searchContentRs[0].commentNum}`,
          likeRs: likeRs.length ? '1' : '0',
          commentRs: commentRs.length ? '1' : '0',
          dateline: `${new Date(parseInt(searchContentRs[0].create_time) * 1000).toLocaleString()}` || '0',
          linkShare: [],
        },
      };

      return {
        backSuccess: true,
        data: contentInfo,
      };
    } else if (searchContentRs[0].type == 2) {
      const musicContent = searchContentRs[0].musicContent;
      const musicContentData = JSON.parse(musicContent);
      // const shareUrl = musicContentData['shareUrl']
      // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
      // if (musicInfo && musicInfo['type']) {
      //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      // }
      const likeRs = await mysqlInstance.searchLikeFun(args['userid'], args['contentid']);
      if (likeRs && likeRs['type'] === 'error') {
        elogger.error('Content Error 6003 ' + likeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 6003',
        };
      }

      const commentRs = await mysqlInstance.searchCommentFun(args['userid'], args['contentid']);
      if (commentRs && commentRs['type'] === 'error') {
        elogger.error('Content Error 60034 ' + commentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 60034',
        };
      }

      let imageArrStr = '';
      if (searchContentRs[0].images) {
        const images = searchContentRs[0].images;
        const imagesArr_ = images.split(',');
        for (let i = 0; i < imagesArr_.length; i++) {
          if (i === imagesArr_.length - 1) {
            imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
          } else {
            imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
          }
        }
      }

      const contentInfo = {
        user: {
          uid: `${searchContentRs[0].authorid}`,
          nickname: searchContentRs[0].nickname,
          gender: `${searchContentRs[0].gender}`,
          avatar: AVATAR_WAILIAN + searchContentRs[0].avatar,
          city: searchContentRs[0].location == 'null' ? '北冰洋' : searchContentRs[0].location,
          hertz: `${searchContentRs[0].hertz}`,
        },
        affair: {
          aid: `${args['contentid']}`,
          content: searchContentRs[0].content || '',
          images: imageArrStr,
          video: '',
          imageType: searchContentRs[0].imageType,
          type: '2',
          musicContent: [musicContentData],
          likeNum: `${searchContentRs[0].likeNum}`,
          commentNum: `${searchContentRs[0].commentNum}`,
          likeRs: likeRs.length ? '1' : '0',
          commentRs: commentRs.length ? '1' : '0',
          dateline: `${new Date(parseInt(searchContentRs[0].create_time) * 1000).toLocaleString()}` || '0',
          linkShare: [],
        },
      };

      return {
        backSuccess: true,
        data: contentInfo,
      };
    } else if (searchContentRs[0].type == 3) {
      const linkShareContent = searchContentRs[0].linkshare;
      const linkShareContentData = JSON.parse(linkShareContent);
      const likeRs = await mysqlInstance.searchLikeFun(args['userid'], args['contentid']);
      if (likeRs && likeRs['type'] === 'error') {
        elogger.error('Content Error 6003 ' + likeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 6003',
        };
      }

      const commentRs = await mysqlInstance.searchCommentFun(args['userid'], args['contentid']);
      if (commentRs && commentRs['type'] === 'error') {
        elogger.error('Content Error 60034 ' + commentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 60034',
        };
      }

      let imageArrStr = '';
      if (searchContentRs[0].images) {
        const images = searchContentRs[0].images;
        const imagesArr_ = images.split(',');
        for (let i = 0; i < imagesArr_.length; i++) {
          if (i === imagesArr_.length - 1) {
            imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
          } else {
            imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
          }
        }
      }

      const contentInfo = {
        user: {
          uid: `${searchContentRs[0].authorid}`,
          nickname: searchContentRs[0].nickname,
          gender: `${searchContentRs[0].gender}`,
          avatar: AVATAR_WAILIAN + searchContentRs[0].avatar,
          city: searchContentRs[0].location == 'null' ? '北冰洋' : searchContentRs[0].location,
          hertz: `${searchContentRs[0].hertz}`,
        },
        affair: {
          aid: `${args['contentid']}`,
          content: searchContentRs[0].content || '',
          images: imageArrStr,
          video: '',
          imageType: searchContentRs[0].imageType,
          type: '3',
          musicContent: [],
          likeNum: `${searchContentRs[0].likeNum}`,
          commentNum: `${searchContentRs[0].commentNum}`,
          likeRs: likeRs.length ? '1' : '0',
          commentRs: commentRs.length ? '1' : '0',
          dateline: `${new Date(parseInt(searchContentRs[0].create_time) * 1000).toLocaleString()}` || '0',
          linkShare: linkShareContentData,
        },
      };

      return {
        backSuccess: true,
        data: contentInfo,
      };
    } else if (searchContentRs[0].type == 4) {
      let imageArrStr = '';
      if (searchContentRs[0].images) {
        const images = searchContentRs[0].images;
        const imagesArr_ = images.split(',');
        for (let i = 0; i < imagesArr_.length; i++) {
          if (i === imagesArr_.length - 1) {
            imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
          } else {
            imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
          }
        }
      }

      let videoArrStr = '';
      if (searchContentRs[0].video) {
        const videos = searchContentRs[0].video;
        const videosArr_ = videos.split(',');
        for (let j = 0; j < videosArr_.length; j++) {
          if (j === videosArr_.length - 1) {
            videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
          } else {
            videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
          }
        }
      }

      const likeRs = await mysqlInstance.searchLikeFun(args['userid'], args['contentid']);
      if (likeRs && likeRs['type'] === 'error') {
        elogger.error('Content Error 6003 ' + likeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 6003',
        };
      }

      const commentRs = await mysqlInstance.searchCommentFun(args['userid'], args['contentid']);
      if (commentRs && commentRs['type'] === 'error') {
        elogger.error('Content Error 60034 ' + commentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 60034',
        };
      }

      const contentInfo = {
        user: {
          uid: `${searchContentRs[0].authorid}`,
          nickname: searchContentRs[0].nickname,
          gender: `${searchContentRs[0].gender}`,
          avatar: AVATAR_WAILIAN + searchContentRs[0].avatar,
          city: searchContentRs[0].location == 'null' ? '北冰洋' : searchContentRs[0].location,
          hertz: `${searchContentRs[0].hertz}`,
        },
        affair: {
          aid: `${args['contentid']}`,
          content: searchContentRs[0].content || '',
          images: imageArrStr,
          video: videoArrStr,
          imageType: searchContentRs[0].imageType,
          type: '4',
          musicContent: [],
          likeNum: `${searchContentRs[0].likeNum}`,
          commentNum: `${searchContentRs[0].commentNum}`,
          likeRs: likeRs.length ? '1' : '0',
          commentRs: commentRs.length ? '1' : '0',
          dateline: `${new Date(parseInt(searchContentRs[0].create_time) * 1000).toLocaleString()}` || '0',
          linkShare: [],
        },
      };

      return {
        backSuccess: true,
        data: contentInfo,
      };
    }
  }

  async getAffairLikes(args) {
    const self = this;
    const aid = args['aid'],
      page = args['page'],
      size = args['size'];
    const rs = await mysqlInstance.getContentLikeV2(aid, page, size);
    const arr = [];
    for (let i = 0; i < rs.length; i++) {
      const data = {
        uid: `${rs[i].id}`,
        nickname: rs[i].nickname,
        signature: rs[i].signature ? rs[i].signature : '连接那些遥远的相似性。',
        gender: `${rs[i].gender}`,
        avatar: AVATAR_WAILIAN + rs[i].avatar,
        city: rs[i].city,
        hertz: `${rs[i].hertz}`,
        create_time: rs[i].create_time,
      };
      const colorFont = await self.getColorFont(rs[i].id);
      data['colorFont'] = colorFont;
      arr.push(data);
    }
    const countRs = await mysqlInstance.searchContentLikeCountV2(aid);
    return {
      backSuccess: true,
      data: {
        page: `${page}`,
        size: `${size}`,
        count: `${countRs[0].count}`,
        data: arr,
      },
    };
  }

  async getAffairComments(args) {
    const aid = args['aid'],
      page = args['page'],
      size = args['size'];
    const rs = await mysqlInstance.getContentCommentV2(aid, page, size);
    const arr = [];
    for (let i = 0; i < rs.length; i++) {
      const data = {
        user: {
          uid: `${rs[i].userid}`,
          nickname: rs[i].nickname,
          gender: `${rs[i].gender}`,
          avatar: AVATAR_WAILIAN + rs[i].avatar,
          city: rs[i].city,
          hertz: `${rs[i].hertz}`,
        },
        toReply: {
          uid: `${rs[i].to_userid}`,
          nickname: rs[i].to_nickname,
          gender: `${rs[i].to_gender}`,
          avatar: AVATAR_WAILIAN + rs[i].to_avatar,
          city: rs[i].to_city,
          hertz: `${rs[i].to_hertz}`,
        },
        comment: rs[i].comment,
        dateline: `${new Date(parseInt(rs[i].create_time) * 1000).toLocaleString()}` || '0',
      };

      arr.push(data);
    }
    const countRs = await mysqlInstance.searchContentCommentCountV2(aid);
    return {
      backSuccess: true,
      data: {
        page: `${page}`,
        size: `${size}`,
        count: `${countRs[0].count}`,
        data: arr,
      },
    };
  }

  async getListByUseridNew(args) {
    const self = this;
    const uid = args['userid'],
      page = args['page'],
      size = args['size'],
      userid_ = args['userid_'],
      agent = args['agent'];
    const searchContentRs = await mysqlInstance.searchUserAllContentNew(uid, page, size, userid_, agent);
    if (searchContentRs && searchContentRs['type'] === 'error') {
      elogger.error('Content Error 6001 ' + searchContentRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 6001',
      };
    }
    const arr = [];
    for (let i = 0; i < searchContentRs.length; i++) {
      if (searchContentRs[i].type == 1) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }
        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            status: `${searchContentRs[i].status}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '1',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
            timing: `${searchContentRs[i].timing}`,
            sponsor: `${searchContentRs[i].sponsor}`,
            top: `${searchContentRs[i].top}`,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 2) {
        const musicContent = searchContentRs[i].musicContent;
        const musicContentData = JSON.parse(musicContent);
        // const shareUrl = musicContentData['shareUrl']
        // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
        // if (musicInfo && musicInfo['type']) {
        //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
        // }
        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            status: `${searchContentRs[i].status}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '2',
            musicContent: musicContentData,
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
            timing: `${searchContentRs[i].timing}`,
            sponsor: `${searchContentRs[i].sponsor}`,
            top: `${searchContentRs[i].top}`,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 3) {
        const linkShareContent = searchContentRs[i].linkshare;
        const linkShareContentData = JSON.parse(linkShareContent);

        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            status: `${searchContentRs[i].status}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '3',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: linkShareContentData,
            timing: `${searchContentRs[i].timing}`,
            sponsor: `${searchContentRs[i].sponsor}`,
            top: `${searchContentRs[i].top}`,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 4) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }

        let videoArrStr = '';
        if (searchContentRs[i].video) {
          const videos = searchContentRs[i].video;
          const videosArr_ = videos.split(',');
          for (let j = 0; j < videosArr_.length; j++) {
            if (j === videosArr_.length - 1) {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
            } else {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
            }
          }
        }

        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            status: `${searchContentRs[i].status}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: videoArrStr,
            imageType: searchContentRs[i].imageType,
            type: '4',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
            timing: `${searchContentRs[i].timing}`,
            sponsor: `${searchContentRs[i].sponsor}`,
            top: `${searchContentRs[i].top}`,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;
        arr.push(contentInfo);
      }
    }

    const countRs = await mysqlInstance.getUserContentCountNew(uid, userid_);
    const contentIds = [];
    for (let i = 0; i < arr.length; i++) {
      contentIds.push(arr[i].affair.aid);
    }

    const commentRs = await mysqlInstance.getCommentByIds(userid_, contentIds);
    const commentIdArr = Array.from(commentRs).map((item) => item.contentid);
    const commentSet = new Set(commentIdArr);
    for (let i = 0; i < arr.length; i++) {
      const isComment = commentSet.has(parseInt(arr[i].affair.aid)) ? '1' : '0';
      arr[i].affair['commentRs'] = isComment;
    }
    return {
      backSuccess: true,
      data: {
        page: `${page}`,
        size: `${size}`,
        count: `${countRs[0].count}`,
        data: arr,
      },
    };
  }

  async getListByUserid(args) {
    const self = this;
    const uid = args['userid'],
      page = args['page'],
      size = args['size'],
      userid_ = args['userid_'],
      agent = args['agent'];
    const searchContentRs = await mysqlInstance.searchUserAllContent(uid, page, size, userid_, agent);
    if (searchContentRs && searchContentRs['type'] === 'error') {
      elogger.error('Content Error 6001 ' + searchContentRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 6001',
      };
    }
    const arr = [];
    for (let i = 0; i < searchContentRs.length; i++) {
      if (searchContentRs[i].type == 1) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }
        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '1',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 2) {
        const musicContent = searchContentRs[i].musicContent;
        const musicContentData = JSON.parse(musicContent);
        // const shareUrl = musicContentData['shareUrl']
        // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
        // if (musicInfo && musicInfo['type']) {
        //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
        // }
        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '2',
            musicContent: musicContentData,
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 3) {
        const linkShareContent = searchContentRs[i].linkshare;
        const linkShareContentData = JSON.parse(linkShareContent);

        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '3',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: linkShareContentData,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 4) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }

        let videoArrStr = '';
        if (searchContentRs[i].video) {
          const videos = searchContentRs[i].video;
          const videosArr_ = videos.split(',');
          for (let j = 0; j < videosArr_.length; j++) {
            if (j === videosArr_.length - 1) {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
            } else {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
            }
          }
        }

        const likeRs = await mysqlInstance.searchLikeFun(userid_, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].authorid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].location == 'null' ? '北冰洋' : searchContentRs[i].location,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: videoArrStr,
            imageType: searchContentRs[i].imageType,
            type: '4',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].authorid);
        contentInfo['user']['colorFont'] = colorFont;
        arr.push(contentInfo);
      }
    }

    const countRs = await mysqlInstance.getUserContentCount(uid, userid_);
    return {
      backSuccess: true,
      data: {
        page: `${page}`,
        size: `${size}`,
        count: `${countRs[0].count}`,
        data: arr,
      },
    };
  }

  async getFollowedList(args) {
    const self = this;
    const uid = args['userid'],
      page = args['page'],
      size = args['size'];
    const attentionRs = await mysqlInstance.searcUserAttentionUserid(uid);
    const attentionArr = [];
    for (let i = 0; i < attentionRs.length; i++) {
      const userid = attentionRs[i].userid2;
      attentionArr.push(userid);
    }
    attentionArr.push(uid);
    const searchContentRs = await mysqlInstance.searchAttentionUserAllContent(attentionArr, page, size);
    if (searchContentRs && searchContentRs['type'] === 'error') {
      elogger.error('Content Error 16001 ' + searchContentRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 16001',
      };
    }
    const arr = [];
    for (let i = 0; i < searchContentRs.length; i++) {
      if (searchContentRs[i].type == 1) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }

        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 16003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 16003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '1',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 2) {
        const musicContent = searchContentRs[i].musicContent;
        const musicContentData = JSON.parse(musicContent);
        // const shareUrl = musicContentData['shareUrl']
        // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
        // if (musicInfo && musicInfo['type']) {
        //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
        // }
        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '2',
            musicContent: musicContentData,
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 3) {
        const linkShareContent = searchContentRs[i].linkshare;
        const linkShareContentData = JSON.parse(linkShareContent);
        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '3',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: linkShareContentData,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 4) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }

        let videoArrStr = '';
        if (searchContentRs[i].video) {
          const videos = searchContentRs[i].video;
          const videosArr_ = videos.split(',');
          for (let j = 0; j < videosArr_.length; j++) {
            if (j === videosArr_.length - 1) {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
            } else {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
            }
          }
        }

        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 16003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 16003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: videoArrStr,
            imageType: searchContentRs[i].imageType,
            type: '4',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      }
    }

    const countRs = await mysqlInstance.getUserContentCount(attentionArr);
    return {
      backSuccess: true,
      data: {
        page: `${page}`,
        size: `${size}`,
        count: `${countRs[0].count}`,
        data: arr,
      },
    };
  }

  async getFollowedListNew(uid, lastId) {
    const self = this;
    const attentionRs = await mysqlInstance.searcUserAttentionUserid(uid);
    const attentionArr = [];
    for (let i = 0; i < attentionRs.length; i++) {
      const userid = attentionRs[i].userid2;
      attentionArr.push(userid);
    }
    attentionArr.push(uid);
    const searchContentRs = await mysqlInstance.searchAttentionUserAllContentNew(attentionArr, lastId);
    if (searchContentRs && searchContentRs['type'] === 'error') {
      elogger.error('Content Error 16001 ' + searchContentRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 16001',
      };
    }
    const arr = [];
    for (let i = 0; i < searchContentRs.length; i++) {
      if (searchContentRs[i].type == 1) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }

        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 16003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 16003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '1',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 2) {
        const musicContent = searchContentRs[i].musicContent;
        const musicContentData = JSON.parse(musicContent);
        // const shareUrl = musicContentData['shareUrl']
        // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
        // if (musicInfo && musicInfo['type']) {
        //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
        // }
        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '2',
            musicContent: musicContentData,
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 3) {
        const linkShareContent = searchContentRs[i].linkshare;
        const linkShareContentData = JSON.parse(linkShareContent);
        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 6003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 6003',
          };
        }

        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let i = 0; i < imagesArr_.length; i++) {
            if (i === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
            }
          }
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: '',
            imageType: searchContentRs[i].imageType,
            type: '3',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: linkShareContentData,
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      } else if (searchContentRs[i].type == 4) {
        let imageArrStr = '';
        if (searchContentRs[i].images) {
          const images = searchContentRs[i].images;
          const imagesArr_ = images.split(',');
          for (let j = 0; j < imagesArr_.length; j++) {
            if (j === imagesArr_.length - 1) {
              imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
            } else {
              imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
            }
          }
        }

        let videoArrStr = '';
        if (searchContentRs[i].video) {
          const videos = searchContentRs[i].video;
          const videosArr_ = videos.split(',');
          for (let j = 0; j < videosArr_.length; j++) {
            if (j === videosArr_.length - 1) {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
            } else {
              videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
            }
          }
        }

        const likeRs = await mysqlInstance.searchLikeFun(uid, searchContentRs[i].contentid);
        if (likeRs && likeRs['type'] === 'error') {
          elogger.error('Content Error 16003 ' + likeRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 16003',
          };
        }

        const contentInfo = {
          user: {
            uid: `${searchContentRs[i].userid}`,
            nickname: searchContentRs[i].nickname,
            gender: `${searchContentRs[i].gender}`,
            avatar: AVATAR_WAILIAN + searchContentRs[i].avatar,
            city: searchContentRs[i].city == 'null' ? '北冰洋' : searchContentRs[i].city,
            hertz: `${searchContentRs[i].hertz}`,
          },
          affair: {
            aid: `${searchContentRs[i].contentid}`,
            content: searchContentRs[i].content || '',
            images: imageArrStr,
            video: videoArrStr,
            imageType: searchContentRs[i].imageType,
            type: '4',
            musicContent: [],
            likeNum: `${searchContentRs[i].likeNum}`,
            commentNum: `${searchContentRs[i].commentNum}`,
            likeRs: likeRs.length ? '1' : '0',
            dateline: `${searchContentRs[i].create_time}` || '0',
            tagType: `${searchContentRs[i].tagType}`,
            tagName: searchContentRs[i].tagName || '',
            linkShare: [],
          },
        };

        const colorFont = await self.getColorFont(searchContentRs[i].userid);
        contentInfo['user']['colorFont'] = colorFont;

        arr.push(contentInfo);
      }
    }

    const countRs = await mysqlInstance.getUserContentCount(attentionArr);
    return {
      backSuccess: true,
      data: {
        count: `${countRs[0].count}`,
        data: arr,
        lastId: arr.length ? arr[arr.length - 1].affair.aid : null,
      },
    };
  }

  async getFollowedListNew2(uid, lastId) {
    const self = this;
    const attentionRs = await mysqlInstance.searcUserAttentionUserid(uid);
    const attentionArr = [];
    for (let i = 0; i < attentionRs.length; i++) {
      const userid = attentionRs[i].userid2;
      attentionArr.push(userid);
    }
    attentionArr.push(uid);

    // 获取内容列表
    const searchContentRs = await mysqlInstance.searchAttentionUserAllContentNew(attentionArr, lastId);
    if (searchContentRs && searchContentRs['type'] === 'error') {
      elogger.error('Content Error 16001 ' + searchContentRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 16001',
      };
    }

    // 没有内容则直接返回
    if (!searchContentRs.length) {
      const countRs = await mysqlInstance.getUserContentCount(attentionArr);
      return {
        backSuccess: true,
        data: {
          count: `${countRs[0].count}`,
          data: [],
          lastId: null,
        },
      };
    }

    const contentIds = searchContentRs.map((item) => item.contentid);
    const userIds = [...new Set(searchContentRs.map((item) => item.userid))];

    const likeStatusRs = await mysqlInstance.batchSearchLikeFun(uid, contentIds);
    if (likeStatusRs && likeStatusRs['type'] === 'error') {
      elogger.error('Content Error 16003 ' + likeStatusRs['msg']);
      return {
        backSuccess: false,
        msg: 'Content Error 16003',
      };
    }

    const commentRs = await mysqlInstance.getCommentByIds(uid, contentIds);
    const commentIdArr = Array.from(commentRs).map((item) => item.contentid);
    const commentSet = new Set(commentIdArr);

    const likeStatusMap = {};
    likeStatusRs.forEach((like) => {
      likeStatusMap[like.contentid] = true;
    });

    const userColorFontRs = await self.batchGetColorFont(userIds);
    const colorFontMap = {};
    if (userColorFontRs) {
      Object.keys(userColorFontRs).forEach((userId) => {
        colorFontMap[userId] = userColorFontRs[userId];
      });
    }

    const arr = [];

    // 辅助函数：处理图片字符串
    const processImages = (images) => {
      if (!images) return '';

      let imageArrStr = '';
      const imagesArr_ = images.split(',');
      for (let j = 0; j < imagesArr_.length; j++) {
        if (j === imagesArr_.length - 1) {
          imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
        } else {
          imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
        }
      }
      return imageArrStr;
    };

    // 辅助函数：处理视频字符串
    const processVideos = (videos) => {
      if (!videos) return '';

      let videoArrStr = '';
      const videosArr_ = videos.split(',');
      for (let j = 0; j < videosArr_.length; j++) {
        if (j === videosArr_.length - 1) {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
        } else {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
        }
      }
      return videoArrStr;
    };

    for (let i = 0; i < searchContentRs.length; i++) {
      const item = searchContentRs[i];
      const contentid = item.contentid;
      const userid = item.userid;

      // 通用用户数据
      const userData = {
        uid: `${userid}`,
        nickname: item.nickname,
        gender: `${item.gender}`,
        avatar: AVATAR_WAILIAN + item.avatar,
        city: item.city == 'null' ? '北冰洋' : item.city,
        hertz: `${item.hertz}`,
        colorFont: colorFontMap[userid] || {},
      };

      // 通用内容数据
      const baseAffairData = {
        aid: `${contentid}`,
        content: item.content || '',
        images: processImages(item.images),
        video: item.type == 4 ? processVideos(item.video) : '',
        imageType: item.imageType,
        type: `${item.type}`,
        likeNum: `${item.likeNum}`,
        commentNum: `${item.commentNum}`,
        likeRs: likeStatusMap[contentid] ? '1' : '0',
        commentRs: commentSet.has(contentid) ? '1' : '0',
        dateline: `${item.create_time}` || '0',
        tagType: `${item.tagType}`,
        tagName: item.tagName || '',
      };

      // 根据类型添加特定数据
      let affairData = { ...baseAffairData };

      if (item.type == 1 || item.type == 4) {
        // 图文或视频
        affairData.musicContent = [];
        affairData.linkShare = [];
      } else if (item.type == 2) {
        // 音乐
        const musicContent = item.musicContent;
        const musicContentData = JSON.parse(musicContent);
        affairData.musicContent = musicContentData;
        affairData.linkShare = [];
      } else if (item.type == 3) {
        // 链接
        const linkShareContent = item.linkshare;
        const linkShareContentData = JSON.parse(linkShareContent);
        affairData.musicContent = [];
        affairData.linkShare = linkShareContentData;
      }

      const contentInfo = {
        user: userData,
        affair: affairData,
      };

      arr.push(contentInfo);
    }

    const countRs = await mysqlInstance.getUserContentCount(attentionArr);
    return {
      backSuccess: true,
      data: {
        count: `${countRs[0].count}`,
        data: arr,
        lastId: arr.length ? arr[arr.length - 1].affair.aid : null,
      },
    };
  }
}

module.exports.phpInstance = new PHPController();
