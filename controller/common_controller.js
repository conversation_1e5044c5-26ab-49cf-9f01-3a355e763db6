'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');
const request = require('request');
const SMSClient = require('@alicloud/sms-sdk');
const qiniu = require('../tools/qiniu').qiniuInstance;
const wxAppId = 'wx09d09b1ce4d983b0';
const wxAppSecret = '5514e359c53b8bc09688b603ca19636c';
const AK = 'Hw4GOeS3snm6fWkt7q2WcL08o5GYaRz1';
const smsClient = new SMSClient({
  accessKeyId: DefaultConfig.sms.accessKeyId,
  secretAccessKey: DefaultConfig.sms.secretAccessKey,
});
const TAG = require('../config/tagConfig').tag;
const Attribute = require('../config/tagConfig').attribute;
const youmengService = require('../services/youmeng').YMInstance;
const WAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const AVATAR_WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const imService = require('../services/IM_service').IMInstance;
const pwd = require('pwd');
const mobileArr = DefaultConfig.mobileArr;
const SIG = require('tls-sig-api');
const blackUserArr = DefaultConfig.blackUserArr;
const config = DefaultConfig.sig_config;
const myInv = DefaultConfig.myInv;
class CommonController extends BaseController {
  constructor(props) {
    super(props);
  }

  invitationCodeFun(code) {
    return co(function* () {
      if (code == 290669) {
        return {
          backSuccess: false,
          msg: '该邀请码已失效',
        };
      }
      const rs = yield mysqlInstance.getInvitationCodeFun(code);
      if (rs.length) {
        const num = parseInt(rs[0].num);
        if (myInv.indexOf(parseInt(code)) !== -1) {
          yield mysqlInstance.updateInvitationCodeFun(code);
          return {
            backSuccess: true,
            data: '',
          };
        }
        if (num > 4) {
          return {
            backSuccess: false,
            msg: '每个邀请码仅能使用5次',
          };
        }
        yield mysqlInstance.updateInvitationCodeFun(code);
        return {
          backSuccess: true,
          data: '',
        };
      } else {
        yield mysqlInstance.insertInvitationCodeFun(code);
        return {
          backSuccess: true,
          data: '',
        };
      }
    });
  }

  invitationCodeFunNew(code) {
    return co(function* () {
      if (code == '462013') {
        return {
          backSuccess: true,
          msg: [],
        };
      }
      const key = 'SET:ALL:INVIT:CODE';
      const rs = yield redisInstance.setGet(key);
      if (rs.length) {
        if (rs.indexOf(code) > -1) {
          const invitRs = yield mysqlInstance.getInvitationCodeFunV2(code);
          if (!invitRs.length) {
            return {
              backSuccess: false,
              msg: '该邀请码无效c1',
            };
          } else {
            const use_num = invitRs[0].use_num;
            if (use_num >= 3) {
              return {
                backSuccess: false,
                msg: '该邀请码已无使用次数',
              };
            } else {
              // yield mysqlInstance.updateUserInvitCodeUse(code);
              return {
                backSuccess: true,
                data: [],
              };
            }
          }
        } else {
          return {
            backSuccess: false,
            msg: '该邀请码无效c2',
          };
        }
      } else {
        return {
          backSuccess: false,
          msg: '该邀请码无效c3',
        };
      }
    });
  }

  getInvitationCodeFunNew(userid) {
    return co(function* () {
      const result = yield mysqlInstance.getInvitationCodeFunV3(userid);
      if (result.length) {
        return {
          backSuccess: true,
          data: [
            {
              code: result[0].code,
              use_num: result[0].use_num,
            },
          ],
        };
      }
      return {
        backSuccess: true,
        data: [
          {
            code: null,
            use_num: 0,
          },
        ],
      };
    });
  }

  updateUserSig(userid, sig) {
    return co(function* () {
      const redisRs = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['userSig', sig]);
      const mysqlRs = yield mysqlInstance.updateUsersig(userid, sig);
      return {
        backSuccess: true,
        usersig: sig,
      };
    });
  }

  updateSsid(ssid, userid) {
    return co(function* () {
      const sessionResult = yield redisInstance.setSessionToRedis(userid);
      if (sessionResult) {
        yield redisInstance.clearSessionInfo(ssid);
        yield mysqlInstance.updateSSID(sessionResult, userid);
        yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['ssid', sessionResult]);
        return {
          backSuccess: true,
          ssid: sessionResult,
        };
      }
    });
  }

  getGuideMapStatus(userid) {
    return co(function* () {
      const result = yield mysqlInstance.getGuideMapStatus(userid);
      if (result && result['type'] === 'error') {
        elogger.error(result['msg']);
        return {
          backSuccess: false,
          msg: result['msg'],
        };
      }
      let status = '1';

      if (result.length) {
        status = '2';
      }
      return {
        backSuccess: true,
        data: status,
      };
    });
  }

  pushRouter(args) {
    return co(function* () {
      const to_userid = args['to_userid'];
      const key = 'HASH:USER:INFO:' + to_userid;
      const redisRS = yield redisInstance.hashGet(key);
      const deviceToken = redisRS['deviceToken'];

      const key2__ = `STR:USER:PUSH:COUNT:${to_userid}`;
      const redisRS2__ = yield redisInstance.get(key2__);
      const pushCount = redisRS2__;

      const body = {
        text: args['text'],
        device_token: deviceToken,
        nickname: args['nickname'],
        pushCount: pushCount,
        to_userid: to_userid,
      };
      const sendMsgRs = youmengService.msgPush(body);
    });
  }

  updateGuideMapStatus(userid) {
    return co(function* () {
      const result = yield mysqlInstance.updateGuideMapStatus(userid);
      if (result && result['type'] === 'error') {
        elogger.error(result['msg']);
        return {
          backSuccess: false,
          msg: result['msg'],
        };
      }
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  //邀请短信
  invitedMsgCode(phoneNum, userid) {
    const self = this;
    return co(function* () {
      const user = yield mysqlInstance.getUserInfoByUserid(userid);
      const name = user[0].nickname;
      const rs = yield self.invitedMsgCodeV2(phoneNum, name);
      if (rs) {
        return {
          backSuccess: true,
          data: '',
        };
      }
      return {
        backSuccess: false,
        msg: rs['err'],
      };
    });
  }

  invitedMsgCodeV2(phoneNum, name) {
    return new Promise(function (resolve, reject) {
      //发送短信
      smsClient
        .sendSMS({
          PhoneNumbers: phoneNum,
          SignName: '52赫兹',
          TemplateCode: 'SMS_166080190',
          TemplateParam: `{"name":"${name}", "code":"930503"}`,
        })
        .then(
          function (res) {
            const { Code } = res;
            if (Code === 'OK') {
              //处理返回参数
              // elogger.debug(res);
              resolve({
                backSuccess: true,
              });
            }
          },
          function (err) {
            elogger.error('sendCodeBase ERROR : ', err['data']['Message']);
            resolve({
              backSuccess: false,
              err: err,
            });
          },
        );
    });
  }

  //发送验证码
  sendCode(mobile, time) {
    const self = this;
    return co(function* () {
      const code = self.getRandomCode();
      //设置redis验证码 60s有效
      const key = 'HASH:CODE:' + mobile;
      yield redisInstance.delKey(key);
      yield redisInstance.setVerifyCode(mobile, code, time);
      const result = yield self.sendCodeBase(mobile, code);
      if (result) {
        return {
          backSuccess: true,
        };
      }
      return {
        backSuccess: false,
      };
    });
  }

  //发送验证码
  sendCodeNew(mobile, time, userAgent) {
    const self = this;
    return co(function* () {
      userAgent = userAgent.toLowerCase();
      const mobileRs = yield mysqlInstance.getUserByMobile(mobile);
      if (!mobileRs.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }
      const codeResult = yield redisInstance.getVerifyCode(mobile);
      if (codeResult[0]) {
        console.log('CODE:', codeResult[0]);
        const result = yield self.sendCodeBase(mobile, codeResult[0]);
        if (result) {
          return {
            backSuccess: true,
          };
        }
      } else {
        const code = self.getRandomCode();
        const key = 'HASH:CODE:' + mobile;
        yield redisInstance.delKey(key);
        yield redisInstance.setVerifyCode(mobile, code, time);
        console.log('CODE:', code);
        const result = yield self.sendCodeBase(mobile, code);
        if (result) {
          return {
            backSuccess: true,
          };
        }
      }
      return {
        backSuccess: false,
      };
    });
  }
  sendCodeBase(mobile, code) {
    return new Promise(function (resolve, reject) {
      //发送短信
      smsClient
        .sendSMS({
          PhoneNumbers: mobile,
          SignName: '博味',
          TemplateCode: 'SMS_121857568', // 'SMS_149097393', //SMS_166080190
          TemplateParam: '{"code":"' + code + '"}',
        })
        .then(
          function (res) {
            const { Code } = res;
            if (Code === 'OK') {
              //处理返回参数
              // elogger.debug(res);
              resolve(true);
            }
          },
          function (err) {
            elogger.error(`${mobile} sendCodeBase ERROR : ${err}`);
            // resolve(false);
            resolve(true); // TODO 记得改回来
          },
        );
    });
  }

  judgeByMobile(mobile) {
    return co(function* () {
      let data = 0;
      const rs = yield mysqlInstance.judgeByMobile(mobile);
      if (rs.length) {
        data = 1;
      }
      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  validateCode(args) {
    const self = this;
    return co(function* () {
      const code = args['code'];
      const userAgent = args['userAgent'].toLowerCase();

      if (mobileArr.indexOf(parseInt(args['mobile'])) !== -1 && parseInt(code) === 2003) {
        return {
          backSuccess: true,
        };
      }

      const mobileRs = yield mysqlInstance.getUserByMobile(args['mobile']);
      if (!mobileRs.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }

      const codeResult = yield redisInstance.getVerifyCode(args['mobile']);
      if (codeResult[0] && codeResult[0] == code) {
        return {
          backSuccess: true,
          data: codeResult[0],
        };
      } else {
        elogger.error(`验证码出错 : 手机 : ${args['mobile']} : 接收 : ${code} : 本地 : ${codeResult[0]}`);
        return {
          backSuccess: false,
          msg: '验证码出错',
        };
      }
    });
  }

  validateAccount(args) {
    return co(function* () {
      const isMobile = args['isMobile'];
      let accountRs = [];
      if (isMobile) {
        const userRs = yield mysqlInstance.searchByMobile(parseInt(args['account']));
        if (!userRs.length) {
          return {
            backSuccess: false,
            msg: '账号不存在',
          };
        }
        accountRs = yield mysqlInstance.getAccountByUserid(userRs[0].id);
        if (!accountRs.length) {
          return {
            backSuccess: false,
            msg: '账号不存在',
          };
        }
        const { hash } = yield pwd.hash(args['password'], accountRs[0].salt);

        const rs = yield mysqlInstance.getUserByUseridAndPassword(userRs[0].id, hash);
        if (!rs.length) {
          return {
            backSuccess: false,
            msg: '账号密码错误',
          };
        }
        return {
          backSuccess: true,
          data: rs[0].user_id,
        };
      } else {
        accountRs = yield mysqlInstance.getAccountByAccount(args['account']);
        if (!accountRs.length) {
          return {
            backSuccess: false,
            msg: '账号不存在',
          };
        }
        const { hash } = yield pwd.hash(args['password'], accountRs[0].salt);

        const rs = yield mysqlInstance.getUserByAccountAndPassword(args['account'], hash);
        if (!rs.length) {
          return {
            backSuccess: false,
            msg: '账号密码错误',
          };
        }
        return {
          backSuccess: true,
          data: rs[0].user_id,
        };
      }
    });
  }

  //注册
  mobileRegisteredFun(args) {
    const self = this;
    return co(function* () {
      const userAgent = args['userAgent'].toLowerCase();
      const result = yield mysqlInstance.getUserAndAccountExit(args['mobile']);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 1001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 1001',
        };
      }
      if (!result.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }

      if (result.length) {
        if (blackUserArr.indexOf(parseInt(result[0].id)) > -1) {
          return {
            backSuccess: false,
            msg: '帐号封禁 拒绝登录',
          };
        }
        yield redisInstance.clearSessionInfo(result[0]['ssid']);
        const sessionResult = yield redisInstance.setSessionToRedis(result[0]['id']);
        if (sessionResult) {
          yield mysqlInstance.updateSSID(sessionResult, result[0]['id']);
          const userid = result[0].id;
          const sig_ = new SIG.Sig(config);
          const sig = sig_.genSig(`${userid}`);
          const data = {
            account: result[0]['account'],
            nickname: result[0]['nickname'],
            mobile: result[0]['mobile'] ? result[0]['mobile'].toString() : '',
            avatar: result[0]['avatar'],
            signature: result[0]['signature'],
            birth: `${result[0]['birth']}`,
            gender: `${result[0]['gender']}`,
            NOOType: '0', //1:已有账号 0:新用户
            ssid: sessionResult.toString(),
            city: result[0]['city'] || '',
            province: result[0]['province'] || '',
            country: result[0]['country'] || '',
            userid: `${result[0].id}`,
            hertz: `${self.returnFloat(result[0]['hertz'])}`,
            usersig: sig,
            tag: [],
            likeTag: [],
            paoLikeTag: [],
            rank: `${result[0]['rank']}`,
          };

          //获取粉丝数
          const fansKey = 'ZSET:USER:FANS:' + userid;
          const fansNum = yield redisInstance.zsetScoreGet(fansKey);

          //获取关注数
          const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
          const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

          //获取动态数
          const contentKey = 'ZSET:USER:CONTENT:' + userid;
          const contentNum = yield redisInstance.zsetScoreGet(contentKey);

          data['fansNum'] = fansNum.toString();
          data['attentionNum'] = attentionNum.toString();
          data['contentNum'] = contentNum.toString();
          data['avatar'] = AVATAR_WAILIAN + data['avatar'];

          const result_ = yield mysqlInstance.getUserInfoByMobile(args['mobile']);
          if (result_.length) {
            data['NOOType'] = '1';
            const tagArrStr = result_[0]['tagArr'];
            const tagArr = tagArrStr.split(',');
            let tagInfoArr = [];
            let paoTagInfoArr = [];
            for (let i = 0; i < tagArr.length; i++) {
              const num = tagArr[i];
              const tagInfo = TAG[num];
              if (tagInfo['type'] == 0) {
                paoTagInfoArr.push(tagInfo);
              } else {
                tagInfoArr.push(tagInfo);
              }
            }

            const likeTagArrStr = result_[0]['likeTagArr'];
            const likeTagArr = likeTagArrStr.split(',');
            let likeTagInfoArr = [];
            for (let i = 0; i < likeTagArr.length; i++) {
              const num = likeTagArr[i];
              const likeTagInfo = TAG[num];
              likeTagInfoArr.push(likeTagInfo);
            }
            data['paoLikeTag'] = paoTagInfoArr;
            data['tag'] = tagInfoArr;
            data['likeTag'] = likeTagInfoArr;
          }
          yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['ssid', sessionResult, 'userSig', sig]);
          yield mysqlInstance.updateUsersig(userid, sig);
          //1:登陆 2:注销
          yield redisInstance.onlySet(`SET:LOGIN:STATUS:${userid}`, '1');
          yield self.updateUserLocation(result[0]['locationStatus'], result[0]['id'], args['ip']);
          return {
            backSuccess: true,
            data: data,
          };
        } else {
          elogger.error('Common Error 1002 redis设置错误');
          return {
            backSuccess: false,
            msg: 'Common Error 1002',
          };
        }
      }
      // const ip = args['ip'];
      // if (ip) {
      //     const rs = yield self.getIpAddressFun(ip);
      //     const RS = JSON.parse(rs);
      //     const status = RS['status'];
      //     if (status == 0) {
      //         const addres = RS['address'];
      //         const country = addres.split("|")[0];
      //         const province = RS['content']['address_detail']['province'];
      //         const city = RS['content']['address_detail']['city'];
      //         args['province'] = province;
      //         args['city'] = city;
      //         args['country'] = country;
      //         args['ipStatus'] = 1
      //     }
      // }
      const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
      const index = Math.floor(Math.random() * locationArr.length);
      const location = locationArr[index];
      args['province'] = location;
      args['city'] = location;
      args['country'] = location;
      args['ipStatus'] = 1;
      const birth = parseInt(Math.round(new Date().getTime() / 1000));
      args['createtime'] = birth;
      const result_ = yield mysqlInstance.mobileRegisteredFun(args);
      console.log(result_);
      if (result_ && result_['type'] === 'error') {
        elogger.error('Common Error 1003 ' + result_['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 1003',
        };
      }
      const sig_ = new SIG.Sig(config);
      const sig = sig_.genSig(`${result_}`);
      yield mysqlInstance.updateUsersig(result_, sig);
      yield redisInstance.onlySet(`SET:LOGIN:STATUS:${result_}`, '1');
      const rankRs = yield mysqlInstance.getRank(result_);
      const rank = rankRs[0].rank;
      const sessionResult = yield redisInstance.setSessionToRedis(result_);
      if (sessionResult) {
        const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + result_, [
          'mobile',
          args['mobile'],
          'ssid',
          sessionResult,
          'userid',
          result_,
          'nickname',
          'whale',
          'signature',
          '',
          'birth',
          birth,
          'gender',
          1,
          'hertz',
          '52.00',
          'rank',
          rank,
          'type',
          1,
          'city',
          args['city'] || '',
          'province',
          args['province'] || '',
          'country',
          args['country'] || '',
          'rank',
          rank,
          'userSig',
          sig,
        ]);
        if (redisUserInfo && redisUserInfo['type'] === 'error') {
          elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${result_}`);
        }

        const redisUserPushCount = yield redisInstance.onlySet(`STR:USER:PUSH:COUNT:${result_}`, '0');
        yield mysqlInstance.updateUserRank(result_, rank);
        yield mysqlInstance.updateSSID(sessionResult, result_);
        yield mysqlInstance.updateUserSignature(result_, rank);
        const accountImportData = {
          userid: `${result_}`,
          nickName: 'whale',
          avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
        };
        // const accountImportRs = yield imService.accountImport(accountImportData);
        // if (accountImportRs['backSuccess']) {
        //   yield mysqlInstance.updateUserStatus(result_, 0);
        // } else {
        //   yield mysqlInstance.updateUserStatus(result_, 1);
        // }
        yield mysqlInstance.updateUserStatus(result_, 0);
        const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
        yield imService.sendBulk(msg, [`${result_}`]);
        return {
          backSuccess: true,
          data: {
            mobile: args['mobile'].toString(),
            account: null,
            NOOType: '0', //1:已有账号 0:新用户
            ssid: sessionResult.toString(),
            userid: result_.toString(),
            nickname: 'whale',
            avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
            signature: '',
            birth: `${birth}`,
            gender: '1',
            city: args['city'] || '',
            province: args['province'] || '',
            country: args['country'] || '',
            usersig: sig,
            tag: [],
            likeTag: [],
            paoLikeTag: [],
            rank: rank.toString(),
            fansNum: '0',
            attentionNum: '0',
            contentNum: '0',
            hertz: '52.00',
          },
        };
      } else {
        elogger.error('redis设置错误');
        return {
          backSuccess: false,
          msg: 'Common Error 1004',
        };
      }
    });
  }

  mobileRegisteredFunNew(args) {
    const self = this;
    return co(function* () {
      const userAgent = args['userAgent'].toLowerCase();
      const mobileRs = yield mysqlInstance.getUserByMobile(args['mobile']);
      if (!mobileRs.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }
      if (args['type'] === 'old') {
        const result = yield mysqlInstance.getUserAndAccountExit(args['mobile']);
        if (result && result['type'] === 'error') {
          elogger.error('Common Error 1001 ' + result['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 1001',
          };
        }
        if (result.length) {
          if (blackUserArr.indexOf(parseInt(result[0].id)) > -1) {
            return {
              backSuccess: false,
              msg: '帐号封禁 拒绝登录',
            };
          }
          yield redisInstance.clearSessionInfo(result[0]['ssid']);
          const sessionResult = yield redisInstance.setSessionToRedis(result[0]['id']);
          if (sessionResult) {
            yield mysqlInstance.updateSSID(sessionResult, result[0]['id']);
            const userid = result[0].id;
            const sig_ = new SIG.Sig(config);
            const sig = sig_.genSig(`${userid}`);
            const data = {
              nickname: result[0]['nickname'],
              account: result[0]['account'],
              mobile: result[0]['mobile'] ? result[0]['mobile'].toString() : '',
              avatar: result[0]['avatar'],
              signature: result[0]['signature'],
              birth: `${result[0]['birth']}`,
              gender: `${result[0]['gender']}`,
              NOOType: '0', //1:已有账号 0:新用户
              ssid: sessionResult.toString(),
              city: result[0]['city'] || '',
              province: result[0]['province'] || '',
              country: result[0]['country'] || '',
              userid: `${result[0].id}`,
              hertz: `${self.returnFloat(result[0]['hertz'])}`,
              usersig: sig,
              tag: [],
              likeTag: [],
              paoLikeTag: [],
              rank: `${result[0]['rank']}`,
            };

            //获取粉丝数
            const fansKey = 'ZSET:USER:FANS:' + userid;
            const fansNum = yield redisInstance.zsetScoreGet(fansKey);

            //获取关注数
            const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
            const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

            //获取动态数
            const contentKey = 'ZSET:USER:CONTENT:' + userid;
            const contentNum = yield redisInstance.zsetScoreGet(contentKey);

            data['fansNum'] = fansNum.toString();
            data['attentionNum'] = attentionNum.toString();
            data['contentNum'] = contentNum.toString();
            data['avatar'] = AVATAR_WAILIAN + data['avatar'];

            const result_ = yield mysqlInstance.getUserInfoByMobile(args['mobile']);
            if (result_.length) {
              data['NOOType'] = '1';
              const tagArrStr = result_[0]['tagArr'];
              const tagArr = tagArrStr.split(',');
              let tagInfoArr = [];
              let paoTagInfoArr = [];
              for (let i = 0; i < tagArr.length; i++) {
                const num = tagArr[i];
                const tagInfo = TAG[num];
                if (tagInfo['type'] == 0) {
                  paoTagInfoArr.push(tagInfo);
                } else {
                  tagInfoArr.push(tagInfo);
                }
              }

              const likeTagArrStr = result_[0]['likeTagArr'];
              const likeTagArr = likeTagArrStr.split(',');
              let likeTagInfoArr = [];
              for (let i = 0; i < likeTagArr.length; i++) {
                const num = likeTagArr[i];
                const likeTagInfo = TAG[num];
                likeTagInfoArr.push(likeTagInfo);
              }
              data['paoLikeTag'] = paoTagInfoArr;
              data['tag'] = tagInfoArr;
              data['likeTag'] = likeTagInfoArr;
            }
            yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['ssid', sessionResult, 'userSig', sig]);
            yield mysqlInstance.updateUsersig(userid, sig);
            //1:登陆 2:注销
            yield redisInstance.onlySet(`SET:LOGIN:STATUS:${userid}`, '1');
            yield self.updateUserLocation(result[0]['locationStatus'], result[0]['id'], args['ip']);
            if (userid === 7) {
              const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
              yield imService.sendBulk(msg, ['7']);
            }
            return {
              backSuccess: true,
              data: data,
            };
          } else {
            elogger.error('Common Error 1002 redis设置错误');
            return {
              backSuccess: false,
              msg: 'Common Error 1002',
            };
          }
        } else {
          return {
            backSuccess: false,
            msg: '非老用户，请注册账号',
          };
        }
      } else if (args['type'] === 'new') {
        if (!args['invitationCode']) {
          return {
            backSuccess: false,
            msg: '请输入邀请码',
          };
        }
        const result = yield mysqlInstance.getUserExit(args['mobile']);
        if (result && result['type'] === 'error') {
          elogger.error('Common Error 1001 ' + result['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 1001',
          };
        }
        if (result.length) {
          return {
            backSuccess: false,
            msg: '请使用老用户登陆入口',
          };
        }
        const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
        const index = Math.floor(Math.random() * locationArr.length);
        const location = locationArr[index];
        args['province'] = location;
        args['city'] = location;
        args['country'] = location;
        args['ipStatus'] = 1;
        const birth = parseInt(Math.round(new Date().getTime() / 1000));
        args['createtime'] = birth;
        const result_ = yield mysqlInstance.mobileRegisteredFun(args);
        if (result_ && result_['type'] === 'error') {
          elogger.error('Common Error 1003 ' + result_['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 1003',
          };
        }
        const sig_ = new SIG.Sig(config);
        const sig = sig_.genSig(`${result_}`);
        yield mysqlInstance.updateUsersig(result_, sig);
        yield redisInstance.onlySet(`SET:LOGIN:STATUS:${result_}`, '1');
        const rankRs = yield mysqlInstance.getRank(result_);
        const rank = rankRs[0].rank;
        const sessionResult = yield redisInstance.setSessionToRedis(result_);
        if (sessionResult) {
          const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + result_, [
            'mobile',
            args['mobile'],
            'ssid',
            sessionResult,
            'userid',
            result_,
            'nickname',
            'whale',
            'signature',
            '',
            'birth',
            birth,
            'gender',
            1,
            'hertz',
            '52.00',
            'rank',
            rank,
            'type',
            1,
            'city',
            args['city'] || '',
            'province',
            args['province'] || '',
            'country',
            args['country'] || '',
            'rank',
            rank,
            'userSig',
            sig,
          ]);
          if (redisUserInfo && redisUserInfo['type'] === 'error') {
            elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${result_}`);
          }

          const redisUserPushCount = yield redisInstance.onlySet(`STR:USER:PUSH:COUNT:${result_}`, '0');
          yield mysqlInstance.updateUserRank(result_, rank);
          yield mysqlInstance.updateSSID(sessionResult, result_);
          yield mysqlInstance.updateUserSignature(result_, rank);
          const accountImportData = {
            userid: `${result_}`,
            nickName: 'whale',
            avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
          };
          // const accountImportRs = yield imService.accountImport(accountImportData);
          // if (accountImportRs['backSuccess']) {
          //   yield mysqlInstance.updateUserStatus(result_, 0);
          // } else {
          //   yield mysqlInstance.updateUserStatus(result_, 1);
          // }
          yield mysqlInstance.updateUserStatus(result_, 0);
          yield mysqlInstance.updateUserInvitCodeUse(args['invitationCode']);
          const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
          yield imService.sendBulk(msg, [`${result_}`]);
          return {
            backSuccess: true,
            data: {
              mobile: args['mobile'].toString(),
              account: null,
              NOOType: '0', //1:已有账号 0:新用户
              ssid: sessionResult.toString(),
              userid: result_.toString(),
              nickname: 'whale',
              avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
              signature: '',
              birth: `${birth}`,
              gender: '1',
              city: args['city'] || '',
              province: args['province'] || '',
              country: args['country'] || '',
              usersig: sig,
              tag: [],
              likeTag: [],
              paoLikeTag: [],
              rank: rank.toString(),
              fansNum: '0',
              attentionNum: '0',
              contentNum: '0',
              hertz: '52.00',
            },
          };
        } else {
          elogger.error('redis设置错误');
          return {
            backSuccess: false,
            msg: 'Common Error 1004',
          };
        }
      } else {
        return {
          backSuccess: false,
          msg: 'type can not be null',
        };
      }
    });
  }

  accountLogin(user_id, ip) {
    const self = this;
    return co(function* () {
      const result = yield mysqlInstance.getUserAndAccountInfoByUserid(user_id);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 1001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 1001',
        };
      }

      if (!result.length) {
        return {
          backSuccess: false,
          msg: '账号不存在',
        };
      }
      if (blackUserArr.indexOf(parseInt(result[0].id)) > -1) {
        return {
          backSuccess: false,
          msg: '帐号封禁 拒绝登录',
        };
      }
      yield redisInstance.clearSessionInfo(result[0]['ssid']);
      const sessionResult = yield redisInstance.setSessionToRedis(result[0]['id']);
      if (sessionResult) {
        yield mysqlInstance.updateSSID(sessionResult, result[0]['id']);
        const userid = result[0].id;
        const sig_ = new SIG.Sig(config);
        const sig = sig_.genSig(`${userid}`);
        const data = {
          account: result[0].account,
          nickname: result[0]['nickname'],
          mobile: result[0]['mobile'] ? result[0]['mobile'].toString() : '',
          avatar: result[0]['avatar'],
          signature: result[0]['signature'],
          birth: `${result[0]['birth']}`,
          gender: `${result[0]['gender']}`,
          NOOType: '0', //1:已有账号 0:新用户
          ssid: sessionResult.toString(),
          city: result[0]['city'] || '',
          province: result[0]['province'] || '',
          country: result[0]['country'] || '',
          userid: `${result[0].id}`,
          hertz: `${self.returnFloat(result[0]['hertz'])}`,
          usersig: sig,
          tag: [],
          likeTag: [],
          paoLikeTag: [],
          rank: `${result[0]['rank']}`,
        };

        //获取粉丝数
        const fansKey = 'ZSET:USER:FANS:' + userid;
        const fansNum = yield redisInstance.zsetScoreGet(fansKey);

        //获取关注数
        const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
        const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

        //获取动态数
        const contentKey = 'ZSET:USER:CONTENT:' + userid;
        const contentNum = yield redisInstance.zsetScoreGet(contentKey);

        const likeCountRs = yield mysqlInstance.getUserLikeCount(userid);
        data['likeCount'] = likeCountRs.length ? `${likeCountRs[0].count}` : '0';

        data['fansNum'] = fansNum.toString();
        data['attentionNum'] = attentionNum.toString();
        data['contentNum'] = contentNum.toString();
        data['avatar'] = AVATAR_WAILIAN + data['avatar'];

        const result_ = yield mysqlInstance.getUserInfoByUserid2(userid);
        if (result_.length) {
          data['NOOType'] = '1';
          const tagArrStr = result_[0]['tagArr'];
          const tagArr = tagArrStr.split(',');
          let tagInfoArr = [];
          let paoTagInfoArr = [];
          for (let i = 0; i < tagArr.length; i++) {
            const num = tagArr[i];
            const tagInfo = TAG[num];
            if (tagInfo['type'] == 0) {
              paoTagInfoArr.push(tagInfo);
            } else {
              tagInfoArr.push(tagInfo);
            }
          }

          const likeTagArrStr = result_[0]['likeTagArr'];
          const likeTagArr = likeTagArrStr.split(',');
          let likeTagInfoArr = [];
          for (let i = 0; i < likeTagArr.length; i++) {
            const num = likeTagArr[i];
            const likeTagInfo = TAG[num];
            likeTagInfoArr.push(likeTagInfo);
          }
          data['paoLikeTag'] = paoTagInfoArr;
          data['tag'] = tagInfoArr;
          data['likeTag'] = likeTagInfoArr;
        }
        yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['ssid', sessionResult, 'userSig', sig]);
        yield mysqlInstance.updateUsersig(userid, sig);
        //1:登陆 2:注销
        yield redisInstance.onlySet(`SET:LOGIN:STATUS:${userid}`, '1');
        yield self.updateUserLocation(result[0]['locationStatus'], result[0]['id'], ip);
        if (userid === 7) {
          const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
          yield imService.sendBulk(msg, ['7']);
        }
        return {
          backSuccess: true,
          data: data,
        };
      } else {
        elogger.error('Common Error 1002 redis设置错误');
        return {
          backSuccess: false,
          msg: 'Common Error 1002',
        };
      }
    });
  }

  wxLogin(args) {
    let self = this;
    return co(function* () {
      const code = args['code'];
      const userAgent = args['userAgent'].toLowerCase();
      let tokenInfo = yield self.getWxCode(code);
      if (tokenInfo && tokenInfo['type'] === 'error') {
        elogger.error(tokenInfo['msg']);
        return {
          backSuccess: false,
          msg: tokenInfo['msg'],
        };
      }

      if (typeof tokenInfo === 'string') {
        tokenInfo = JSON.parse(tokenInfo);
      }

      if (tokenInfo['errcode']) {
        elogger.error('Common Error 2001 ' + tokenInfo['errmsg']);
        return {
          backSuccess: false,
          msg: 'Common Error 2001',
        };
      }

      const token = tokenInfo['access_token'],
        openid = tokenInfo['openid'];

      let userInfo = yield self.getWxUserInfo(token, openid);

      if (typeof userInfo === 'string') {
        userInfo = JSON.parse(userInfo);
      }
      const unionid = userInfo['unionid'];
      const unionidRs = yield mysqlInstance.getUserByUnionid(unionid);

      if (!unionidRs.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }
      const result = yield mysqlInstance.getUserExitByUnionid(unionid);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 2002' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 2002',
        };
      }
      if (result.length) {
        if (blackUserArr.indexOf(parseInt(result[0].id)) > -1) {
          return {
            backSuccess: false,
            msg: '帐号封禁 拒绝登录',
          };
        }
        yield redisInstance.clearSessionInfo(result[0]['ssid']);
        const sessionResult = yield redisInstance.setSessionToRedis(result[0]['id']);
        if (sessionResult) {
          yield mysqlInstance.updateSSID(sessionResult, result[0]['id']);
          const sig_ = new SIG.Sig(config);
          const sig = sig_.genSig(`${result[0].id}`);
          const data = {
            nickname: result[0]['nickname'],
            mobile: '',
            avatar: result[0]['avatar'],
            signature: result[0]['signature'],
            birth: result[0]['birth'].toString(),
            gender: result[0]['gender'].toString(),
            NOOType: '0', //1:已有账号 0:新用户
            ssid: sessionResult.toString(),
            city: result[0]['city'] || '',
            province: result[0]['province'] || '',
            country: result[0]['country'] || '',
            userid: result[0].id.toString(),
            usersig: sig,
            tag: [],
            likeTag: [],
            paoLikeTag: [],
            rank: `${result[0].rank}`,
            hertz: `${self.returnFloat(result[0]['hertz'])}`,
          };
          const result_ = yield mysqlInstance.getUserInfoByUnionid(result[0]['unionid']);
          if (result_.length) {
            data['NOOType'] = '1';
            const tagArrStr = result_[0]['tagArr'];
            const tagArr = tagArrStr.split(',');
            let tagInfoArr = [];
            let paoTagInfoArr = [];
            for (let i = 0; i < tagArr.length; i++) {
              const num = tagArr[i];
              const tagInfo = TAG[num];
              if (tagInfo['type'] == 0) {
                paoTagInfoArr.push(tagInfo);
              } else {
                tagInfoArr.push(tagInfo);
              }
            }

            const likeTagArrStr = result_[0]['likeTagArr'];
            const likeTagArr = likeTagArrStr.split(',');
            let likeTagInfoArr = [];
            for (let i = 0; i < likeTagArr.length; i++) {
              const num = likeTagArr[i];
              const likeTagInfo = TAG[num];
              likeTagInfoArr.push(likeTagInfo);
            }
            data['paoLikeTag'] = paoTagInfoArr;
            data['tag'] = tagInfoArr;
            data['likeTag'] = likeTagInfoArr;
          }
          const userid = result[0].id;
          //获取粉丝数
          const fansKey = 'ZSET:USER:FANS:' + userid;
          const fansNum = yield redisInstance.zsetScoreGet(fansKey);

          //获取关注数
          const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
          const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

          //获取动态数
          const contentKey = 'ZSET:USER:CONTENT:' + userid;
          const contentNum = yield redisInstance.zsetScoreGet(contentKey);

          data['fansNum'] = fansNum.toString();
          data['attentionNum'] = attentionNum.toString();
          data['contentNum'] = contentNum.toString();
          data['avatar'] = AVATAR_WAILIAN + data['avatar'];
          yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['ssid', sessionResult, 'userSig', sig]);
          yield mysqlInstance.updateUsersig(userid, sig);
          yield redisInstance.onlySet(`SET:LOGIN:STATUS:${userid}`, '1');
          yield self.updateUserLocation(result[0]['locationStatus'], result[0]['id'], args['ip']);
          return {
            backSuccess: true,
            data: data,
          };
        } else {
          elogger.error('redis设置错误');
          return {
            backSuccess: false,
            msg: 'Common Error 2003',
          };
        }
      }
      const birth = parseInt(Math.round(new Date().getTime() / 1000));
      const data = {
        createtime: birth,
        unionid: unionid,
        invitationCode: args['invitationCode'],
        province: '',
        city: '',
        country: '',
      };
      // const ip = args['ip'];
      // if (ip) {
      //     const rs = yield self.getIpAddressFun(ip);
      //     const RS = JSON.parse(rs);
      //     const status = RS['status'];
      //     if (status == 0) {
      //         const addres = RS['address'];
      //         const country = addres.split("|")[0];
      //         const province = RS['content']['address_detail']['province'];
      //         const city = RS['content']['address_detail']['city'];
      //         data['province'] = province;
      //         data['city'] = city;
      //         data['country'] = country;
      //         data['ipStatus'] = 1
      //     }
      // }
      const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
      const index = Math.floor(Math.random() * locationArr.length);
      const location = locationArr[index];
      data['province'] = location;
      data['city'] = location;
      data['country'] = location;
      data['ipStatus'] = 1;
      const result_ = yield mysqlInstance.wxRegisteredFun(data);
      if (result_ && result_['type'] === 'error') {
        elogger.error('Common Error 2004 ' + result_['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 2004',
        };
      }
      const sig_ = new SIG.Sig(config);
      const sig = sig_.genSig(`${result_}`);
      yield mysqlInstance.updateUsersig(result_, sig);
      yield redisInstance.onlySet(`SET:LOGIN:STATUS:${result_}`, '1');
      const rankRs = yield mysqlInstance.getRank(result_);
      const rank = rankRs[0].rank;
      const sessionResult = yield redisInstance.setSessionToRedis(result_);
      if (sessionResult) {
        const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + result_, [
          'unionid',
          unionid,
          'ssid',
          sessionResult,
          'userid',
          result_,
          'type',
          2,
          'city',
          data['city'] || '',
          'province',
          data['province'] || '',
          'country',
          data['country'] || '',
          'rank',
          rank,
          'userSig',
          sig,
        ]);
        if (redisUserInfo && redisUserInfo['type'] === 'error') {
          elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${result_}`);
        }
        const redisUserPushCount = yield redisInstance.onlySet(`STR:USER:PUSH:COUNT:${result_}`, '0');
        yield mysqlInstance.updateUserRank(result_, rank);
        yield mysqlInstance.updateSSID(sessionResult, result_);
        yield mysqlInstance.updateUserSignature(result_, rank);
        const accountImportData = {
          userid: `${result_}`,
          nickName: 'whale',
          avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
        };
        // const accountImportRs = yield imService.accountImport(accountImportData);
        // if (accountImportRs['backSuccess']) {
        //   yield mysqlInstance.updateUserStatus(result_, 0);
        // } else {
        //   yield mysqlInstance.updateUserStatus(result_, 1);
        // }
        yield mysqlInstance.updateUserStatus(result_, 0);
        const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
        yield imService.sendBulk(msg, [`${result_}`]);
        return {
          backSuccess: true,
          data: {
            mobile: '',
            NOOType: '0', //1:已有账号 0:新用户
            ssid: sessionResult.toString(),
            userid: result_.toString(),
            nickname: 'whale',
            avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
            signature: '',
            birth: `${birth}`,
            gender: '1',
            city: data['city'] || '',
            province: data['province'] || '',
            country: data['country'] || '',
            usersig: sig,
            tag: [],
            likeTag: [],
            paoLikeTag: [],
            rank: rank.toString(),
            fansNum: '0',
            attentionNum: '0',
            contentNum: '0',
            hertz: '0',
          },
        };
      } else {
        elogger.error('redis设置错误');
        return {
          backSuccess: false,
          msg: 'Common Error 2005',
        };
      }
    });
  }

  wxLoginNew(args) {
    let self = this;
    return co(function* () {
      const code = args['code'];
      let tokenInfo = yield self.getWxCode(code);
      const userAgent = args['userAgent'].toLowerCase();
      if (tokenInfo && tokenInfo['type'] === 'error') {
        elogger.error(tokenInfo['msg']);
        return {
          backSuccess: false,
          msg: tokenInfo['msg'],
        };
      }

      if (typeof tokenInfo === 'string') {
        tokenInfo = JSON.parse(tokenInfo);
      }

      if (tokenInfo['errcode']) {
        elogger.error('Common Error 2001 ' + tokenInfo['errmsg']);
        return {
          backSuccess: false,
          msg: 'Common Error 2001',
        };
      }

      const token = tokenInfo['access_token'],
        openid = tokenInfo['openid'],
        unionid = tokenInfo['unionid'];

      // let userInfo = yield self.getWxUserInfo(token, openid);
      // if (typeof userInfo === 'string') {
      //     userInfo = JSON.parse(userInfo);
      // }
      // const unionid = userInfo['unionid'];
      const unionidRs = yield mysqlInstance.getUserByUnionid(unionid);
      if (!unionidRs.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }

      if (args['type'] === 'old') {
        const result = yield mysqlInstance.getUserExitByUnionid(unionid);
        if (result && result['type'] === 'error') {
          elogger.error('Common Error 2002' + result['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 2002',
          };
        }
        if (result.length) {
          if (blackUserArr.indexOf(parseInt(result[0].id)) > -1) {
            return {
              backSuccess: false,
              msg: '帐号封禁 拒绝登录',
            };
          }
          yield redisInstance.clearSessionInfo(result[0]['ssid']);
          const sessionResult = yield redisInstance.setSessionToRedis(result[0]['id']);

          if (sessionResult) {
            yield mysqlInstance.updateSSID(sessionResult, result[0]['id']);
            const sig_ = new SIG.Sig(config);
            const sig = sig_.genSig(`${result[0].id}`);
            const data = {
              nickname: result[0]['nickname'],
              mobile: '',
              avatar: result[0]['avatar'],
              signature: result[0]['signature'],
              birth: result[0]['birth'].toString(),
              gender: result[0]['gender'].toString(),
              NOOType: '0', //1:已有账号 0:新用户
              ssid: sessionResult.toString(),
              city: result[0]['city'] || '',
              province: result[0]['province'] || '',
              country: result[0]['country'] || '',
              userid: result[0].id.toString(),
              usersig: sig,
              tag: [],
              likeTag: [],
              paoLikeTag: [],
              rank: `${result[0].rank}`,
              hertz: `${self.returnFloat(result[0]['hertz'])}`,
            };
            const result_ = yield mysqlInstance.getUserInfoByUnionid(result[0]['unionid']);
            if (result_.length) {
              data['NOOType'] = '1';
              const tagArrStr = result_[0]['tagArr'];
              const tagArr = tagArrStr.split(',');
              let tagInfoArr = [];
              let paoTagInfoArr = [];
              for (let i = 0; i < tagArr.length; i++) {
                const num = tagArr[i];
                const tagInfo = TAG[num];
                if (tagInfo['type'] == 0) {
                  paoTagInfoArr.push(tagInfo);
                } else {
                  tagInfoArr.push(tagInfo);
                }
              }

              const likeTagArrStr = result_[0]['likeTagArr'];
              const likeTagArr = likeTagArrStr.split(',');
              let likeTagInfoArr = [];
              for (let i = 0; i < likeTagArr.length; i++) {
                const num = likeTagArr[i];
                const likeTagInfo = TAG[num];
                likeTagInfoArr.push(likeTagInfo);
              }
              data['paoLikeTag'] = paoTagInfoArr;
              data['tag'] = tagInfoArr;
              data['likeTag'] = likeTagInfoArr;
            }
            const userid = result[0].id;
            //获取粉丝数
            const fansKey = 'ZSET:USER:FANS:' + userid;
            const fansNum = yield redisInstance.zsetScoreGet(fansKey);

            //获取关注数
            const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
            const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

            //获取动态数
            const contentKey = 'ZSET:USER:CONTENT:' + userid;
            const contentNum = yield redisInstance.zsetScoreGet(contentKey);

            data['fansNum'] = fansNum.toString();
            data['attentionNum'] = attentionNum.toString();
            data['contentNum'] = contentNum.toString();
            data['avatar'] = AVATAR_WAILIAN + data['avatar'];
            yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['ssid', sessionResult, 'userSig', sig]);
            yield mysqlInstance.updateUsersig(userid, sig);
            yield redisInstance.onlySet(`SET:LOGIN:STATUS:${userid}`, '1');
            yield self.updateUserLocation(result[0]['locationStatus'], result[0]['id'], args['ip']);

            return {
              backSuccess: true,
              data: data,
            };
          } else {
            elogger.error('redis设置错误');
            return {
              backSuccess: false,
              msg: 'Common Error 2003',
            };
          }
        } else {
          return {
            backSuccess: false,
            msg: '非老用户，请注册账号',
          };
        }
      } else if (args['type'] === 'new') {
        if (!args['invitationCode']) {
          return {
            backSuccess: false,
            msg: '请输入邀请码',
          };
        }
        const result = yield mysqlInstance.getUserExitByUnionid(unionid);
        if (result && result['type'] === 'error') {
          elogger.error('Common Error 2002' + result['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 2002',
          };
        }
        if (result.length) {
          return {
            backSuccess: false,
            msg: '再试一次',
          };
        }
        const birth = parseInt(Math.round(new Date().getTime() / 1000));
        const data = {
          createtime: birth,
          unionid: unionid,
          invitationCode: args['invitationCode'],
          province: '',
          city: '',
          country: '',
        };
        const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
        const index = Math.floor(Math.random() * locationArr.length);
        const location = locationArr[index];
        data['province'] = location;
        data['city'] = location;
        data['country'] = location;
        data['ipStatus'] = 1;
        const result_ = yield mysqlInstance.wxRegisteredFun(data);
        if (result_ && result_['type'] === 'error') {
          elogger.error('Common Error 2004 ' + result_['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 2004',
          };
        }
        const sig_ = new SIG.Sig(config);
        const sig = sig_.genSig(`${result_}`);
        yield mysqlInstance.updateUsersig(result_, sig);
        yield redisInstance.onlySet(`SET:LOGIN:STATUS:${result_}`, '1');
        const rankRs = yield mysqlInstance.getRank(result_);
        const rank = rankRs[0].rank;
        const sessionResult = yield redisInstance.setSessionToRedis(result_);
        if (sessionResult) {
          const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + result_, [
            'unionid',
            unionid,
            'ssid',
            sessionResult,
            'userid',
            result_,
            'type',
            2,
            'city',
            data['city'] || '',
            'province',
            data['province'] || '',
            'country',
            data['country'] || '',
            'rank',
            rank,
            'userSig',
            sig,
          ]);
          if (redisUserInfo && redisUserInfo['type'] === 'error') {
            elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${result_}`);
          }
          const redisUserPushCount = yield redisInstance.onlySet(`STR:USER:PUSH:COUNT:${result_}`, '0');
          yield mysqlInstance.updateUserRank(result_, rank);
          yield mysqlInstance.updateSSID(sessionResult, result_);
          yield mysqlInstance.updateUserSignature(result_, rank);
          const accountImportData = {
            userid: `${result_}`,
            nickName: 'whale',
            avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
          };
          // const accountImportRs = yield imService.accountImport(accountImportData);
          // if (accountImportRs['backSuccess']) {
          //   yield mysqlInstance.updateUserStatus(result_, 0);
          // } else {
          //   yield mysqlInstance.updateUserStatus(result_, 1);
          // }
          yield mysqlInstance.updateUserStatus(result_, 0);
          yield mysqlInstance.updateUserInvitCodeUse(args['invitationCode']);
          const msg = `hi，我是 zoe \n这是一份《52hz 使用指南》\n\n指南：52hz 的底层分发和推荐是基于 NLP（自然语义理解）在 app 里你表达了什么，就会遇见什么。我们鼓励你真实且友善的表达自己的观点和态度，接受其他用户的多元和每个人的局限性，善用拉黑功能。\n\n欢迎你来海里玩`;
          yield imService.sendBulk(msg, [`${result_}`]);
          return {
            backSuccess: true,
            data: {
              mobile: '',
              NOOType: '0', //1:已有账号 0:新用户
              ssid: sessionResult.toString(),
              userid: result_.toString(),
              nickname: 'whale',
              avatar: AVATAR_WAILIAN + '52hz_avatar_new.png',
              signature: '',
              birth: `${birth}`,
              gender: '1',
              city: data['city'] || '',
              province: data['province'] || '',
              country: data['country'] || '',
              usersig: sig,
              tag: [],
              likeTag: [],
              paoLikeTag: [],
              rank: rank.toString(),
              fansNum: '0',
              attentionNum: '0',
              contentNum: '0',
              hertz: '0',
            },
          };
        } else {
          elogger.error('redis设置错误');
          return {
            backSuccess: false,
            msg: 'Common Error 2005',
          };
        }
      }
    });
  }

  //获取token  openid    // ***
  getWxCode(code) {
    let url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${wxAppId}&secret=${wxAppSecret}&code=${code}&grant_type=authorization_code`;
    return new Promise((resolve, reject) => {
      request(url, function (error, response, body) {
        if (!error && response && response.statusCode == 200) {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: error,
          });
        }
      });
    });
  }

  //获取微信用户信息    // ***
  getWxUserInfo(token, openid) {
    let url2 = `https://api.weixin.qq.com/sns/userinfo?access_token=${token}&openid=${openid}&lang=zh_CN`;
    return new Promise((resolve, reject) => {
      request(url2, function (error, response, body) {
        if (!error && response && response.statusCode == 200) {
          if (typeof body === 'string') {
            body = JSON.parse(body);
          }
          if (body['errcode'] == 40001) {
            resolve({
              type: 'error',
              msg: error,
            });
          } else {
            resolve(body);
          }
        } else {
          resolve({
            type: 'error',
            msg: error,
          });
        }
      });
    });
  }

  wxRegister(data) {
    return co(function* () {
      let userrs = yield mysqlInstance.getUserInfoByOpenid(data.unionid);
      if (userrs && userrs['type'] === 'error') {
        elogger.error('Common Error 3001 ' + userrs['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 3001',
        };
      }
      if (userrs.length) {
        let sessionResult = yield redisInstance.setSessionToRedis(result[0]['id']);
        if (sessionResult) {
          return {
            nickname: userrs[0]['nickname'],
            avatar: userrs[0]['avatar'],
            signature: userrs[0]['signature'],
            birth: userrs[0]['birth'],
            gender: userrs[0]['gender'],
            type: 1, //1:已有账号 0:新用户
            ssid: sessionResult,
            city: userrs[0]['city'],
            province: userrs[0]['province'],
            country: userrs[0]['country'],
            userid: userrs[0]['id'],
          };
        } else {
          return null;
        }
      } else {
        let registerid = yield mysqlInstance.wxRegisteredFun(data);
        if (registerid && registerid['type'] === 'error') {
          elogger.error('Common Error 3002 ' + registerid['msg']);
          return {
            backSuccess: false,
            msg: 'Common Error 3002',
          };
        }
        const sessionResult = yield redisInstance.setSessionToRedis(registerid);
        if (sessionResult) {
          return {
            nickname: data['nickname'],
            avatar: data['avatar'],
            gender: data['gender'],
            type: 0, //1:已有账号 0:新用户
            ssid: sessionResult,
            city: data['city'],
            province: data['province'],
            country: data['country'],
            userid: registerid,
          };
        } else {
          return null;
        }
      }
    });
  }

  logoutFun(ssid, userid) {
    return co(function* () {
      yield redisInstance.clearSessionInfo(ssid);
      yield redisInstance.onlySet(`SET:LOGIN:STATUS:${userid}`, '2');
      return {
        backSuccess: true,
        data: '登出成功',
      };
    });
  }

  getCertificate(type, image_name) {
    return co(function* () {
      const certificate = yield qiniu.getCertificate(type, image_name);
      return {
        backSuccess: true,
        data: certificate,
      };
    });
  }

  getLocationAndPushStatus(userid) {
    return co(function* () {
      const result = yield mysqlInstance.getLocationAndPushStatus(userid);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 4001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 4001',
        };
      }
      const data = {
        locationStatus: `${result[0].locationStatus}`,
        pushStatus: `${result[0].pushStatus}`,
        cellStyle: `${result[0].cellStyle}`,
        homeHide: `${result[0].homeHide}`,
      };
      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  updateCellStyle(userid, style) {
    return co(function* () {
      const result = yield mysqlInstance.updateCellStyle(userid, style);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 5001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 5001',
        };
      }
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  updateHomeHide(userid, hide) {
    return co(function* () {
      const result = yield mysqlInstance.updateHomeHide(userid, hide);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 5001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 5001',
        };
      }
      yield redisInstance.hashAdd(`HASH:USER:INFO:${userid}`, ['homeHide', `${hide}`]);
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  updateLocationAndPushStatus(args) {
    const self = this;
    return co(function* () {
      const result = yield mysqlInstance.updateLocationAndPushStatus(args);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 5001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 5001',
        };
      }

      yield self.updateUserLocation(args['locationStatus'], args['userid'], args['ip'], args['location']);
      // if (parseInt(args['locationStatus']) === 0) {
      //   yield self.updateUserLocation(args['userid'], args['location']);
      // }

      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  updateUserLocation(locationStatus, userid, ip, location) {
    const self = this;
    return co(function* () {
      let country = null;
      let province = null;
      let city = null;

      const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];

      function getRandomOcean() {
        const index = Math.floor(Math.random() * locationArr.length);
        return locationArr[index];
      }

      if (parseInt(locationStatus) === 1 && ip) {
        try {
          const rs = yield self.getIpAddressFun(ip);
          const RS = JSON.parse(rs);

          if (RS && RS['status'] == 0 && RS['address'] && RS['content'] && RS['content']['address_detail']) {
            const addres = RS['address'];
            country = addres.split('|')[0];
            province = RS['content']['address_detail']['province'];
            city = RS['content']['address_detail']['city'];
            if (!country || !province || !city) {
              const ocean = getRandomOcean();
              country = country || ocean;
              province = province || ocean;
              city = city || ocean;
            }
          } else {
            const ocean = getRandomOcean();
            country = ocean;
            province = ocean;
            city = ocean;
          }
        } catch (error) {
          const ocean = getRandomOcean();
          country = ocean;
          province = ocean;
          city = ocean;
        }
      } else {
        if (location) {
          country = location;
          province = location;
          city = location;
        } else {
          const userinfo = yield mysqlInstance.getUserInfoByUserid(userid);
          if (userinfo && userinfo.length > 0) {
            const city_ = userinfo[0]['city'];
            if (locationArr.indexOf(city_) === -1) {
              const ocean = getRandomOcean();
              country = ocean;
              province = ocean;
              city = ocean;
            } else {
              country = city_;
              province = city_;
              city = city_;
            }
          } else {
            const ocean = getRandomOcean();
            country = ocean;
            province = ocean;
            city = ocean;
          }
        }
      }

      const data = {
        country: country,
        province: province,
        city: city,
        userid: userid,
      };

      yield mysqlInstance.updateUserLocation(data);
      const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, [
        'city',
        city || '',
        'province',
        province || '',
        'country',
        country || '',
        'locationStatus',
        locationStatus || 1,
      ]);

      if (redisUserInfo && redisUserInfo['type'] === 'error') {
        elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${userid}`);
      }
      return;
    });
  }

  getUnreadNotification(userid) {
    return co(function* () {
      const result = yield mysqlInstance.getUnreadNotification(userid);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 6001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 6001',
        };
      }
      return {
        backSuccess: true,
        data: { count: `${result[0].count}` },
      };
    });
  }

  getNotificationList(userid, page, size) {
    const self = this;
    return co(function* () {
      const result = yield mysqlInstance.getNotificationList(userid, page, size);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 7001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 7001',
        };
      }
      const arr = [];
      for (let i = 0; i < result.length; i++) {
        let data = {};
        const id = result[i]['id'];
        const avatar = AVATAR_WAILIAN + result[i]['avatar'];
        const nickname = result[i]['nickname'];
        const time = `${result[i]['create_time']}`;
        const text = result[i]['text'];
        const type = parseInt(result[i]['type']);
        const status = result[i]['status'];
        const contentid = result[i]['contentid'] || '';
        const attention_userid = result[i]['attention_userid'] || '';
        let images = result[i]['images'] || '';
        if (images) {
          images = WAILIAN + images;
        }
        const content = result[i]['content'] || '';
        if (type == 3) {
          data = {
            noticationId: `${id}`,
            avatar: avatar,
            nickName: nickname,
            time: time,
            text: text,
            status: `${status}`,
            type: `${type}`,
            contentid: contentid,
            discussid: '',
            images: images,
            content: content,
            authorid: '',
            attention_userid: attention_userid,
          };
          data['colorFont'] = {
            type: 0,
            expire_time: 0,
            status: 0,
          };
        } else if (type == 4) {
          const authorid = yield mysqlInstance.getDiscussById(result[i]['discussid']);
          data = {
            noticationId: `${id}`,
            avatar: avatar,
            nickName: nickname,
            time: time,
            text: text,
            status: `${status}`,
            type: `${type}`,
            contentid: '',
            discussid: `${result[i]['discussid']}`,
            images: images,
            content: content,
            authorid: authorid.length ? `${authorid[0].userid}` : '',
            attention_userid: attention_userid,
          };
          if (authorid.length) {
            data['colorFont'] = yield self.getColorFont(authorid[0].userid);
          } else {
            data['colorFont'] = {
              type: 0,
              expire_time: 0,
              status: 0,
            };
          }
        } else if (type === 5) {
          data = {
            noticationId: `${id}`,
            avatar: avatar,
            nickName: nickname,
            time: time,
            text: text,
            status: `${status}`,
            type: `${type}`,
            contentid: contentid,
            discussid: '',
            images: images,
            content: content,
            authorid: '',
            attention_userid: attention_userid,
            weight: result[i]['weight'],
            to_user_id: result[i]['to_user_id'],
          };
          data['colorFont'] = {
            type: 0,
            expire_time: 0,
            status: 0,
          };
        } else {
          const authorid = yield mysqlInstance.getContentById(contentid);
          data = {
            noticationId: `${id}`,
            avatar: avatar,
            nickName: nickname,
            time: time,
            text: text,
            status: `${status}`,
            type: `${type}`,
            contentid: `${contentid}`,
            discussid: '',
            images: images,
            content: content,
            authorid: authorid.length ? `${authorid[0].userid}` : '',
            attention_userid: attention_userid,
          };
          if (authorid.length) {
            data['colorFont'] = yield self.getColorFont(authorid[0].userid);
          } else {
            data['colorFont'] = {
              type: 0,
              expire_time: 0,
              status: 0,
            };
          }
        }

        arr.push(data);
      }
      const countRs = yield mysqlInstance.getNotificationListCount(userid);
      const count = countRs[0].count;

      const unReadResult = yield mysqlInstance.getUnreadNotification(userid);
      if (unReadResult && unReadResult['type'] === 'error') {
        elogger.error('Common Error 6001 ' + unReadResult['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 6001',
        };
      }

      const data_ = {
        page: `${page}`,
        size: `${size}`,
        count: `${count}`,
        unreadCount: `${unReadResult[0].count}`,
        // unreadCount: '19',
        data: arr,
      };
      return {
        backSuccess: true,
        data: data_,
      };
    });
  }

  updateNotificationStatus(noticationId, userid) {
    return co(function* () {
      const result = yield mysqlInstance.updateNotificationStatus(noticationId, userid);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 8001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 8001',
        };
      }
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  deleteNotification(noticationIds, userid) {
    return co(function* () {
      const result = yield mysqlInstance.deleteNotification(noticationIds, userid);
      if (result && result['type'] === 'error') {
        elogger.error('Common Error 9001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Common Error 9001',
        };
      }
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  feedBack(userid, text, type) {
    const self = this;
    return co(function* () {
      text = self.toLiteral(text);
      const rs = yield mysqlInstance.addFeedBack(userid, text, type);
      if (rs && rs['type'] === 'error') {
        elogger.error('Common Error 10011 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Common Error 10011',
        };
      }
      return {
        backSuccess: true,
        data: [],
      };
    });
  }

  getIpAddress(ip) {
    const self = this;
    return co(function* () {
      const rs = yield self.getIpAddressFun(ip);
      const RS = JSON.parse(rs);
      const status = RS['status'];
      if (status !== 0) {
        elogger.error(RS['message']);
        return {
          backSuccess: false,
          msg: RS['message'],
        };
      }
      const addres = RS['address'];
      const country = addres.split('|')[0];
      const province = RS['content']['address_detail']['province'];
      const city = RS['content']['address_detail']['city'];
      const data = {
        country: country,
        province: province,
        city: city,
      };
      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  //获取userinfo
  getIpAddressFun(ip) {
    const url = `http://api.map.baidu.com/location/ip?ip=${ip}&ak=${AK}&coor=bd09ll`;
    return new Promise((resolve, reject) => {
      request(url, function (error, response, body) {
        if (!error && response && response.statusCode == 200) {
          resolve(body);
        } else {
          resolve({
            type: 'error',
            msg: error,
          });
        }
      });
    });
  }

  changeMobile(args) {
    return co(function* () {
      const { userid, mobile } = args;
      // 查询手机号是否已注册
      const rs = yield mysqlInstance.searchByMobile(mobile);
      if (rs.length) {
        return {
          backSuccess: false,
          msg: '该手机号已注册',
        };
      }
      const updateSqlRs = yield mysqlInstance.updateUserMobile(userid, mobile);
      if (updateSqlRs && updateSqlRs['type'] === 'error') {
        elogger.error('Common Error 11010 ' + updateSqlRs['sql']);
        return {
          backSuccess: false,
          msg: 'Common Error 11010',
        };
      }
      const redisRs = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['mobile', mobile]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:USER:INFO:>>${redisRs['msg']},userid>>${userid}`);
        return {
          backSuccess: false,
          msg: 'Common Error 11011',
        };
      }
      return {
        backSuccess: true,
        data: [],
      };
    });
  }

  getSmsCode(mobile, userAgent) {
    return co(function* () {
      userAgent = userAgent.toLowerCase();
      const mobileRs = yield mysqlInstance.getUserByMobile(mobile);
      if (!mobileRs.length) {
        if (
          userAgent.indexOf('ios') === -1 &&
          userAgent.indexOf('iphone') === -1 &&
          userAgent.indexOf('cfnetwork') === -1
        ) {
          return {
            backSuccess: false,
            code: 1003,
          };
        }
      }
      const codeResult = yield redisInstance.getVerifyCode(mobile);
      if (codeResult[0]) {
        return { backSuccess: true, code: codeResult[0] };
      } else {
        return { backSuccess: false, code: 0 };
      }
    });
  }

  getFree() {
    return co(function* () {
      const rs = yield mysqlInstance.getFree();
      const arr = [];
      for (let i = 0; i < rs.length; i++) {
        const free = rs[i];
        const free_id = free.id;
        const title = free.title;
        const link = free.link;
        const created_at = free.created_at;
        arr.push({
          free_id,
          title,
          link,
          created_at,
        });
      }
      return { backSuccess: true, data: arr };
    });
  }
}

module.exports.commonInstance = new CommonController();
