'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const SFMInstance = require('../services/space_fm').SFMService;
const CHInstance = require('../models/clickhouse').CHInstance;
class testController extends BaseController {
    constructor(props) {
        super(props);
    }

    postReport(args) {
        return co(function* () {
            const userinfo = yield mysqlInstance.getUserInfo(args['reported_userid']);
            if (userinfo && userinfo['type'] === 'error') {
                return {
                    backSuccess: false,
                    msg: 'report-1001 ' + userinfo['msg']
                }
            }
            if (!userinfo.length) {
                return {
                    backSuccess: false,
                    msg: '该用户不存在'
                }
            }
            const result = yield mysqlInstance.postReport(args);
            if (result && result['type'] === 'error') {
                return {
                    backSuccess: false,
                    msg: 'report-1002 ' + result['msg']
                }
            }
            return {
                backSuccess: true,
            }
        })
    }

    putContent(args) {
        const self = this;
        return co(function* () {
            args = self.toLiteral(args)
            const rs = yield mysqlInstance.putContentTest(args);
            if (rs && rs['type'] === 'error') {
                elogger.error('Content Error 1001 ' + rs['sql'])
                return {
                    backSuccess: false,
                    msg: 'Content Error 1001'
                }
            }
        })
    }

    getContent(id) {
        return co(function* () {
            const rs = yield mysqlInstance.getContentTest(id);
            if (rs && rs['type'] === 'error') {
                elogger.error('Content Error 1001 ' + rs['sql'])
                return {
                    backSuccess: false,
                    msg: 'Content Error 1001'
                }
            }
            return {
                backSuccess: true,
                data: rs[0].text
            }
        })
    }

    redisList(userid) {
        return co(function* () {
            const args = {
                userid: userid
            }
            const redisListRs = yield redisInstance.lrange(`LIST:CONTENT:NEW`, 0, -1);
            const redisList = [];
            for (let i = 0; i < redisListRs.length; i++) {
                redisList.push(parseInt(redisListRs[i]));
            }
            if (redisList.indexOf(args['userid']) == -1) {
                if (redisList.length >= 500) {
                    yield redisInstance.rpop(`LIST:CONTENT:NEW`)
                    yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid'])
                } else {
                    yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid'])
                }
            }
            return;
        })
    }

    changeUserHertzFun(userid, type) {
        const self = this
        return co(function* () {
            yield self.changeUserHertz(userid, type)
            return
        })
    }

    async test(URL) {
        const rs = await SFMInstance.getMusicInfo(URL);
        return {
            backSuccess: true,
            data: rs
        }
    }
}



module.exports.testInstance = new testController();