'use strict';
const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const request = require('request');
const URL = 'http://172.16.241.103:10031/zoe/rpc';
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const IMAEWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const TAG = require('../config/tagConfig').tag;
class RecommenController extends BaseController {
  constructor(props) {
    super(props);
  }

  getRecommen(userid, TODAYTIME) {
    const self = this;
    return co(function* () {
      const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;
      // 获取当天是否推荐过
      const recommenRs = yield mysqlInstance.getRecommenByTimeAndUser(userid, TODAYTIME);
      if (recommenRs.length && recommenRs[0].recommen.length) {
        // const inputFilterArr = JSON.parse(recommenRs[0].input_filter);
        const recommenArr = JSON.parse(recommenRs[0].recommen);
        const popArr = recommenArr.concat([]);
        const suiji = [];
        suiji.push(popArr.pop());
        const recommen = yield formatRecommen(userid, null, recommenArr, suiji, 2);
        return {
          backSuccess: true,
          data: recommen,
        };
      } else {
        // 获取输入源数组
        // {
        //     id: contentid,
        //     content: content,
        //     num: 3
        // }
        const inputArr = yield inputFilter(userid);
        const ARR = [];
        let searchArr = [];
        let suiji = [];
        for (let i = 0; i < inputArr.length; i++) {
          ARR.push(inputArr[i]['id']);
          const inputArrData = inputArr[i];
          // 根据输入源获取输出源
          const ZoeRs = yield createRequest(inputArrData);
          if (ZoeRs['result'] && ZoeRs['data'].code === 200) {
            const recommenRs = ZoeRs['data'].result;
            const neighborIdArr = [];
            for (let j = 0; j < recommenRs.length; j++) {
              const neighbor_id = recommenRs[j].neighbor_id;
              neighborIdArr.push(neighbor_id);
            }
            // 过滤输出源
            const outputArr = yield outputFilter(userid, neighborIdArr, inputArrData['num']);
            const outputArr_ = [];
            for (let z = 0; z < outputArr.length; z++) {
              // const inputContent = self.toLiteral(inputArr[i]['content']);
              let inputContent = null;
              const inputType = inputArr[i]['type'];
              switch (inputType) {
                case 1:
                  inputContent = '根据你点赞过的动态推荐';
                  break;
                case 2:
                  inputContent = '根据你评论过的动态推荐';
                  break;
                case 3:
                  inputContent = '根据你发布过的动态推荐';
                  break;
                case 4:
                  inputContent = '昨日最热';
                  break;
              }
              const data = {
                inputid: inputArr[i]['id'],
                inputContent: inputContent,
                outputid: outputArr[z],
              };
              outputArr_.push(data);
            }
            if (i === outputArr_.length - 1) {
              suiji = outputArr_;
            }
            searchArr = searchArr.concat(outputArr_);
          } else {
            return {
              backSuccess: false,
              data: 'RECOMMEN URL ERROR',
            };
          }
        }
        const searchRs = yield formatRecommen(userid, ARR, searchArr, suiji, 1);
        // const searchRs = yield formatRecommen([142, 5422, 6454, 8657, 2411, 8234, 2345, 5323, 5342], [5342]);
        return {
          backSuccess: true,
          data: searchRs,
        };
      }
    });
  }
}

function createRequest(data) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'POST',
      url: URL,
      headers: {
        'cache-control': 'no-cache',
        'Content-Type': 'application/json',
      },
      body: {
        function: 'searchByContents',
        params: { data: [{ item_id: parseInt(data['id']), content: data['content'] }] },
      },
      json: true,
    };
    request(options, function (error, response, body) {
      if (error) {
        resolve({
          result: false,
        });
      }
      resolve({
        result: true,
        data: body,
      });
    });
  });
}

// function createRequest(data) {
//     return new Promise((resolve, reject) => {
//         resolve({
//             result: true,
//             data: BODY
//         })
//     })
// }

async function formatRecommen(userid, inputArr, ARR, SUIJI, type) {
  const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;
  let arr = [];
  const suiji = [];
  for (let i = 0; i < ARR.length; i++) {
    arr.push(ARR[i]['outputid']);
  }
  suiji.push(SUIJI['outputid']);
  arr = arrToSet(arr);
  let result = [];
  const RS = await mysqlInstance.getContentByIn(arr);
  for (let i = 0; i < RS.length; i++) {
    let accordContent = '';
    let accordContentId = '';
    for (let z = 0; z < ARR.length; z++) {
      const outputid = ARR[z]['outputid'];
      if (outputid === RS[i].contentid) {
        accordContent = ARR[z]['inputContent'];
        accordContentId = ARR[z]['inputid'];
      }
    }
    const user = {
      gender: RS[i].gender,
      userid: RS[i].userid,
      avatar: WAILIAN + RS[i].avatar,
      nickname: RS[i].nickname,
      signature: RS[i].signature,
      hertz: RS[i].hertz,
    };
    const content = {
      accordContent: accordContent,
      accordContentId: accordContentId,
      contentid: RS[i].contentid,
      content: RS[i].content === '' ? null : RS[i].content,
      images: null,
      musicInfo: null,
    };
    const tagArrStr = RS[i]['tagArr'];
    let tagArr = tagArrStr.split(',');
    tagArr = arrToSet(tagArr);
    let tagInfoArr = [];
    let paoTagInfoArr = [];
    for (let i = 0; i < tagArr.length; i++) {
      const num = tagArr[i];
      const tagInfo = TAG[num];
      if (tagInfo['type'] == 0) {
        paoTagInfoArr.push(tagInfo);
      } else {
        tagInfoArr.push(tagInfo);
      }
    }
    user['tag'] = tagInfoArr;
    if (RS[i].type == 1) {
      let imageArrStr = '';
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
    } else if (RS[i].type == 2) {
      let imageArrStr = '';
      const musicContent = RS[i].musicContent;
      const musicContentData = JSON.parse(musicContent);
      // const shareUrl = musicContentData['shareUrl']
      // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
      // if (musicInfo && musicInfo['type']) {
      //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      // }
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
      content['musicInfo'] = musicContentData;
    }
    // 1:音乐 2:图文 3:纯文字 4:随机
    if (suiji.indexOf(content['contentid']) > -1) {
      content['iconType'] = 4;
    } else if (content['musicInfo']) {
      content['iconType'] = 1;
    } else if (content['images']) {
      content['iconType'] = 2;
    } else {
      content['iconType'] = 3;
    }
    result.push({
      user: user,
      content: content,
    });
  }
  if (result.length !== 9) {
    let sedArr = [];
    const recommen = await mysqlInstance.getAllRecommenOutput(userid, TODAYTIME);

    for (let i = 0; i < recommen.length; i++) {
      const recommenArr = JSON.parse(recommen[i].recommen);

      const recommenArr_ = [];
      for (let j = 0; j < recommenArr.length; j++) {
        const contentid = recommenArr[j].outputid;
        recommenArr_.push(contentid);
      }
      sedArr = sedArr.concat(recommenArr_);
    }
    sedArr = arrToSet(sedArr);
    const RS = await mysqlInstance.getRecommenContentRandom(userid, sedArr, 9 - result.length);
    const RS_ = [];
    for (let i = 0; i < RS.length; i++) {
      RS_.push(RS[i].id);
      const data = {
        inputid: null,
        inputContent: '昨日最热',
        outputid: RS[i].id,
      };
      ARR.push(data);
    }
    // const randomRs = await formatRecommenV2(RS_)
    const randomRs = await formatRecommenV2(ARR, RS_);
    result = result.concat(randomRs);
  }
  if (type === 1) {
    await mysqlInstance.addRecommen(userid, inputArr, ARR);
  }
  return result;
}

async function formatRecommenV2(ARR, inArr) {
  const result = [];
  const RS = await mysqlInstance.getContentByIn(inArr);
  for (let i = 0; i < RS.length; i++) {
    let accordContent = '';
    let accordContentId = '';
    for (let z = 0; z < ARR.length; z++) {
      const outputid = ARR[z]['outputid'];
      if (outputid === RS[i].contentid) {
        accordContent = ARR[z]['inputContent'];
        accordContentId = ARR[z]['inputid'];
      }
    }
    const user = {
      gender: RS[i].gender,
      userid: RS[i].userid,
      avatar: WAILIAN + RS[i].avatar,
      nickname: RS[i].nickname,
      signature: RS[i].signature,
      hertz: RS[i].hertz,
    };
    const content = {
      accordContent: accordContent,
      accordContentId: accordContentId,
      contentid: RS[i].contentid,
      content: RS[i].content === '' ? null : RS[i].content,
      images: null,
      musicInfo: null,
    };
    const tagArrStr = RS[i]['tagArr'];
    let tagArr = tagArrStr.split(',');
    tagArr = arrToSet(tagArr);
    let tagInfoArr = [];
    let paoTagInfoArr = [];
    for (let i = 0; i < tagArr.length; i++) {
      const num = tagArr[i];
      const tagInfo = TAG[num];
      if (tagInfo['type'] == 0) {
        paoTagInfoArr.push(tagInfo);
      } else {
        tagInfoArr.push(tagInfo);
      }
    }
    user['tag'] = tagInfoArr;
    if (RS[i].type == 1) {
      let imageArrStr = '';
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
    } else if (RS[i].type == 2) {
      let imageArrStr = '';
      const musicContent = RS[i].musicContent;
      const musicContentData = JSON.parse(musicContent);
      // const shareUrl = musicContentData['shareUrl']
      // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
      // if (musicInfo && musicInfo['type']) {
      //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      // }
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
      content['musicInfo'] = musicContentData;
    }
    // 1:音乐 2:图文 3:纯文字 4:随机
    if (content['musicInfo']) {
      content['iconType'] = 1;
    } else if (content['images']) {
      content['iconType'] = 2;
    } else {
      content['iconType'] = 3;
    }
    result.push({
      user: user,
      content: content,
    });
  }
  return result;
}

async function inputFilter(userid) {
  const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;
  const inputArr = [];
  let arr = [];
  const recommen = await mysqlInstance.getAllRecommenInput(userid, TODAYTIME);
  for (let i = 0; i < recommen.length; i++) {
    const inputFilterArr = JSON.parse(recommen[i].input_filter);
    arr = arr.concat(inputFilterArr);
  }
  arr = arrToSet(arr);
  // 根据点赞
  const accordLikeRs = await mysqlInstance.getContentAccordLike(userid, arr);
  for (let i = 0; i < accordLikeRs.length; i++) {
    const data = {
      id: accordLikeRs[i].id,
      content: accordLikeRs[i].content,
      num: 3,
      type: 1,
    };
    inputArr.push(data);
  }
  // 根据评论
  const accordCommentRs = await mysqlInstance.getContentAccordComment(userid, arr);
  for (let i = 0; i < accordCommentRs.length; i++) {
    const data = {
      id: accordCommentRs[i].id,
      content: accordCommentRs[i].content,
      num: 3,
      type: 2,
    };
    inputArr.push(data);
  }
  // 根据动态
  const accordContentRs = await mysqlInstance.getContentAccordContent(userid, arr);
  for (let i = 0; i < accordContentRs.length; i++) {
    const data = {
      id: accordContentRs[i].id,
      content: accordContentRs[i].content,
      num: 2,
      type: 3,
    };
    inputArr.push(data);
  }
  // 随机
  const accordRandRs = await mysqlInstance.getContentAccordRand(userid, arr);
  for (let i = 0; i < accordRandRs.length; i++) {
    const data = {
      id: accordRandRs[i].id,
      content: accordRandRs[i].content,
      num: 1,
      type: 4,
    };
    inputArr.push(data);
  }
  return inputArr;
}

async function outputFilter(userid, searchArr, limit) {
  const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;
  const outputArr = [];
  let sedArr = [];
  const recommen = await mysqlInstance.getAllRecommenOutput(userid, TODAYTIME);
  for (let i = 0; i < recommen.length; i++) {
    const recommenArr = JSON.parse(recommen[i].recommen);
    const recommenArr_ = [];
    for (let j = 0; j < recommenArr.length; j++) {
      if (recommenArr[j].outputid) {
        const contentid = recommenArr[j].outputid;
        recommenArr_.push(contentid);
      }
    }
    sedArr = sedArr.concat(recommenArr_);
  }
  sedArr = arrToSet(sedArr);
  // 过滤拉黑的
  const excludeBlack = await mysqlInstance.getExcludeBlack(searchArr, userid, TODAYTIME);
  for (let i = 0; i < excludeBlack.length; i++) {
    outputArr.push(excludeBlack[i].id);
  }
  // 过滤关注的
  const excludeAttention = await mysqlInstance.getExcludeAttention(searchArr, userid, TODAYTIME);
  for (let i = 0; i < excludeAttention.length; i++) {
    outputArr.push(excludeAttention[i].id);
  }
  // 过滤看过的
  const excludeSed = await mysqlInstance.getExcludeSed(searchArr, sedArr, TODAYTIME);
  for (let i = 0; i < excludeSed.length; i++) {
    outputArr.push(excludeSed[i].id);
  }
  return outputArr.slice(0, limit);
}

function arrToSet(arr) {
  let newSet = new Set();
  for (let i = 0; i < arr.length; i++) {
    newSet.add(arr[i]);
  }
  const newArr = Array.from(newSet);
  return newArr;
}

// 输入元 （）
// async function inputFilter(userid) {
//     const inputArr = [];
//     let arr = [];
//     const recommen = await mysqlInstance.getAllRecommenInput(userid, TODAYTIME);
//     for (let i = 0; i < recommen.length; i++) {
//         const inputFilterArr = JSON.parse(recommen[i].input_filter);
//         arr = arr.concat(inputFilterArr);
//     }
//     arr = arrToSet(arr);
//     // 根据点赞
//     const accordLikeRs = await mysqlInstance.getContentAccordLike(userid, arr);
//     for (let i = 0; i < accordLikeRs.length; i++) {
//         const data = {
//             id: accordLikeRs[i].id,
//             content: accordLikeRs[i].content,
//             num: 3
//         }
//         inputArr.push(data);
//     }
//     // 根据评论
//     const accordCommentRs = await mysqlInstance.getContentAccordComment(userid, arr);
//     for (let i = 0; i < accordCommentRs.length; i++) {
//         const data = {
//             id: accordCommentRs[i].id,
//             content: accordCommentRs[i].content,
//             num: 3
//         }
//         inputArr.push(data);
//     }
//     // 根据动态
//     const accordContentRs = await mysqlInstance.getContentAccordContent(userid, arr);
//     for (let i = 0; i < accordContentRs.length; i++) {
//         const data = {
//             id: accordContentRs[i].id,
//             content: accordContentRs[i].content,
//             num: 2
//         }
//         inputArr.push(data);
//     }
//     // 随机
//     const accordRandRs = await mysqlInstance.getContentAccordRand(userid, arr);
//     for (let i = 0; i < accordRandRs.length; i++) {
//         const data = {
//             id: accordRandRs[i].id,
//             content: accordRandRs[i].content,
//             num: 1
//         }
//         inputArr.push(data);
//     }
//     return inputArr;
// }
// ZOE元 （）
// 输出元 （）
// 格式化元 （）

module.exports.recommenInstance = new RecommenController();
