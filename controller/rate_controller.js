'use strict';
/** 标签分组编排
 *  A:[1~1000]
 *  B:[1001~2000]
 *  C:[2001~3000]
 *  D:[3001~4000]
 *  E:[4001~5000]
 */
const co = require('co');
const BaseController = require('./BaseController');
const tagData = require('../config/tagConfig').attribute;
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const tagWeightMap = require('../config/tagWeight.js');

class RateController extends BaseController {
  constructor(props) {
    super(props);
  }

  getAttribute(type) {
    return co(function* () {
      const result = yield mysqlInstance.getAttribute(type);
      if (result && result['type'] === 'error') {
        elogger.error('Rate Error 1001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Rate Error 1001',
        };
      }
      const arr = [];
      for (let i = 0; i < result.length; i++) {
        const data = {
          tagId: result[i].id,
          name: result[i].name,
          type: result[i].type,
        };
        arr.push(data);
      }
      if (!Array.prototype.shuffle) {
        Array.prototype.shuffle = function () {
          for (
            var j, x, i = this.length;
            i;
            j = parseInt(Math.random() * i), x = this[--i], this[i] = this[j], this[j] = x
          );
          return this;
        };
      }
      const arr_ = arr.shuffle();
      return {
        backSuccess: true,
        data: arr_,
      };
    });
  }

  calculateRate(args) {
    const self = this;
    return co(function* () {
      const userid = args['userid'];
      if (args['type'] == 1) {
        const rate = args['rate'];
        const attribute = args['attribute'];
        const updateUserHertz = yield mysqlInstance.updateUserRateAndVaryRate(rate, args['userid']);
        if (updateUserHertz && updateUserHertz['type'] === 'error') {
          elogger.error('Rate Error 2001 ' + updateUserHertz['msg']);
          return {
            backSuccess: false,
            msg: 'Rate Error 2001',
          };
        }
        const userTagRs = yield mysqlInstance.searchUserTag(args['userid']);
        if (userTagRs.length) {
          //更新
          const updateUserTag = yield mysqlInstance.updateUserTag(
            args['userid'],
            args['tagArrStr'],
            args['likeTagArrStr'],
            attribute,
            rate,
          );
          if (updateUserTag && updateUserTag['type'] === 'error') {
            elogger.error('Rate Error 2002 ' + updateUserTag['msg']);
            return {
              backSuccess: false,
              msg: 'Rate Error 2002',
            };
          }
        } else {
          const insertUSerTag = yield mysqlInstance.insertUSerTag(
            args['userid'],
            args['tagArrStr'],
            args['likeTagArrStr'],
            attribute,
            rate,
          );
          if (insertUSerTag && insertUSerTag['type'] === 'error') {
            elogger.error('Rate Error 2003 ' + insertUSerTag['msg']);
            return {
              backSuccess: false,
              msg: 'Rate Error 2003',
            };
          }
        }

        const depict = tagData[attribute]['depict'];
        const whale = tagData[attribute]['whale'];
        yield redisInstance.hashAdd('HASH:USER:INFO:' + args['userid'], [
          'tag',
          args['tagArrStr'],
          'likeTag',
          args['likeTagArrStr'],
          'hertz',
          rate,
          'vary_hertz',
          rate,
        ]);
        return {
          backSuccess: true,
          data: {
            rate: rate,
            whale: whale,
            depict: depict,
          },
        };
      }
      const tagArr = args['tagIntArr'];
      let aNum = 0;
      let bNum = 0;
      let cNum = 0;
      let dNum = 0;
      let eNum = 0;
      for (let i = 0; i < tagArr.length; i++) {
        const tagNum = parseInt(tagArr[i]);
        const weight = tagWeightMap[tagNum] || 1; // 权重映射，默认1
        if (1 <= tagNum && tagNum <= 1000) {
          aNum += weight;
        } else if (1001 <= tagNum && tagNum <= 2000) {
          bNum += weight;
        } else if (2001 <= tagNum && tagNum <= 3000) {
          cNum += weight;
        } else if (3001 <= tagNum && tagNum <= 4000) {
          dNum += weight;
        } else if (4001 <= tagNum && tagNum <= 5000) {
          eNum += weight;
        }
      }
      const ops = {
        A: aNum,
        B: bNum,
        C: cNum,
        D: dNum,
        E: eNum,
      };
      let tag = null;
      let max = -1;
      let max_ = -1;
      let count = 0;
      let fone = null;
      let ftwo = null;
      for (let key in ops) {
        if (ops[key] > max) {
          max = ops[key];
          fone = `${key}`;
        }
      }

      for (let key in ops) {
        count += ops[key];
      }

      for (let key in ops) {
        if (ops[key] > max_ && fone !== key) {
          max_ = ops[key];
          ftwo = `${key}`;
        }
      }

      if (max / count < 0.3) {
        tag = 'OTHER';
      } else {
        tag = `${fone}${ftwo}`;
      }

      const rateArr = tagData[tag]['interval'];
      // const depict = tagData[tag]['depict'];
      let rate = self.randRate(rateArr);
      // const sortTagArrStr = tagArr.sort().toString();
      // const data = {
      //     sortTagArrStr: sortTagArrStr,
      //     rate: rate
      // }
      // if (HERTZOBJ[userid]) {
      //     if (HERTZOBJ[userid].sortTagArrStr === sortTagArrStr) {
      //         rate = HERTZOBJ[userid].rate
      //     }
      // } else {
      //     HERTZOBJ[userid] = data;
      // }

      return {
        backSuccess: true,
        data: {
          rate: rate,
          attribute: tag,
        },
      };
    });
  }

  randRate(arr) {
    const max = arr[1];
    const min = arr[0];
    const a = (Math.random() * (max - min + 1) + min).toFixed(2);
    return a;
  }

  tagFun() {
    return co(function* () {
      const a = yield mysqlInstance.searchTagSQL();
      const arr = [];
      for (let i = 0; i < a.length; i++) {
        arr.push(a[i].id);
      }
      for (let i = 0; i < 10000; i++) {
        const index = Math.floor(Math.random() * arr.length);
        const tag = arr[index];
        const userArr = [2, 3, 4, 6];
        const index_ = Math.floor(Math.random() * userArr.length);
        const userid = userArr[index_];
        const rs = yield mysqlInstance.addUserVaryAttribute(userid, tag);
      }
    });
  }
}

module.exports.rateInstance = new RateController();
