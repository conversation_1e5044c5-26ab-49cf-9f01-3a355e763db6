'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const TAG = require('../config/tagConfig').tag;
const Attribute = require('../config/tagConfig').attribute;
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const imService = require('../services/IM_service').IMInstance;
const attentionController = require('../controller/attention_controller').attentionInstance;
const pwd = require('pwd');
const SMSClient = require('@alicloud/sms-sdk');
const YEAR_BADGES = DefaultConfig.YEAR_BADGES;

const smsClient = new SMSClient({
  accessKeyId: DefaultConfig.sms.accessKeyId,
  secretAccessKey: DefaultConfig.sms.secretAccessKey,
});

const SIG = require('tls-sig-api');
const config = DefaultConfig.sig_config;

/**
 * badge 类型
 * 1: 研发
 * 2: 点赞王
 * 3: 冒泡王
 * 4: 2019年领队
 * 5: 2020年领队
 * 6: 回归小鲸鱼
 */

class AnchorController extends BaseController {
  constructor(props) {
    super(props);
  }

  getUserInfo(userid) {
    const self = this;
    return co(function* () {
      const key = 'HASH:USER:INFO:' + userid;
      const redisRS = yield redisInstance.hashGet(key);
      const redisUid = redisRS['userid'];
      const arr = [];
      let data = null;
      if (!redisRS || !redisRS['tag'] || !redisRS['likeTag'] || !redisRS['account']) {
        const rs = yield mysqlInstance.getUserAndAccountInfoByUserid(userid);
        if (rs && rs['type'] === 'error') {
          elogger.error('User Error 1001 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 1001',
          };
        }
        if (!rs.length) {
          return {
            backSuccess: false,
            msg: 'User Error 1002 该用户不存在',
          };
        }
        data = {
          userid: rs[0]['id'].toString(),
          account: rs[0]['account'],
          nickname: rs[0]['nickname'],
          gender: rs[0]['gender'].toString(),
          birth: rs[0]['birth'].toString(),
          avatar: rs[0]['avatar'],
          mobile: rs[0]['mobile'] ? rs[0]['mobile'].toString() : '',
          signature: rs[0]['signature'],
          ssid: rs[0]['ssid'],
          NOOType: '0',
          city: rs[0]['city'] || '北冰洋',
          province: rs[0]['province'] || '北冰洋',
          country: rs[0]['country'] || '北冰洋',
          hertz: `${self.returnFloat(rs[0]['hertz'])}`,
          rank: `${rs[0]['rank']}`,
          tag: [],
          likeTag: [],
          paoLikeTag: [],
          usersig: rs[0]['usersig'],
          verify: rs[0]['verify'],
        };
        const result_ = yield mysqlInstance.getUserInfo(userid);
        if (result_.length) {
          data['NOOType'] = '1';
          const tagArrStr = result_[0]['tagArr'];
          let tagArr = tagArrStr.split(',');
          tagArr = self.arrToSet(tagArr);
          let tagInfoArr = [];
          let paoTagInfoArr = [];
          for (let i = 0; i < tagArr.length; i++) {
            const num = tagArr[i];
            const tagInfo = TAG[num];
            if (tagInfo['type'] == 0) {
              paoTagInfoArr.push(tagInfo);
            } else {
              tagInfoArr.push(tagInfo);
            }
          }

          const likeTagArrStr = result_[0]['likeTagArr'];
          const likeTagArr = likeTagArrStr.split(',');
          let likeTagInfoArr = [];
          for (let i = 0; i < likeTagArr.length; i++) {
            const num = likeTagArr[i];
            const likeTagInfo = TAG[num];
            likeTagInfoArr.push(likeTagInfo);
          }
          data['paoLikeTag'] = paoTagInfoArr;
          data['tag'] = tagInfoArr;
          data['likeTag'] = likeTagInfoArr;
        }
      } else {
        data = {
          userid: redisRS['userid'],
          account: redisRS['account'],
          nickname: redisRS['nickname'],
          gender: redisRS['gender'],
          birth: redisRS['birth'],
          avatar: redisRS['avatar'],
          mobile: redisRS['mobile'],
          NOOType: '0',
          ssid: redisRS['ssid'],
          signature: redisRS['signature'],
          city: redisRS['city'],
          province: redisRS['province'],
          country: redisRS['country'],
          hertz: `${self.returnFloat(redisRS['hertz'])}` || '52.00',
          rank: redisRS['rank'],
          tag: [],
          likeTag: [],
          paoLikeTag: [],
          usersig: redisRS['userSig'],
          verify: redisRS['verify'],
        };
        if (redisRS['tag'] && redisRS['likeTag']) {
          data['NOOType'] = '1';
          const tagArrStr = redisRS['tag'];
          let tagArr = tagArrStr.split(',');
          tagArr = self.arrToSet(tagArr);
          let tagInfoArr = [];
          let paoTagInfoArr = [];
          for (let i = 0; i < tagArr.length; i++) {
            const num = tagArr[i];
            const tagInfo = TAG[num];
            if (tagInfo['type'] == 0) {
              paoTagInfoArr.push(tagInfo);
            } else {
              tagInfoArr.push(tagInfo);
            }
          }

          const likeTagArrStr = redisRS['likeTag'];
          const likeTagArr = likeTagArrStr.split(',');
          let likeTagInfoArr = [];
          for (let i = 0; i < likeTagArr.length; i++) {
            const num = likeTagArr[i];
            const likeTagInfo = TAG[num];
            likeTagInfoArr.push(likeTagInfo);
          }
          data['paoLikeTag'] = paoTagInfoArr;
          data['tag'] = tagInfoArr;
          data['likeTag'] = likeTagInfoArr;
        }
      }

      const likeCountRs = yield mysqlInstance.getUserLikeCount(userid);
      data['likeCount'] = likeCountRs.length ? `${likeCountRs[0].count}` : '0';
      //获取粉丝数
      // const fansKey = 'ZSET:USER:FANS:' + userid;
      // const fansNum = yield redisInstance.zsetScoreGet(fansKey);
      const fanscount = yield mysqlInstance.getFansCount(userid);
      const fansNum = fanscount[0].count;
      //获取关注数
      // const attentionKey = 'ZSET:USER:ATTENTION:' + userid
      // const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);
      const attentioncount = yield mysqlInstance.getAttentionCount(userid);
      const attentionNum = attentioncount[0].count;
      //获取动态数
      // const contentKey = 'ZSET:USER:CONTENT:' + userid;
      // const contentNum = yield redisInstance.zsetScoreGet(contentKey);
      const contentcount = yield mysqlInstance.getUserContentCount(userid);
      const contentNum = contentcount[0].count;

      data['fansNum'] = fansNum.toString();
      data['attentionNum'] = attentionNum.toString();
      data['contentNum'] = contentNum.toString();
      data['avatar'] = WAILIAN + data['avatar'];
      // const sig_ = new SIG.Sig(config);
      // const sig = sig_.genSig(`${userid}`)
      // data['usersig'] = sig;
      const user_hertz = data['hertz'];
      for (let key in Attribute) {
        const att = Attribute[key];
        const interval = att['interval'];
        const min = interval[0];
        const max = interval[1];
        if (user_hertz >= min && user_hertz <= max) {
          data['whale'] = att['whale'];
        }
      }
      if (data['hertz'].length) {
        const hertzLength = data['hertz'].length;
        const lengthNum = 5 - hertzLength;
        if (lengthNum == 1) {
          data['hertz'] = data['hertz'] + '0';
        }

        if (lengthNum == 2) {
          data['hertz'] = data['hertz'] + '00';
        }
      }
      const codeRemainTime = yield mysqlInstance.getCodeRemainTime(userid);
      data['codeRemainTime'] = codeRemainTime;
      const codeRs = yield mysqlInstance.getCodeStatus(userid);
      if (codeRs.length) {
        data['inviteCodeStatus'] = true;
      } else {
        data['inviteCodeStatus'] = false;
      }

      const badge = [];
      const badgeRs = yield mysqlInstance.getUserBadge(userid);
      for (let i = 0; i < badgeRs.length; i++) {
        const data = {
          type: badgeRs[i].type,
          type_name: badgeRs[i].type_name,
          introduction: badgeRs[i].introduction,
          icon: badgeRs[i].icon,
        };
        badge.push(data);
      }
      const allBadgeArr = [];
      for (let i = 0; i < badge.length; i++) {
        const type = badge[i].type;
        allBadgeArr.push(type);
      }
      const create_time = yield mysqlInstance.getUserCreateTime(userid);
      const create_time_ = create_time[0].create_time;
      if (allBadgeArr.indexOf(1) < 0) {
        const create_timestamp = parseInt(create_time_);
        const timestamps = Object.keys(YEAR_BADGES).sort((a, b) => a - b);
        for (let i = 0; i < timestamps.length; i++) {
          if (create_timestamp < timestamps[i]) {
            badge.push(YEAR_BADGES[timestamps[i]]);
            break;
          }
        }
        // if (parseInt(create_time_) < 1577808000) {
        //   const data = {
        //     type: 4,
        //     type_name: '领队小鲸鱼',
        //     introduction: '2019年注册用户',
        //     icon: DefaultConfig.wailian.DOC_DOMAIN + '2020年注册.png',
        //   };
        //   badge.push(data);
        // } else if (parseInt(create_time_) < 1609430400 && parseInt(create_time_) >= 1577808000) {
        //   const data = {
        //     type: 5,
        //     type_name: '周岁小鲸鱼',
        //     introduction: '2020年注册用户',
        //     icon: DefaultConfig.wailian.DOC_DOMAIN + '2019年注册.png',
        //   };
        //   badge.push(data);
        // }

        if (parseInt(create_time_) < 1740119512) {
          const data = {
            type: 6,
            type_name: '回归小鲸鱼',
            introduction: '回归用户',
            icon: DefaultConfig.wailian.DOC_DOMAIN + '回归用户.png',
          };
          badge.push(data);
        }
      }

      data['badge'] = badge;
      data['colorFont'] = create_time[0].colorFont;
      const special = yield mysqlInstance.getUserSpecialByUserid(userid);
      data['special'] = special[0].special;
      if (data['signature'] === '') {
        data['signature'] = '连接那些遥远的相似性。';
      }
      arr.push(data);
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  getOtherPersonInfo(userid1, userid) {
    const self = this;

    return co(function* () {
      const key = 'HASH:USER:INFO:' + userid;
      const redisRS = yield redisInstance.hashGet(key);
      const arr = [];
      let data = null;
      if (
        !redisRS ||
        !redisRS['tag'] ||
        !redisRS['likeTag'] ||
        !redisRS['country'] ||
        !redisRS['province'] ||
        !redisRS['city']
      ) {
        const rs = yield mysqlInstance.getUserInfo(userid);
        if (rs && rs['type'] === 'error') {
          elogger.error('User Error 2001 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 2001',
          };
        }
        if (!rs.length) {
          return {
            backSuccess: false,
            msg: 'User Error 2002 该用户不存在',
          };
        }

        data = {
          userid: rs[0]['id'].toString(),
          nickname: rs[0]['nickname'],
          gender: rs[0]['gender'].toString(),
          birth: rs[0]['birth'].toString(),
          avatar: rs[0]['avatar'],
          mobile: '',
          NOOType: '1',
          ssid: '',
          signature: rs[0]['signature'],
          city: rs[0]['city'] || '北冰洋',
          province: rs[0]['province'] || '北冰洋',
          country: rs[0]['country'] || '北冰洋',
          likeTag: [],
          paoLikeTag: [],
          tag: [],
          rank: '',
          usersig: '',
          hertz: `${self.returnFloat(rs[0]['hertz'])}`,
          homeHide: Boolean(rs[0]['homeHide']),
        };

        const tagArrStr = rs[0]['tagArr'];
        let tagArr = tagArrStr.split(',');
        tagArr = self.arrToSet(tagArr);
        const likeTagArrStr = rs[0]['likeTagArr'];
        const likeTagArr = likeTagArrStr.split(',');
        let tagInfoArr = [];
        let paoTagInfoArr = [];
        for (let i = 0; i < tagArr.length; i++) {
          const num = tagArr[i];
          const tagInfo = TAG[num];
          if (tagInfo['type'] == 0) {
            paoTagInfoArr.push(tagInfo);
          } else {
            tagInfoArr.push(tagInfo);
          }
        }
        const likeTagInfoArr = [];
        for (let i = 0; i < likeTagArr.length; i++) {
          const num = likeTagArr[i];
          const likeTagInfo = TAG[num];
          likeTagInfoArr.push(likeTagInfo);
        }
        data['paoLikeTag'] = paoTagInfoArr;
        data['tag'] = tagInfoArr;
      } else {
        data = {
          userid: redisRS['userid'].toString(),
          nickname: redisRS['nickname'],
          gender: redisRS['gender'].toString(),
          birth: redisRS['birth'].toString(),
          avatar: redisRS['avatar'],
          mobile: '',
          NOOType: '1',
          ssid: '',
          signature: redisRS['signature'],
          city: redisRS['city'] || '北冰洋',
          province: redisRS['province'] || '北冰洋',
          country: redisRS['country'] || '北冰洋',
          likeTag: [],
          paoLikeTag: [],
          tag: [],
          rank: '',
          usersig: '',
          hertz: `${self.returnFloat(redisRS['hertz'])}`,
          homeHide: redisRS['homeHide'] ? Boolean(parseInt(redisRS['homeHide'])) : false,
        };

        const tagArrStr = redisRS['tag'];
        let tagArr = tagArrStr.split(',');
        tagArr = self.arrToSet(tagArr);
        const likeTagArrStr = redisRS['likeTag'];
        const likeTagArr = likeTagArrStr.split(',');
        let tagInfoArr = [];
        let paoTagInfoArr = [];
        for (let i = 0; i < tagArr.length; i++) {
          const num = tagArr[i];
          const tagInfo = TAG[num];
          if (tagInfo['type'] == 0) {
            paoTagInfoArr.push(tagInfo);
          } else {
            tagInfoArr.push(tagInfo);
          }
        }

        data['tag'] = tagInfoArr;
        data['paoLikeTag'] = paoTagInfoArr;
      }

      const likeCountRs = yield mysqlInstance.getUserLikeCount(userid);
      data['likeCount'] = likeCountRs.length ? `${likeCountRs[0].count}` : '0';

      // //获取粉丝数
      // const fansKey = 'ZSET:USER:FANS:' + userid;
      // const fansNum = yield redisInstance.zsetScoreGet(fansKey);

      // //获取关注数
      // const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
      // const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

      // //获取动态数
      // const contentKey = 'ZSET:USER:CONTENT:' + userid;
      // const contentNum = yield redisInstance.zsetScoreGet(contentKey);

      //获取粉丝数
      const fanscount = yield mysqlInstance.getFansCount(userid);
      const fansNum = fanscount[0].count;
      //获取关注数
      const attentioncount = yield mysqlInstance.getAttentionCount(userid);
      const attentionNum = attentioncount[0].count;
      //获取动态数
      const contentcount = yield mysqlInstance.getUserContentCount(userid);
      const contentNum = contentcount[0].count;

      //是否已关注
      const attentionData = {
        userid1: userid1,
        userid2: userid,
      };
      data['attentionRs'] = '0';
      data['attention_time'] = '0';
      const attentionRs = yield mysqlInstance.searchAttentionByUserid1(attentionData);
      if (attentionRs.length) {
        const type = attentionRs[0].type;
        const attention_time = attentionRs[0].update_time || attentionRs[0].create_time;
        const now = parseInt(new Date() / 1000);
        const days = now - attention_time;
        const time = parseInt(days / (60 * 60 * 24));
        data['attentionRs'] = `${type}`;
        data['attention_time'] = `${time}`;
      }
      data['fansNum'] = fansNum.toString();
      data['attentionNum'] = attentionNum.toString();
      data['contentNum'] = contentNum.toString();
      data['avatar'] = WAILIAN + data['avatar'];
      const user_hertz = data['hertz'];
      for (let key in Attribute) {
        const att = Attribute[key];
        const interval = att['interval'];
        const min = interval[0];
        const max = interval[1];
        if (user_hertz >= min && user_hertz <= max) {
          data['whale'] = att['whale'];
        }
      }
      const badge = [];
      const badgeRs = yield mysqlInstance.getUserBadge(userid);
      for (let i = 0; i < badgeRs.length; i++) {
        const data = {
          type: badgeRs[i].type,
          type_name: badgeRs[i].type_name,
          introduction: badgeRs[i].introduction,
          icon: `${DefaultConfig.wailian.DOC_DOMAIN}${badgeRs[i].icon}`,
        };
        badge.push(data);
      }
      const allBadgeArr = [];
      for (let i = 0; i < badge.length; i++) {
        const type = badge[i].type;
        allBadgeArr.push(type);
      }
      const create_time = yield mysqlInstance.getUserCreateTime(userid);
      const create_time_ = create_time[0].create_time;
      if (allBadgeArr.indexOf(1) < 0) {
        const create_timestamp = parseInt(create_time_);
        const timestamps = Object.keys(YEAR_BADGES).sort((a, b) => a - b);
        for (let i = 0; i < timestamps.length; i++) {
          if (create_timestamp < timestamps[i]) {
            badge.push(YEAR_BADGES[timestamps[i]]);
            break;
          }
        }
        // if (parseInt(create_time_) < 1577808000) {
        //   const data = {
        //     type: 4,
        //     type_name: '领队小鲸鱼',
        //     introduction: '2019年注册用户',
        //     icon: DefaultConfig.wailian.DOC_DOMAIN + '2020年注册.png',
        //   };
        //   badge.push(data);
        // } else if (parseInt(create_time_) < 1609430400 && parseInt(create_time_) >= 1577808000) {
        //   const data = {
        //     type: 5,
        //     type_name: '周岁小鲸鱼',
        //     introduction: '2020年注册用户',
        //     icon: DefaultConfig.wailian.DOC_DOMAIN + '2019年注册.png',
        //   };
        //   badge.push(data);
        // }

        if (parseInt(create_time_) < 1740119512) {
          const data = {
            type: 6,
            type_name: '回归小鲸鱼',
            introduction: '回归用户',
            icon: `${DefaultConfig.wailian.DOC_DOMAIN}back.png`,
          };
          badge.push(data);
        }
      }

      data['badge'] = badge;
      data['colorFont'] = create_time[0].colorFont;

      const special = yield mysqlInstance.getUserSpecialByUserid(userid);
      data['special'] = special[0].special;
      if (data['signature'] === '') {
        data['signature'] = '连接那些遥远的相似性。';
      }

      arr.push(data);

      const redislist = yield redisInstance.lpush(`LIST:USER:HOMEPAGE:${userid1}`, userid);
      if (parseInt(redislist) == 30) {
        yield redisInstance.rpop(`LIST:USER:HOMEPAGE:${userid1}`);
      }

      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  getImUserInfo(userids) {
    const self = this;
    return {
      backSuccess: true,
      data: [],
    };
    // return co(function* () {
    //   const useridsArr = userids.split(',');
    //   const data = [];
    //   for (let i = 0; i < useridsArr.length; i++) {
    //     const key = 'HASH:USER:INFO:' + useridsArr[i];
    //     const redisRS = yield redisInstance.hashGet(key);
    //     if (!redisRS || !redisRS['avatar'] || !redisRS['nickname']) {
    //       const r = /^\+?[1-9][0-9]*$/; //正整数
    //       const flag = r.test(`${useridsArr[i]}`);
    //       if (flag) {
    //         const result = yield mysqlInstance.getImUserInfo(parseInt(useridsArr[i]));
    //         if (result && result['type'] === 'error') {
    //           elogger.error('User Error 3001 ' + result['msg']);
    //           return {
    //             backSuccess: false,
    //             msg: 'User Error 3001',
    //           };
    //         }
    //         if (result.length) {
    //           const info = {
    //             userid: `${useridsArr[i]}`,
    //             avatar: WAILIAN + result[0].avatar,
    //             nickName: result[0].nickname,
    //           };
    //           const colorFont = yield self.getColorFont(useridsArr[i]);
    //           info['colorFont'] = colorFont;
    //           const special = yield mysqlInstance.getUserSpecialByUserid(useridsArr[i]);
    //           info['special'] = special[0].special;
    //           data.push(info);
    //         } else {
    //           const info = {
    //             userid: `${useridsArr[i]}`,
    //             avatar: DefaultConfig.wailian.AVATAR_DOMAIN + '52hz_avatar_new.png',
    //             nickName: '小鲸鱼',
    //           };
    //           const colorFont = yield self.getColorFont(useridsArr[i]);
    //           info['colorFont'] = colorFont;
    //           const special = yield mysqlInstance.getUserSpecialByUserid(useridsArr[i]);
    //           info['special'] = special[0].special;
    //           data.push(info);
    //         }
    //       } else {
    //         const info = {
    //           userid: `${useridsArr[i]}`,
    //           avatar: DefaultConfig.wailian.AVATAR_DOMAIN + '52hz_avatar_new.png',
    //           nickName: '小鲸鱼',
    //           special: 0,
    //         };
    //         const colorFont = yield self.getColorFont(useridsArr[i]);
    //         info['colorFont'] = colorFont;
    //         data.push(info);
    //       }
    //     } else {
    //       const info = {
    //         userid: `${useridsArr[i]}`,
    //         avatar: WAILIAN + redisRS['avatar'],
    //         nickName: redisRS['nickname'],
    //       };
    //       const colorFont = yield self.getColorFont(useridsArr[i]);
    //       info['colorFont'] = colorFont;
    //       const special = yield mysqlInstance.getUserSpecialByUserid(useridsArr[i]);
    //       info['special'] = special[0].special;
    //       data.push(info);
    //     }
    //   }
    //   return {
    //     backSuccess: true,
    //     data: data,
    //   };
    // });
  }

  getImUserInfoV2(userids) {
    return co(function* () {
      const useridsArr = userids.split(',');
      const data = [];
      const arr = [];
      for (let i = 0; i < useridsArr.length; i++) {
        const key = 'HASH:USER:INFO:' + useridsArr[i];
        const redisRS = yield redisInstance.hashGet(key);
        if (!redisRS || !redisRS['avatar'] || !redisRS['nickname']) {
          const r = /^\+?[1-9][0-9]*$/; //正整数
          const flag = r.test(`${useridsArr[i]}`);
          if (flag) {
            arr.push(parseInt(useridsArr[i]));
            // const result = yield mysqlInstance.getImUserInfo(parseInt(useridsArr[i]));
            // if (result && result['type'] === 'error') {
            //     elogger.error('User Error 3001 ' + result['msg']);
            //     return {
            //         backSuccess: false,
            //         msg: 'User Error 3001'
            //     }
            // }
            // if (result.length) {
            //     const info = {
            //         userid: `${useridsArr[i]}`,
            //         avatar: WAILIAN + result[0].avatar,
            //         nickName: result[0].nickname
            //     }
            //     data.push(info);
            // } else {
            //     const info = {
            //         userid: `${useridsArr[i]}`,
            //         avatar: 'http://avatar.fiftytwohz.com/52hz_avatar_new.png',
            //         nickName: '小鲸鱼'
            //     }
            //     data.push(info);
            // }
          } else {
            const info = {
              userid: `${useridsArr[i]}`,
              avatar: DefaultConfig.wailian.AVATAR_DOMAIN + '52hz_avatar_new.png',
              nickName: '小鲸鱼',
            };
            data.push(info);
          }
        } else {
          const info = {
            userid: `${useridsArr[i]}`,
            avatar: WAILIAN + redisRS['avatar'],
            nickName: redisRS['nickname'],
          };
          data.push(info);
        }
      }
      const rs = yield mysqlInstance.getImUserInfoV2(arr);
      for (let i = 0; i < rs.length; i++) {
        const info = {
          userid: `${rs[i].id}`,
          avatar: WAILIAN + rs[i].avatar,
          nickName: rs[i].nickname,
        };
        data.push(info);
      }
      const body = [];
      for (let i = 0; i < useridsArr.length; i++) {
        for (let j = 0; j < data.length; j++) {
          if (useridsArr[i] === data[j].id) {
            body.push(data[j]);
          }
        }
      }
      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  postUserBlack(userid, blackUserid) {
    return co(function* () {
      const result = yield mysqlInstance.getUserBlack(userid, blackUserid);
      if (result && result['type'] === 'error') {
        elogger.error('User Error 4001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 4001',
        };
      }
      let type = 0;
      if (result.length) {
        type = 1;
      }
      const result_ = yield mysqlInstance.postUserBlack(userid, blackUserid, type);
      if (result_ && result_['type'] === 'error') {
        elogger.error('User Error 4002 ' + result_['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 4002',
        };
      }

      //取关
      const data = {
        userid1: userid,
        userid2: blackUserid,
      };

      yield attentionController.releaseAttention(data);
      yield redisInstance.setAdd(`SET:USER:BLACK:${userid}`, blackUserid);

      // const result2 = yield mysqlInstance.getUserBlack(blackUserid, userid);
      // if (result2 && result2['type'] === 'error') {
      //     elogger.error('User Error 4003 ' + result2['msg']);
      //     return {
      //         backSuccess: false,
      //         msg: 'User Error 4003'
      //     }
      // }
      // let type2 = 0;
      // let status = 1;
      // if (result2.length) {
      //     type2 = 1;
      //     status = result2[0].status;
      // }
      // if (status == 1) {
      //     const result2_ = yield mysqlInstance.postUserBlack_(blackUserid, userid, type2, status);
      //     if (result2_ && result2_['type'] === 'error') {
      //         elogger.error('User Error 4004 ' + result2_['msg']);
      //         return {
      //             backSuccess: false,
      //             msg: 'User Error 4004'
      //         }
      //     }
      // }
      imService.addImBlack(userid, blackUserid);
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  getUserBlack(userid) {
    return co(function* () {
      const result = yield mysqlInstance.getUserBlackList(userid);
      if (result && result['type'] === 'error') {
        elogger.error('User Error 5001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 5001',
        };
      }
      const arr = [];
      for (let i = 0; i < result.length; i++) {
        const data = {
          userid: `${result[i].id}`,
          nickName: result[i].nickname,
          avatar: WAILIAN + result[i].avatar,
          time: `${result[i].create_time}`,
        };
        arr.push(data);
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  cancelUserBlack(userid, blackUserid) {
    return co(function* () {
      const result = yield mysqlInstance.cancelUserBlack(userid, blackUserid);
      if (result && result['type'] === 'error') {
        elogger.error('User Error 6001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 6001',
        };
      }
      // const result2 = yield mysqlInstance.getUserBlack(blackUserid, userid);
      // if (result2 && result2['type'] === 'error') {
      //     elogger.error('User Error 6002 ' + result2['msg']);
      //     return {
      //         backSuccess: false,
      //         msg: 'User Error 6002'
      //     }
      // }
      // const status = result2[0].status;
      // if (status == 2) {
      //     const result2 = yield mysqlInstance.cancelUserBlack_(blackUserid, userid, 1)
      //     if (result2 && result2['type'] === 'error') {
      //         elogger.error('User Error 6003 ' + resuresult2lt['msg']);
      //         return {
      //             backSuccess: false,
      //             msg: 'User Error 6003'
      //         }
      //     }
      // }
      yield redisInstance.setDel(`SET:USER:BLACK:${userid}`, blackUserid);
      imService.rmImBlack(userid, blackUserid);
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  getUserBlackStatus(userid, blackUserid) {
    return co(function* () {
      const result = yield mysqlInstance.getUserBlack(userid, blackUserid);
      if (result && result['type'] === 'error') {
        elogger.error('User Error 7001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 7001',
        };
      }
      let status = 1;
      if (result.length) {
        const blackStatus = result[0].status;
        status = blackStatus;
      }
      return {
        backSuccess: true,
        data: `${status}`,
      };
    });
  }

  /**
   *
   * @param {用户id}} userid
   * @param {暂无用}} num
   * 40%hertz
   * 30%vary_hertz
   * 20%随机
   * 10%likeTag
   */
  getSimilarUser(userid) {
    const self = this;
    return co(function* () {
      let Info = [];
      let havaArr = [];
      const key = 'SET:USER:SIMILAR:' + userid;
      // yield redisInstance.delKey(key);
      let redisArr = yield redisInstance.setGet(key);
      const rs = yield mysqlInstance.getUserInfo(userid);
      if (rs && rs['type'] === 'error') {
        elogger.error('User Error 8001 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8001',
        };
      }
      if (!rs.length) {
        return {
          backSuccess: false,
          msg: 'User Error 8002 该用户不存在',
        };
      }
      const hertz = rs[0].hertz;
      let hz_max = null;
      let hz_min = null;
      for (const key in Attribute) {
        const arr = Attribute[key]['interval'];
        const max = arr[1];
        const min = arr[0];
        if (hertz <= max && hertz >= min) {
          hz_max = max;
          hz_min = min;
        }
      }

      const vary_hertz = rs[0].vary_hertz;
      let vary_hz_max = null;
      let vary_hz_min = null;
      for (const key in Attribute) {
        const arr = Attribute[key]['interval'];
        const max = arr[1];
        const min = arr[0];
        if (vary_hertz <= max && vary_hertz >= min) {
          vary_hz_max = max;
          vary_hz_min = min;
        }
      }
      //获取拉黑列表
      const blackUserList = yield mysqlInstance.getUserBlackList(userid);
      if (blackUserList && blackUserList['type'] === 'error') {
        elogger.error('User Error 8003 ' + blackUserList['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8003',
        };
      }
      for (let i = 0; i < blackUserList.length; i++) {
        const black_userid = blackUserList[i].black_userid;
        havaArr.push(`${black_userid}`);
      }
      const blackUserList_ = yield mysqlInstance.getUserBlackList_(userid);
      if (blackUserList_ && blackUserList_['type'] === 'error') {
        elogger.error('User Error 8008 ' + blackUserList_['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8008',
        };
      }
      for (let i = 0; i < blackUserList_.length; i++) {
        const userid_ = blackUserList_[i].userid;
        havaArr.push(`${userid_}`);
      }
      //获取关注列表
      const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
      const attentionArr = yield redisInstance.zsetGet(attentionKey);
      if (attentionArr.length) {
        for (let i = 0; i < attentionArr.length; i++) {
          // const key = 'HASH:USER:INFO:' + attentionArr[i];
          // const redisRS = yield redisInstance.hashGet(key);
          // const userid = redisRS['userid'].toString()
          havaArr.push(attentionArr[i]);
        }
      }
      havaArr.push(
        '100024',
        '100068',
        '100075',
        '100060',
        '100225',
        '100212',
        '100416',
        '100132',
        '100635',
        '100029',
        '100089',
        '100114',
        '100146',
        '100161',
        '100186',
        '100208',
        '100248',
        '100252',
        '100254',
        '100266',
        '100270',
        '100295',
        '100300',
        '100303',
        '100343',
        '100346',
        '100352',
        '100394',
        '100399',
        '100445',
        '100450',
        '100457',
        '100458',
        '100485',
        '100522',
        '100533',
        '100560',
        '100586',
        '100599',
        '100600',
        '100606',
        '100621',
        '100637',
        '100642',
        '100669',
        '100724',
        '100731',
        '100800',
        '100238',
        '100107',
        '100127',
        '100158',
        '100160',
        '100164',
        '100173',
        '100176',
        '100181',
        '100184',
        '100190',
        '100214',
        '100224',
        '100230',
        '100243',
        '100256',
        '100260',
        '100269',
        '100299',
        '100315',
        '100355',
        '100418',
        '100433',
        '100443',
        '100456',
        '100491',
        '100544',
        '100561',
        '100566',
        '100651',
        '100653',
        '100687',
        '100821',
        '100847',
        '100864',
        '100503',
        '100546',
        '100636',
        '100795',
        '100820',
        '100867',
        '100878',
        '100888',
        '100968',
        '100972',
        '100997',
        '101001',
        '101002',
        '101010',
        '101966',
        '101967',
        '101968',
        '101969',
        '101970',
        '101971',
        '101972',
        '101973',
        '101974',
        '101975',
        '101976',
        '101977',
        '101978',
        '101979',
        '101980',
        '101981',
        '101982',
        '101983',
        '101984',
        '101985',
        '101986',
        '101987',
        '101988',
        '101989',
        '101990',
        '101991',
        '101992',
        '101993',
        '101994',
        '101995',
        '101996',
        '101997',
        '101998',
        '101999',
        '102000',
        '102001',
        '102002',
        '102003',
        '102004',
        '102005',
        '102006',
        '102007',
        '102008',
        '102009',
        '102010',
        '102011',
        '102012',
        '102013',
        '102014',
        '102015',
        '102016',
        '102017',
        '102018',
        '102019',
        '102020',
        '102021',
        '102022',
        '102023',
        '102024',
        '102025',
        '102026',
        '102027',
        '102028',
        '102029',
        '102030',
        '102031',
        '102032',
        '102033',
        '102034',
        '102035',
        '102036',
        '102037',
        '102038',
        '102039',
        '102040',
        '102041',
        '102042',
        '102043',
        '102044',
        '102045',
        '102046',
        '102047',
        '102048',
        '102049',
        '102050',
        '102051',
        '102052',
        '102053',
        '102054',
        '102055',
        '102056',
        '102057',
        '102058',
        '102059',
        '102060',
        '102061',
        '102062',
        '102063',
        '102064',
        '102065',
        '102066',
        '102067',
        '102068',
        '102069',
        '102070',
        '102071',
        '102072',
        '102073',
        '102074',
        '102075',
        '102076',
        '102077',
        '102078',
        '102079',
        '102080',
        '102081',
        '102082',
        '102083',
        '102084',
        '102085',
        '102086',
        '102087',
        '102088',
        '102089',
        '102090',
        '102091',
        '102092',
        '102093',
        '102094',
        '102095',
        '102096',
        '102097',
        '102098',
        '102099',
        '102100',
        '102101',
        '102102',
        '102103',
        '102104',
        '102105',
        '102106',
        '102107',
        '102108',
        '102109',
        '102110',
        '102111',
        '102112',
        '102113',
        '102114',
        '102115',
        '102116',
        '102117',
        '102118',
        '102119',
        '102120',
        '102121',
        '102122',
        '102123',
        '102124',
        '102125',
        '102126',
        '102127',
        '102128',
        '102129',
        '102130',
        '102131',
        '102132',
        '102133',
        '102134',
        '102135',
        '102136',
        '102137',
        '102138',
        '102139',
        '102140',
        '102141',
        '102142',
        '102143',
        '102144',
        '102145',
        '102146',
        '102147',
        '102148',
        '102149',
        '102150',
        '102151',
        '102152',
        '102153',
        '102154',
        '102155',
        '102156',
        '102157',
        '102158',
        '102159',
        '102160',
        '102161',
        '102162',
        '102163',
        '102164',
        '102165',
        '102166',
        '102167',
        '102168',
        '102169',
        '102170',
        '102171',
        '102172',
        '102173',
        '102174',
        '102175',
        '102176',
        '102177',
        '102178',
        '102179',
        '102180',
        '102181',
        '102182',
        '102183',
        '102184',
        '102185',
        '102186',
        '102187',
        '102188',
        '102189',
        '102190',
        '102191',
        '102192',
        '102193',
        '102194',
        '102195',
        '102196',
        '102197',
        '102198',
        '102199',
        '102200',
        '102201',
        '102202',
        '102203',
        '102204',
        '102205',
        '102206',
        '102207',
        '102208',
        '102209',
        '102210',
        '102211',
        '102212',
        '102213',
        '102214',
        '102215',
        '102216',
        '102217',
        '102218',
        '102219',
        '102220',
        '102221',
        '102222',
        '102223',
        '102224',
        '102225',
        '102226',
        '102227',
        '102228',
        '102229',
        '102230',
        '102231',
        '102232',
        '102233',
        '102234',
        '102235',
        '102236',
        '102237',
        '102238',
        '102239',
        '102240',
      );
      //hertz
      const hertzResult = yield mysqlInstance.getSimilarUserByHertz(hz_max, hz_min, userid);
      if (hertzResult && hertzResult['type'] === 'error') {
        elogger.error('User Error 8004 ' + hertzResult['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8004',
        };
      }

      [Info, havaArr, redisArr] = yield self.selectUnRedis(Info, hertzResult, redisArr, havaArr, 4, userid);
      //随机
      const randomResult = yield mysqlInstance.getRandomUser(userid);
      if (randomResult && randomResult['type'] === 'error') {
        elogger.error('User Error 8005 ' + randomResult['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8005',
        };
      }
      [Info, havaArr, redisArr] = yield self.selectUnRedis(Info, randomResult, redisArr, havaArr, 2, userid);
      //likeTag
      // const likeTagResult = yield mysqlInstance.getSimilarUserByLikeTag(userid);
      //vary_hertz
      const userCreateTime = rs[0].create_time;
      const now = parseInt(Math.round(new Date().getTime() / 1000));

      if (now - userCreateTime >= 432000) {
        const varyHertzResult = yield mysqlInstance.getSimilarUserByVaryHertz(vary_hz_max, vary_hz_min, userid);
        if (varyHertzResult && varyHertzResult['type'] === 'error') {
          elogger.error('User Error 8006 ' + varyHertzResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8006',
          };
        }
        [Info, havaArr, redisArr] = yield self.selectUnRedis(Info, varyHertzResult, redisArr, havaArr, 4, userid);
      }

      if (Info.length < 10) {
        const num = 10 - Info.length;
        const randomResult_ = yield mysqlInstance.getRandomUser(userid);
        if (randomResult_ && randomResult_['type'] === 'error') {
          elogger.error('User Error 8007 ' + randomResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8007',
          };
        }
        [Info, havaArr, redisArr] = yield self.selectUnRedis(Info, randomResult_, redisArr, havaArr, num, userid);
      }

      if (Info.length < 10) {
        yield redisInstance.delKey(key);
        self.getSimilarUser(userid);
      }

      const data = [];
      let my_like_tag = rs[0]['tagArr'].split(',');
      my_like_tag = self.arrToSet(my_like_tag);
      let pao_my_like_tag = [];
      for (let i = 0; i < my_like_tag.length; i++) {
        const num = my_like_tag[i];
        const tagInfo = TAG[num];
        if (tagInfo['type'] == 0) {
          pao_my_like_tag.push(num);
        }
      }

      for (let i = 0; i < Info.length; i++) {
        if (redisArr.includes(`${Info[i].id}`)) {
          const userInfo = {
            userid: Info[i].id.toString(),
            hertz: `${self.returnFloat(Info[i].hertz.toString())}`,
            avatar: WAILIAN + Info[i].avatar,
            nickname: Info[i].nickname,
            gender: Info[i].gender.toString(),
            signature: Info[i].signature,
            birth: Info[i].birth.toString(),
            city: Info[i].city,
          };
          let user_tag = Info[i]['tagArr'].split(',');
          user_tag = self.arrToSet(user_tag);
          let user_tag_arr = [];
          let user_like_tag_arr = [];
          for (let j = 0; j < user_tag.length; j++) {
            const num = user_tag[j];
            const tagInfo = TAG[num];
            if (tagInfo['type'] == 1) {
              const name = tagInfo['name'];
              user_tag_arr.push(name);
            }
          }
          for (let j = 0; j < user_tag.length; j++) {
            const id = user_tag[j];
            if (pao_my_like_tag.includes(id)) {
              const name = TAG[id]['name'];
              user_like_tag_arr.push(name);
            }
          }
          let user_tag_arr_str = '';
          let user_like_tag_arr_str = '';
          for (let j = 0; j < user_tag_arr.length; j++) {
            if (j === user_tag_arr.length - 1) {
              user_tag_arr_str += `${user_tag_arr[j]}`;
            } else {
              user_tag_arr_str += `${user_tag_arr[j]},`;
            }
          }
          for (let j = 0; j < user_like_tag_arr.length; j++) {
            if (j === user_like_tag_arr.length - 1) {
              user_like_tag_arr_str += `${user_like_tag_arr[j]}`;
            } else {
              user_like_tag_arr_str += `${user_like_tag_arr[j]},`;
            }
          }
          userInfo['user_tag'] = user_tag_arr_str;
          userInfo['user_like_tag'] = user_like_tag_arr_str;
          const hertzLength = userInfo['hertz'].length;
          const lengthNum = 5 - hertzLength;
          if (lengthNum == 1) {
            userInfo['hertz'] = userInfo['hertz'] + '0';
          }

          if (lengthNum == 2) {
            userInfo['hertz'] = userInfo['hertz'] + '.00';
          }
          data.push(userInfo);
        }
      }
      let newArr = '';
      for (let i = 0; i < data.length; i++) {
        const id = data[i].userid;
        if (i === data.length - 1) {
          newArr += `${id}`;
        } else {
          newArr += `${id},`;
        }
      }

      const newKey = 'STR:USER:SIMILAR:' + userid;
      // const result__ = yield redisInstance.get(newKey);
      yield redisInstance.delKey(newKey);
      yield redisInstance.set(newKey, newArr, 604800);
      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  selectUnRedis(Info, obj, redisArr, havaArr, num, userid) {
    const objLength = obj.length;
    if (objLength <= num) {
      num = objLength;
    }
    const key = 'SET:USER:SIMILAR:' + userid;
    let Info_ = Info;
    let redisArr_ = redisArr;
    let havaArr_ = havaArr;
    let NewReids = [];
    let HaveArr = [];
    const self = this;
    return co(function* () {
      if (!redisArr_.length) {
        let newRedisArr = [];
        for (let i = 0; i < num; i++) {
          havaArr_.push(`${obj[i]['id']}`);
          HaveArr = havaArr_;
          Info_.push(obj[i]);
          newRedisArr.push(`${obj[i]['id']}`);
        }
        NewReids = newRedisArr;
        if (newRedisArr.length) {
          yield redisInstance.delKey(key);
          const redisRs_ = yield redisInstance.setAddWithTime(key, newRedisArr, 1296000, 3);
        }
      } else {
        const similarArr = [];
        for (let i = 0; i < obj.length; i++) {
          similarArr.push(`${obj[i]['id']}`);
        }
        const arr = similarArr.filter((key_) => !redisArr_.includes(key_));
        let arr_ = arr.filter((key_) => !havaArr_.includes(key_));
        // let newRedisArr = arr_.slice(0, num);
        let newRedisArr = [];
        for (let i = 0; i < num; i++) {
          const index = Math.floor(Math.random() * arr_.length);
          const userid__ = arr_[index];
          arr_.splice(index, 1);
          if (typeof userid__ !== 'undefined' && userid__ !== 'undefined') {
            newRedisArr.push(userid__);
          }
        }

        NewReids = newRedisArr.concat(redisArr_);
        HaveArr = newRedisArr.concat(havaArr_);
        const time = yield redisInstance.ttl(key);
        yield redisInstance.delKey(key);
        const redisRs_ = yield redisInstance.setAddWithTime(key, NewReids, time, 4);
        for (let i = 0; i < obj.length; i++) {
          const id = `${obj[i]['id']}`;
          if (newRedisArr.includes(id)) {
            Info_.push(obj[i]);
          }
        }
      }
      return [Info_, HaveArr, NewReids];
    });
  }

  /**
   *
   * @param {用户id}} userid
   * @param {暂无用}} num
   * 40%hertz
   * 30%vary_hertz
   * 20%随机
   * 10%likeTag
   */
  getSimilarUserNew(userid) {
    const self = this;
    return co(function* () {
      const key = 'SET:USER:SIMILAR:' + userid;
      let excludeArr = [];
      let tuijianArr = [];
      const rs = yield mysqlInstance.getUserInfo(userid);
      if (rs && rs['type'] === 'error') {
        elogger.error('User Error 18001 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 18001',
        };
      }
      if (!rs.length) {
        return {
          backSuccess: false,
          msg: 'User Error 18002 该用户不存在',
        };
      }
      const userCreateTime = rs[0].create_time;
      const now = parseInt(Math.round(new Date().getTime() / 1000));
      const hertz = rs[0].hertz;
      let hz_max = null;
      let hz_min = null;
      for (const key in Attribute) {
        const arr = Attribute[key]['interval'];
        const max = arr[1];
        const min = arr[0];
        if (hertz <= max && hertz >= min) {
          hz_max = max;
          hz_min = min;
        }
      }

      const vary_hertz = rs[0].vary_hertz;
      let vary_hz_max = null;
      let vary_hz_min = null;
      for (const key in Attribute) {
        const arr = Attribute[key]['interval'];
        const max = arr[1];
        const min = arr[0];
        if (vary_hertz <= max && vary_hertz >= min) {
          vary_hz_max = max;
          vary_hz_min = min;
        }
      }

      //排除拉黑，被拉黑，已关注，固定名单, 已推荐
      //获取拉黑列表
      const blackUserList = yield mysqlInstance.getUserBlackFun2(userid);
      if (blackUserList && blackUserList['type'] === 'error') {
        elogger.error('User Error 8003 ' + blackUserList['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8003',
        };
      }
      for (let i = 0; i < blackUserList.length; i++) {
        const black_userid = blackUserList[i].black_userid;
        excludeArr.push(`${black_userid}`);
      }
      const blackUserList_ = yield mysqlInstance.getBeBlackedList(userid);
      if (blackUserList_ && blackUserList_['type'] === 'error') {
        elogger.error('User Error 8008 ' + blackUserList_['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 8008',
        };
      }
      for (let i = 0; i < blackUserList_.length; i++) {
        const userid = blackUserList_[i].userid;
        excludeArr.push(`${userid}`);
      }
      //获取关注列表
      const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
      const attentionArr = yield redisInstance.zsetGet(attentionKey);
      if (attentionArr && attentionArr['type'] === 'error') {
        elogger.error(`ZSET:USER:ATTENTION:>>${attentionArr['msg']},userid>>${userid}`);
      }
      if (attentionArr.length) {
        for (let i = 0; i < attentionArr.length; i++) {
          excludeArr.push(attentionArr[i]);
        }
      }
      //获取已推荐
      const recommendArr = yield redisInstance.setGet('SET:USER:SIMILAR:' + userid);
      if (recommendArr.length) {
        for (let i = 0; i < recommendArr.length; i++) {
          excludeArr.push(recommendArr[i]);
        }
      }

      excludeArr.push(
        '101379',
        '100024',
        '100068',
        '100075',
        '100060',
        '100225',
        '100212',
        '100416',
        '100132',
        '100635',
        '100029',
        '100089',
        '100114',
        '100146',
        '100161',
        '100186',
        '100208',
        '100248',
        '100252',
        '100254',
        '100266',
        '100270',
        '100295',
        '100300',
        '100303',
        '100343',
        '100346',
        '100352',
        '100394',
        '100399',
        '100445',
        '100450',
        '100457',
        '100458',
        '100485',
        '100522',
        '100533',
        '100560',
        '100586',
        '100599',
        '100600',
        '100606',
        '100621',
        '100637',
        '100642',
        '100669',
        '100724',
        '100731',
        '100800',
        '100238',
        '100107',
        '100127',
        '100158',
        '100160',
        '100164',
        '100173',
        '100176',
        '100181',
        '100184',
        '100190',
        '100214',
        '100224',
        '100230',
        '100243',
        '100256',
        '100260',
        '100269',
        '100299',
        '100315',
        '100355',
        '100418',
        '100433',
        '100443',
        '100456',
        '100491',
        '100544',
        '100561',
        '100566',
        '100651',
        '100653',
        '100687',
        '100821',
        '100847',
        '100864',
        '100503',
        '100546',
        '100636',
        '100795',
        '100820',
        '100867',
        '100878',
        '100888',
        '100968',
        '100972',
        '100997',
        '101001',
        '101002',
        '101010',
        '101966',
        '101967',
        '101968',
        '101969',
        '101970',
        '101971',
        '101972',
        '101973',
        '101974',
        '101975',
        '101976',
        '101977',
        '101978',
        '101979',
        '101980',
        '101981',
        '101982',
        '101983',
        '101984',
        '101985',
        '101986',
        '101987',
        '101988',
        '101989',
        '101990',
        '101991',
        '101992',
        '101993',
        '101994',
        '101995',
        '101996',
        '101997',
        '101998',
        '101999',
        '102000',
        '102001',
        '102002',
        '102003',
        '102004',
        '102005',
        '102006',
        '102007',
        '102008',
        '102009',
        '102010',
        '102011',
        '102012',
        '102013',
        '102014',
        '102015',
        '102016',
        '102017',
        '102018',
        '102019',
        '102020',
        '102021',
        '102022',
        '102023',
        '102024',
        '102025',
        '102026',
        '102027',
        '102028',
        '102029',
        '102030',
        '102031',
        '102032',
        '102033',
        '102034',
        '102035',
        '102036',
        '102037',
        '102038',
        '102039',
        '102040',
        '102041',
        '102042',
        '102043',
        '102044',
        '102045',
        '102046',
        '102047',
        '102048',
        '102049',
        '102050',
        '102051',
        '102052',
        '102053',
        '102054',
        '102055',
        '102056',
        '102057',
        '102058',
        '102059',
        '102060',
        '102061',
        '102062',
        '102063',
        '102064',
        '102065',
        '102066',
        '102067',
        '102068',
        '102069',
        '102070',
        '102071',
        '102072',
        '102073',
        '102074',
        '102075',
        '102076',
        '102077',
        '102078',
        '102079',
        '102080',
        '102081',
        '102082',
        '102083',
        '102084',
        '102085',
        '102086',
        '102087',
        '102088',
        '102089',
        '102090',
        '102091',
        '102092',
        '102093',
        '102094',
        '102095',
        '102096',
        '102097',
        '102098',
        '102099',
        '102100',
        '102101',
        '102102',
        '102103',
        '102104',
        '102105',
        '102106',
        '102107',
        '102108',
        '102109',
        '102110',
        '102111',
        '102112',
        '102113',
        '102114',
        '102115',
        '102116',
        '102117',
        '102118',
        '102119',
        '102120',
        '102121',
        '102122',
        '102123',
        '102124',
        '102125',
        '102126',
        '102127',
        '102128',
        '102129',
        '102130',
        '102131',
        '102132',
        '102133',
        '102134',
        '102135',
        '102136',
        '102137',
        '102138',
        '102139',
        '102140',
        '102141',
        '102142',
        '102143',
        '102144',
        '102145',
        '102146',
        '102147',
        '102148',
        '102149',
        '102150',
        '102151',
        '102152',
        '102153',
        '102154',
        '102155',
        '102156',
        '102157',
        '102158',
        '102159',
        '102160',
        '102161',
        '102162',
        '102163',
        '102164',
        '102165',
        '102166',
        '102167',
        '102168',
        '102169',
        '102170',
        '102171',
        '102172',
        '102173',
        '102174',
        '102175',
        '102176',
        '102177',
        '102178',
        '102179',
        '102180',
        '102181',
        '102182',
        '102183',
        '102184',
        '102185',
        '102186',
        '102187',
        '102188',
        '102189',
        '102190',
        '102191',
        '102192',
        '102193',
        '102194',
        '102195',
        '102196',
        '102197',
        '102198',
        '102199',
        '102200',
        '102201',
        '102202',
        '102203',
        '102204',
        '102205',
        '102206',
        '102207',
        '102208',
        '102209',
        '102210',
        '102211',
        '102212',
        '102213',
        '102214',
        '102215',
        '102216',
        '102217',
        '102218',
        '102219',
        '102220',
        '102221',
        '102222',
        '102223',
        '102224',
        '102225',
        '102226',
        '102227',
        '102228',
        '102229',
        '102230',
        '102231',
        '102232',
        '102233',
        '102234',
        '102235',
        '102236',
        '102237',
        '102238',
        '102239',
        '102240',
      );
      //hertz
      // const randomResult = yield mysqlInstance.getRandomUser(userid);
      // if (randomResult && randomResult['type'] === 'error') {
      //     elogger.error('User Error 8005 ' + randomResult['msg']);
      //     return {
      //         backSuccess: false,
      //         msg: 'User Error 8005'
      //     }
      // }
      // [tuijianArr, excludeArr] = yield self.selectUnRedisNew(randomResult, excludeArr, 10, userid)
      if (now - userCreateTime <= 432000) {
        //创建小于五天，按随机推荐
        const randomResult = yield mysqlInstance.getRandomUser(userid);
        if (randomResult && randomResult['type'] === 'error') {
          elogger.error('User Error 8005 ' + randomResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8005',
          };
        }
        [tuijianArr, excludeArr] = yield self.selectUnRedisNew(randomResult, excludeArr, 10, userid, tuijianArr);
      } else {
        //hertz
        const hertzResult = yield mysqlInstance.getSimilarUserByHertz(hz_max, hz_min, userid);
        if (hertzResult && hertzResult['type'] === 'error') {
          elogger.error('User Error 8004 ' + hertzResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8004',
          };
        }

        [tuijianArr, excludeArr] = yield self.selectUnRedisNew(hertzResult, excludeArr, 4, userid, tuijianArr);
        //likeTag
        // const likeTagResult = yield mysqlInstance.getSimilarUserByLikeTag(userid);
        //vary_hertz
        const varyHertzResult = yield mysqlInstance.getSimilarUserByVaryHertz(vary_hz_max, vary_hz_min, userid);
        if (varyHertzResult && varyHertzResult['type'] === 'error') {
          elogger.error('User Error 8006 ' + varyHertzResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8006',
          };
        }
        [tuijianArr, excludeArr] = yield self.selectUnRedisNew(varyHertzResult, excludeArr, 4, userid, tuijianArr);
        //随机
        const randomResult = yield mysqlInstance.getRandomUser2(userid);
        if (randomResult && randomResult['type'] === 'error') {
          elogger.error('User Error 8005 ' + randomResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8005',
          };
        }
        [tuijianArr, excludeArr] = yield self.selectUnRedisNew(randomResult, excludeArr, 2, userid, tuijianArr);
      }

      if (tuijianArr.length < 10) {
        const num = 10 - tuijianArr.length;
        const randomResult_ = yield mysqlInstance.getRandomUser2(userid);
        if (randomResult_ && randomResult_['type'] === 'error') {
          elogger.error('User Error 8007 ' + randomResult['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 8007',
          };
        }
        [tuijianArr, excludeArr] = yield self.selectUnRedisNew(randomResult_, excludeArr, num, userid, tuijianArr);
      }

      if (tuijianArr.length < 10) {
        yield redisInstance.delKey(key);
        self.getSimilarUserNew(userid);
      }

      // if (userid == 7) {
      //     const userInfo = {
      //         id: `101013`,
      //         hertz: `52.00`,
      //         avatar: `${WAILIAN}1589576381548466746`,
      //         nickname: `寸灾`,
      //         gender: `1`,
      //         signature: `1026`,
      //         birth: `823190400`,
      //         city: `太平洋`,
      //         likeTagArr: "5013,21,5023,5026",
      //         tagArr: "1008,1007,11,4,10,10,7,7,2,20,1022,13,5026"
      //     }
      //     tuijianArr.push(userInfo)
      // }

      const data = [];
      let my_like_tag = rs[0]['tagArr'].split(',');
      my_like_tag = self.arrToSet(my_like_tag);
      let pao_my_like_tag = [];
      for (let i = 0; i < my_like_tag.length; i++) {
        const num = my_like_tag[i];
        const tagInfo = TAG[num];
        if (tagInfo['type'] == 0) {
          pao_my_like_tag.push(num);
        }
      }

      for (let i = 0; i < tuijianArr.length; i++) {
        const userInfo = {
          userid: tuijianArr[i].id.toString(),
          hertz: `${self.returnFloat(tuijianArr[i].hertz)}`,
          avatar: WAILIAN + tuijianArr[i].avatar,
          nickname: tuijianArr[i].nickname,
          gender: tuijianArr[i].gender.toString(),
          signature: tuijianArr[i].signature,
          birth: tuijianArr[i].birth.toString(),
          city: tuijianArr[i].city,
        };
        let user_tag = tuijianArr[i]['tagArr'].split(',');
        user_tag = self.arrToSet(user_tag);
        let user_tag_arr = [];
        let user_like_tag_arr = [];
        for (let j = 0; j < user_tag.length; j++) {
          const num = user_tag[j];
          const tagInfo = TAG[num];
          if (tagInfo['type'] == 1) {
            const name = tagInfo['name'];
            user_tag_arr.push(name);
          }
        }
        for (let j = 0; j < user_tag.length; j++) {
          const id = user_tag[j];
          if (pao_my_like_tag.includes(id)) {
            const name = TAG[id]['name'];
            user_like_tag_arr.push(name);
          }
        }
        let user_tag_arr_str = '';
        let user_like_tag_arr_str = '';
        for (let j = 0; j < user_tag_arr.length; j++) {
          if (j === user_tag_arr.length - 1) {
            user_tag_arr_str += `${user_tag_arr[j]}`;
          } else {
            user_tag_arr_str += `${user_tag_arr[j]},`;
          }
        }
        for (let j = 0; j < user_like_tag_arr.length; j++) {
          if (j === user_like_tag_arr.length - 1) {
            user_like_tag_arr_str += `${user_like_tag_arr[j]}`;
          } else {
            user_like_tag_arr_str += `${user_like_tag_arr[j]},`;
          }
        }
        userInfo['user_tag'] = user_tag_arr_str;
        userInfo['user_like_tag'] = user_like_tag_arr_str;
        const hertzLength = userInfo['hertz'].length;
        const lengthNum = 5 - hertzLength;
        if (lengthNum == 1) {
          userInfo['hertz'] = userInfo['hertz'] + '0';
        }

        if (lengthNum == 2) {
          userInfo['hertz'] = userInfo['hertz'] + '.00';
        }
        data.push(userInfo);
      }
      let newArr = '';
      for (let i = 0; i < data.length; i++) {
        const id = data[i].userid;
        if (i === data.length - 1) {
          newArr += `${id}`;
        } else {
          newArr += `${id},`;
        }
      }

      const newKey = 'STR:USER:SIMILAR:' + userid;
      // const result__ = yield redisInstance.get(newKey);
      yield redisInstance.delKey(newKey);
      yield redisInstance.set(newKey, newArr, 604800);
      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  selectUnRedisNew(searchArr, excludeArr, num, userid, outPutInfoArr) {
    const self = this;
    const searchLength = searchArr.length;
    if (searchLength <= num) {
      num = searchLength;
    }
    const arr = [];
    const key = 'SET:USER:SIMILAR:' + userid;
    const beforeOutPutArr = [];
    const outPutArr = [];
    return co(function* () {
      const redisExistedArr = yield redisInstance.setGet(key);
      if (!redisExistedArr.length) {
        for (let i = 0; i < searchArr.length; i++) {
          const userid = `${searchArr[i].id}`;
          arr.push(userid);
          if (excludeArr.indexOf(userid) == -1) {
            beforeOutPutArr.push(userid);
          }
        }

        for (let i = 0; i < num; i++) {
          const index = Math.floor(Math.random() * beforeOutPutArr.length);
          const userid = beforeOutPutArr[index];
          beforeOutPutArr.splice(index, 1);
          outPutArr.push(userid);
        }
        for (let i = 0; i < outPutArr.length; i++) {
          if (typeof outPutArr[i] == 'undefined' && outPutArr[i] == 'undefined') {
            outPutArr.splice(i, 1);
            continue;
          }
          excludeArr.push(outPutArr[i]);
        }
        yield redisInstance.delKey(key);
        if (outPutArr.length == 0) {
          const arr = [
            100280, 100162, 100093, 100185, 100166, 100271, 100311, 100314, 100334, 100108, 100348, 100337, 100172,
            100175, 100382, 100388, 100265, 100347, 100370, 100391, 100129, 100408, 100133, 100154, 100137, 100197,
            100358, 100430, 100431, 100428, 100308, 100444, 100426, 100452, 100451, 100384, 100463, 100140, 100468,
            100475, 100440, 100460, 100332, 100333, 100435, 100492, 100495, 100402, 100487, 100479, 100509, 100472,
            100511, 100442, 100513,
          ];
          const sarr = self.getRandomArr(arr, 10);
          for (let i = 0; i < sarr.length; i++) {
            outPutArr.push(sarr[i]);
          }
        }
        const rs = yield redisInstance.setAddWithTime(key, outPutArr, 1296000, 1);
      } else {
        if (redisExistedArr.length >= 300) {
          yield redisInstance.delKey(key);
          self.selectUnRedisNew(searchArr, excludeArr, num, userid, outPutInfoArr);
        } else {
          for (let i = 0; i < searchArr.length; i++) {
            const userid = `${searchArr[i].id}`;
            if (excludeArr.indexOf(userid) == -1) {
              if (redisExistedArr.indexOf(userid) == -1) {
                beforeOutPutArr.push(userid);
              } else {
                console.log('redisExistedArr存在>>>>>>>', userid);
              }
            }
          }

          for (let i = 0; i < num; i++) {
            const index = Math.floor(Math.random() * beforeOutPutArr.length);
            const userid = beforeOutPutArr[index];
            if (typeof userid !== 'undefined' && userid !== 'undefined') {
              outPutArr.push(userid);
            }
            beforeOutPutArr.splice(index, 1);
          }
          for (let i = 0; i < outPutArr.length; i++) {
            if (typeof outPutArr[i] !== 'undefined' && outPutArr[i] !== 'undefined') {
              redisExistedArr.push(outPutArr[i]);
            }
          }
          for (let j = 0; j < redisExistedArr.length; j++) {
            excludeArr.push(redisExistedArr[j]);
          }
          const time = yield redisInstance.ttl(key);
          yield redisInstance.delKey(key);
          yield redisInstance.setAddWithTime(key, redisExistedArr, time, 2);
        }
      }
      for (let i = 0; i < searchArr.length; i++) {
        const userid = `${searchArr[i]['id']}`;
        if (outPutArr.includes(userid)) {
          outPutInfoArr.push(searchArr[i]);
        }
      }
      return [outPutInfoArr, excludeArr];
    });
  }

  arrToSet(arr) {
    let newSet = new Set();
    for (let i = 0; i < arr.length; i++) {
      newSet.add(arr[i]);
    }
    const newArr = Array.from(newSet);
    return newArr;
  }

  updateUserInfo(args) {
    const self = this;
    return co(function* () {
      const rs = yield mysqlInstance.getUserInfoByUserid(args['userid']);
      if (rs && rs['type'] === 'error') {
        elogger.error('User Error 9001 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 9001',
        };
      }
      if (!rs.length) {
        return {
          backSuccess: false,
          msg: 'User Error 9002 该用户不存在',
        };
      }
      const updateRs = yield mysqlInstance.updateUserInfo(args);
      if (updateRs && updateRs['type'] === 'error') {
        elogger.error('User Error 9003 ' + updateRs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 9003',
        };
      }
      // const locationArr = ['太平洋', '大西洋', '北冰洋'];
      // const index = Math.floor((Math.random() * locationArr.length));
      // const location = locationArr[index];
      const country = rs[0].country || '';
      const province = rs[0].province || '';
      const city = rs[0].city || '';
      // const country = country;
      // const province = province;
      // const city = city;
      const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + args['userid'], [
        'userid',
        args['userid'],
        'nickname',
        args['nickname'],
        'avatar',
        args['avatar'],
        'signature',
        args['signature'],
        'birth',
        args['birth'],
        'rank',
        rs[0]['rank'],
        'gender',
        args['gender'],
        'country',
        country,
        'province',
        province,
        'city',
        city,
        'hertz',
        rs[0]['hertz'],
      ]);
      if (redisUserInfo && redisUserInfo['type'] === 'error') {
        elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${args['userid']}`);
      }
      const userInfoRs = yield self.getUserInfo(args['userid']);
      const userInfo = userInfoRs['data'];
      const accountImportData = {
        userid: `${args['userid']}`,
        nickName: userInfo[0]['nickname'],
        avatar: userInfo[0]['avatar'],
      };
      // const accountImportRs = yield imService.accountImport(accountImportData);
      // if (accountImportRs['backSuccess']) {
      //   yield mysqlInstance.updateUserStatus(args['userid'], 0);
      // } else {
      //   yield mysqlInstance.updateUserStatus(args['userid'], 1);
      // }
      yield mysqlInstance.updateUserStatus(args['userid'], 0);
      // if (parseInt(args['userid']) === 7) {
      //   yield self.updateWebRedis(args['userid'], args['nickname'], args['avatar'], args['gender']);
      // }

      return {
        backSuccess: true,
        data: userInfo,
      };
    });
  }

  getFansAttentionContentCount(userid) {
    return co(function* () {
      const data = {};
      //获取粉丝数
      const fansKey = 'ZSET:USER:FANS:' + userid;
      const fansNum = yield redisInstance.zsetScoreGet(fansKey);

      //获取关注数
      const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
      const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

      //获取动态数
      const contentKey = 'ZSET:USER:CONTENT:' + userid;
      const contentNum = yield redisInstance.zsetScoreGet(contentKey);

      data['fansNum'] = fansNum.toString();
      data['attentionNum'] = attentionNum.toString();
      data['contentNum'] = contentNum.toString();
      const arr = [];
      arr.push(data);
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  updateUserDeviceToken(args) {
    return co(function* () {
      const device_token = args['deviceToken'];
      const userid = args['userid'];
      const result = yield mysqlInstance.updateUserDeviceToken(userid, device_token);
      if (result && result['type'] === 'error') {
        elogger.error('User Error 10001 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 10001',
        };
      }
      const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, ['deviceToken', device_token]);
      if (redisUserInfo && redisUserInfo['type'] === 'error') {
        elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${args['userid']}`);
      }
      return {
        backSuccess: true,
        data: '',
      };
    });
  }

  removeUserPushCount(userid) {
    return co(function* () {
      yield redisInstance.onlySet(`STR:USER:PUSH:COUNT:${userid}`, '0');
      yield mysqlInstance.updateAllNotificeation(userid);
      return;
    });
  }

  getReportResult(userid) {
    return co(function* () {
      const reportRseult = yield mysqlInstance.getUserReportCount(userid);
      if (reportRseult.length) {
        const count = reportRseult[0].count;
        if (count > 3) {
          return {
            backSuccess: true,
            data: [
              {
                reportRseult: '1',
              },
            ],
          };
        }
      }
      return {
        backSuccess: true,
        data: [
          {
            reportRseult: '0',
          },
        ],
      };
    });
  }

  getBlackResult(userid, to_userid) {
    return co(function* () {
      const rs = yield mysqlInstance.getUserBlackRs(userid, to_userid);
      if (rs && rs['type'] === 'error') {
        elogger.error('User Error 60021 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 60021',
        };
      }
      if (rs.length) {
        return {
          backSuccess: true,
          data: [
            {
              blackRseult: '1',
            },
          ],
        };
      }
      const rs_ = yield mysqlInstance.getUserBlackRs(to_userid, userid);
      if (rs_ && rs_['type'] === 'error') {
        elogger.error('User Error 60022 ' + rs_['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 60022',
        };
      }
      if (rs_.length) {
        return {
          backSuccess: true,
          data: [
            {
              blackRseult: '1',
            },
          ],
        };
      }
      return {
        backSuccess: true,
        data: [
          {
            blackRseult: '0',
          },
        ],
      };
    });
  }

  getRandomArr(arr, num) {
    const len = arr.length;
    for (var i = len - 1; i >= 0; i--) {
      var randomIndex = Math.floor(Math.random() * (i + 1));
      var itemIndex = arr[randomIndex];
      arr[randomIndex] = arr[i];
      arr[i] = itemIndex;
    }
    const tmpArr = arr;
    const arrList = [];
    for (let i = 0; i < num; i++) {
      arrList.push(tmpArr[i]);
    }
    return arrList;
  }

  postUserInvitationCode(userid) {
    const self = this;
    return co(function* () {
      const userinfo = yield mysqlInstance.getUserInfoByUserid(userid);
      if (!userinfo.length) {
        return {
          backSuccess: false,
          msg: 'User Error 70024',
        };
      }
      const user_create_time = userinfo[0].create_time;
      const create_time = parseInt(new Date(user_create_time * 1000).setHours(0, 0, 0, 0) / 1000);
      const now = parseInt(new Date().setHours(0, 0, 0, 0) / 1000);
      const day = 7 - (now - create_time) / (60 * 60 * 24) >= 0 ? 7 - (now - create_time) / (60 * 60 * 24) : 0;
      if (day > 0) {
        return {
          backSuccess: false,
          msg: '注册时间不足7天，无法生成邀请码',
        };
      }
      // if (now - user_create_time < 60 * 60 * 24 * 7) {
      //     return {
      //         backSuccess: false,
      //         msg: '注册时间不足7天，无法生成邀请码'
      //     }
      // }
      const rs2 = yield mysqlInstance.getUserInvitationCodeV2(userid);
      if (rs2 && rs2['type'] === 'error') {
        elogger.error('User Error 70021 ' + rs2['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 70021',
        };
      }
      if (rs2.length) {
        return {
          backSuccess: false,
          msg: '您已设置邀请码',
        };
      }
      const code = yield self.getInvitCode();
      // const rs = yield mysqlInstance.getUserInvitationCode(code);
      // if (rs && rs['type'] === 'error') {
      //     elogger.error('User Error 70021 ' + rs['msg']);
      //     return {
      //         backSuccess: false,
      //         msg: 'User Error 70021'
      //     }
      // }
      // if (rs.length) {
      //     return {
      //         backSuccess: false,
      //         msg: '该邀请码被占用，请重新设置'
      //     }
      // }
      const args = {
        userid: userid,
        code: code,
      };
      const addRs = yield mysqlInstance.postUserInvitationCode(args);
      if (addRs && addRs['type'] === 'error') {
        elogger.error('User Error 70022 ' + addRs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 70022',
        };
      }
      const key = 'SET:ALL:INVIT:CODE';
      yield redisInstance.setAdd(key, args['code']);
      return {
        backSuccess: true,
        data: [
          {
            code: code,
          },
        ],
      };
    });
  }

  userVerify(phone) {
    const self = this;
    return co(function* () {
      const userRs = yield mysqlInstance.getUserByMobile(phone);
      if (!userRs.length) {
        return {
          backSuccess: true,
        };
      }
      const rs = yield mysqlInstance.userVerify(phone);
      if (rs && rs['type'] === 'error') {
        elogger.error('User Error 70022 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'User Error 70023',
        };
      }
      return {
        backSuccess: true,
        verify: {
          verify: Boolean(rs[0].verify),
        },
      };
    });
  }
  userVerifyQA(phone) {
    const self = this;
    return co(function* () {
      const user = yield mysqlInstance.getUserInfoByMobileV2(phone);
      if (!user.length) {
        return {
          backSuccess: false,
          msg: '不存在该用户',
        };
      }
      const userid = user[0].id;
      const user_verify = yield mysqlInstance.getUserVerify(userid);
      if (!user_verify.length) {
        const arr = [];
        const userRs = yield mysqlInstance.userInfoVerifyQA(phone);
        if (userRs && userRs['type'] === 'error') {
          elogger.error('User Error 70022 ' + userRs['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 70023',
          };
        }

        const nicknameBody = {
          qid: 100,
          type: 1, // 文字
          question: 'Q: 请选择账号的正确昵称',
          answer: [],
        };

        const genderBody = {
          qid: 200,
          type: 1, // 文字
          question: 'Q: 请选择账号的正确性别',
          answer: ['男', '女'],
        };

        const avatarBody = {
          qid: 300,
          type: 2, // 图片
          question: 'Q: 请选择账号的正确头像',
          answer: [],
        };

        for (let i = 0; i < userRs.length; i++) {
          const { nickname, avatar, gender, mobile } = userRs[i];
          nicknameBody.answer.push(nickname);
          avatarBody.answer.push(WAILIAN + avatar);
          if (mobile === phone) {
            nicknameBody['atrue'] = nickname;
            genderBody['atrue'] = gender === 1 ? '男' : '女';
            avatarBody['atrue'] = WAILIAN + avatar;
          }
        }
        arr.push(nicknameBody, genderBody, avatarBody);
        yield mysqlInstance.addUserVerify(userid, JSON.stringify(arr));
        const aarr = [];
        for (let i = 0; i < arr.length; i++) {
          arr[i].answer.sort(function (a, b) {
            return Math.random() - 0.5;
          });
          const index = arr[i].answer.indexOf(arr[i].atrue);
          aarr.push(arr[i].qid + index);
          delete arr[i].atrue;
        }
        yield mysqlInstance.updateUserVerifyAnwser(userid, aarr.toString());
        return {
          backSuccess: true,
          data: arr,
        };
      } else {
        if (user_verify[0].frequency >= 3) {
          return {
            backSuccess: false,
            msg: '验证次数超过三次, 请添加客服微信',
          };
        }
        const arr = JSON.parse(user_verify[0].question);
        const aarr = [];
        for (let i = 0; i < arr.length; i++) {
          arr[i].answer.sort(function (a, b) {
            return Math.random() - 0.5;
          });
          const index = arr[i].answer.indexOf(arr[i].atrue);
          aarr.push(arr[i].qid + index);
          delete arr[i].atrue;
        }
        yield mysqlInstance.updateUserVerifyAnwser(userid, aarr.toString());
        return {
          backSuccess: true,
          data: arr,
        };
      }
    });
  }

  postUserVerifyQA(phone, qa) {
    const self = this;
    return co(function* () {
      qa = qa.split(',');
      const user = yield mysqlInstance.getUserInfoByMobileV2(phone);
      if (!user.length) {
        return {
          backSuccess: false,
          msg: '不存在该用户',
        };
      }
      const userid = user[0].id;
      const user_verify = yield mysqlInstance.getUserVerify(userid);
      if (user_verify[0].frequency >= 3) {
        return {
          backSuccess: false,
          msg: '失败次数超过三次, 请联系官方邮箱: <EMAIL>',
        };
      }
      const anwser = user_verify[0].anwser.split(',');
      if (self.isArrEqual(qa, anwser)) {
        const rs = yield mysqlInstance.updateUserVerify(userid);
        if (rs && rs['type'] === 'error') {
          elogger.error('User Error 70022 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 70023',
          };
        }

        const time = parseInt(Math.round(new Date().getTime() / 1000));
        const msg = `我们回来了，你也回来了。这四年，世界变了好多，感谢你一直在心里留着 52Hz 的位置, 希望这里还留着你想念的声音。`;

        const rs2 = yield mysqlInstance.addLiuyan(108474, userid, msg, time);
        if (rs2 && rs2['type'] === 'error') {
          elogger.error('welcome Error 1002 ' + rs2['sql']);
          return {
            backSuccess: false,
            msg: 'welcome Error 1002',
          };
        }

        return {
          backSuccess: true,
          message: '验证成功, 请重新登录',
        };
      } else {
        const rs = yield mysqlInstance.updateUserVerifyFrequency(userid);
        if (rs && rs['type'] === 'error') {
          elogger.error('User Error 70022 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'User Error 70023',
          };
        }
        return {
          backSuccess: true,
          message: '验证失败, 请重新验证',
        };
      }
    });
  }

  setAccount(userid, account) {
    return co(function* () {
      const accountRs = yield mysqlInstance.getAccountByAccount(account);
      if (accountRs.length) {
        return {
          backSuccess: false,
          msg: '账号已存在',
        };
      }
      const accountRs_ = yield mysqlInstance.getAccountByUserid(userid);
      if (accountRs_.length) {
        const updateTime = accountRs_[0].updated;
        const now = Math.floor(Date.now() / 1000);
        const oneMonth = 30 * 24 * 60 * 60;

        if (now - updateTime < oneMonth) {
          return {
            backSuccess: false,
            msg: '距离上次修改未满一个月，请稍后再试',
          };
        }
        yield mysqlInstance.setAccount(userid, account);
      } else {
        yield mysqlInstance.addAccount(userid, account);
      }
      return {
        backSuccess: true,
      };
    });
  }

  getPasswordStatus(userid) {
    return co(function* () {
      const accountRs = yield mysqlInstance.getAccountByUserid(userid);
      if (!accountRs.length) {
        return {
          backSuccess: true,
          code: 3500,
          msg: '账号不存在',
        };
      }
      if (!accountRs[0].password) {
        return {
          backSuccess: true,
          code: 3501,
          msg: '请设置密码',
        };
      }
      return {
        code: 0,
        backSuccess: true,
        msg: 'success',
      };
    });
  }

  setPassword(userid, newPassword, oldPassword) {
    return co(function* () {
      const accountRs = yield mysqlInstance.getAccountByUserid(userid);
      if (!accountRs.length) {
        return {
          backSuccess: false,
          msg: '账号不存在',
        };
      }
      if (oldPassword) {
        const { hash } = yield pwd.hash(oldPassword, accountRs[0].salt);
        if (hash !== accountRs[0].password) {
          return {
            backSuccess: false,
            msg: '密码不正确',
          };
        }
      } else {
        if (accountRs[0].password) {
          return {
            backSuccess: false,
            msg: '请输入原始密码',
          };
        }
      }

      const newPwd = yield pwd.hash(newPassword);

      yield mysqlInstance.setPassword(userid, newPwd.hash, newPwd.salt);
      return {
        backSuccess: true,
      };
    });
  }

  isUserOnline(userid) {
    return co(function* () {
      const result = yield redisInstance.isUserOnline(userid);
      return {
        backSuccess: true,
        status: result,
      };
    });
  }

  getForgetMsgCode(userid) {
    const self = this;
    return co(function* () {
      const userRs = yield mysqlInstance.getUserInfoByUserid(userid);
      const mobile = userRs[0].mobile;
      const codeResult = yield redisInstance.getVerifyCode(mobile);
      if (codeResult[0]) {
        console.log(codeResult[0]);
        const result = yield self.sendCodeBase(mobile, codeResult[0]);
        if (result) {
          return {
            backSuccess: true,
          };
        }
      } else {
        const code = self.getRandomCode();
        console.log(code);
        const key = 'HASH:CODE:' + mobile;
        yield redisInstance.delKey(key);
        yield redisInstance.setVerifyCode(mobile, code, 60 * 15);
        const result = yield self.sendCodeBase(mobile, code);
        if (result) {
          return {
            backSuccess: true,
          };
        }
      }
      return {
        backSuccess: false,
      };
    });
  }

  sendCodeBase(mobile, code) {
    return new Promise(function (resolve, reject) {
      //发送短信
      smsClient
        .sendSMS({
          PhoneNumbers: mobile,
          SignName: '博味',
          TemplateCode: 'SMS_121857568', // 'SMS_149097393', //SMS_166080190
          TemplateParam: '{"code":"' + code + '"}',
        })
        .then(
          function (res) {
            const { Code } = res;
            if (Code === 'OK') {
              //处理返回参数
              // elogger.debug(res);
              resolve(true);
            }
          },
          function (err) {
            elogger.error(`${mobile} sendCodeBase ERROR : ${err}`);
            // resolve(false);
            resolve(true); // TODO 记得改回来
          },
        );
    });
  }

  validateCode(userid, code) {
    return co(function* () {
      const userRs = yield mysqlInstance.getUserInfoByUserid(userid);
      const codeResult = yield redisInstance.getVerifyCode(userRs[0].mobile);
      if (codeResult[0] && codeResult[0] == code) {
        yield mysqlInstance.setPassword(userid, null, null);
        return {
          backSuccess: true,
          data: [],
        };
      } else {
        elogger.error(`验证码出错 : 手机 : ${args['mobile']} : 接收 : ${code} : 本地 : ${codeResult[0]}`);
        return {
          backSuccess: false,
          msg: '验证码出错',
        };
      }
    });
  }

  getUserAFL(userid) {
    return co(function* () {
      const data = {};
      const fansKey = 'ZSET:USER:FANS:' + userid;
      const fansNum = yield redisInstance.zsetScoreGet(fansKey);

      const attentionKey = 'ZSET:USER:ATTENTION:' + userid;
      const attentionNum = yield redisInstance.zsetScoreGet(attentionKey);

      data['fansNum'] = fansNum.toString();
      data['attentionNum'] = attentionNum.toString();

      const likeCountRs = yield mysqlInstance.getUserLikeCount(userid);
      data['likeNum'] = likeCountRs.length ? `${likeCountRs[0].count}` : '0';

      return {
        backSuccess: true,
        data: data,
      };
    });
  }

  clearUserNotice(userid) {
    return co(function* () {
      yield mysqlInstance.clearUserNotice(userid);
      return {
        backSuccess: true,
      };
    });
  }

  coCreation() {
    return co(function* () {
      const rs = yield mysqlInstance.getCoCreation();
      const typeDesc = {
        1: '• 开发',
        2: '• 设计',
        3: '• 共创',
      };

      const groupedData = rs.reduce((acc, item) => {
        if (!acc.find((g) => g.type === item.type)) {
          acc.push({
            type: item.type,
            desc: typeDesc[item.type],
            users: [],
          });
        }

        const group = acc.find((g) => g.type === item.type);
        group.users.push({
          userid: item.userid,
          nickname: item.nickname,
          avatar: WAILIAN + item.avatar,
          desc: item.descs,
        });

        return acc;
      }, []);

      return {
        backSuccess: true,
        data: groupedData,
      };
    });
  }

  isArrEqual(arr1, arr2) {
    return arr1.length === arr2.length && arr1.every((ele) => arr2.includes(ele));
  }

  /**
   * 推荐用户接口：协同过滤+热门用户补充
   * @param {Object} args {userid, limit}
   * @returns 推荐用户列表
   */
  getSearchRecommenNew(args) {
    return co(function* () {
      const userid = args['userid'];
      const limit = args['limit'] || 10;
      // 1. 获取当前用户最近N条搜索内容
      const searchHistory = yield mysqlInstance.getUserSearchHistory(userid, 10);
      if (!searchHistory || !searchHistory.length) {
        // 没有搜索历史，直接返回热门用户
        const hotUsers = yield mysqlInstance.getHotUsers(limit);
        const hotUsersSimple = hotUsers.map((u) => ({
          id: u.id,
          nickname: u.nickname,
          avatar: `${WAILIAN}${u.avatar}`,
        }));
        return { backSuccess: true, data: hotUsersSimple };
      }
      // 2. 找到也搜索过这些内容的其他用户
      const similarUsers = yield mysqlInstance.getUsersBySearchContents(searchHistory, userid);
      if (!similarUsers || !similarUsers.length) {
        // 没有相似用户，直接返回热门用户
        const hotUsers = yield mysqlInstance.getHotUsers(limit);
        const hotUsersSimple = hotUsers.map((u) => ({
          id: u.id,
          nickname: u.nickname,
          avatar: `${WAILIAN}${u.avatar}`,
        }));
        return { backSuccess: true, data: hotUsersSimple };
      }
      const similarUserIds = similarUsers.map((u) => u.userid);
      // 3. 获取这些用户关注的用户
      const attentionList = yield mysqlInstance.getAttentionsByUserids(similarUserIds);
      if (!attentionList || !attentionList.length) {
        // 没有关注数据，直接返回热门用户
        const hotUsers = yield mysqlInstance.getHotUsers(limit);
        const hotUsersSimple = hotUsers.map((u) => ({
          id: u.id,
          nickname: u.nickname,
          avatar: `${WAILIAN}${u.avatar}`,
        }));
        return { backSuccess: true, data: hotUsersSimple };
      }
      // 4. 获取当前用户已关注的用户，去重
      const myAttentions = yield mysqlInstance.getAttentionsByUserid(userid);
      const myAttentionSet = new Set(myAttentions.map((a) => a.userid2));
      myAttentionSet.add(Number(userid)); // 不推荐自己
      // 5. 统计被关注用户出现频次
      const freqMap = {};
      for (const att of attentionList) {
        const uid = att.userid2;
        if (myAttentionSet.has(uid)) continue;
        freqMap[uid] = (freqMap[uid] || 0) + 1;
      }
      // 6. 排序，取前N
      const sortedUserIds = Object.entries(freqMap)
        .sort((a, b) => b[1] - a[1])
        .map(([uid]) => uid)
        .slice(0, limit);
      let recommendUsers = [];
      if (sortedUserIds.length) {
        // 查询用户信息
        const userSql = `SELECT id, nickname, avatar FROM user WHERE id IN (${sortedUserIds.join(',')})`;
        recommendUsers = yield mysqlInstance.query(userSql);
      }
      // 7. 不足补热门用户
      if (recommendUsers.length < limit) {
        const hotUsers = yield mysqlInstance.getHotUsers(limit);
        const existIds = new Set(recommendUsers.map((u) => u.id));
        for (const u of hotUsers) {
          if (!existIds.has(u.id) && !myAttentionSet.has(u.id)) {
            recommendUsers.push({ id: u.id, nickname: u.nickname, avatar: u.avatar });
            existIds.add(u.id);
          }
          if (recommendUsers.length >= limit) break;
        }
      }
      // 最终只返回三列
      recommendUsers = recommendUsers.map((u) => ({ id: u.id, nickname: u.nickname, avatar: `${WAILIAN}${u.avatar}` }));
      return { backSuccess: true, data: recommendUsers };
    });
  }

  getUnreadStatus(userid) {
    return co(function* () {
      const liuyan = yield mysqlInstance.getLiuyanUnreadCount(userid);
      const notion = yield mysqlInstance.getUnreadNotification(userid);
      const data = {
        liuyan: Boolean(liuyan[0].count > 0),
        notion: Boolean(notion[0].count > 0),
      };
      return { backSuccess: true, data };
    });
  }
}

module.exports.userInstance = new AnchorController();
