'use strict';
const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const request = require('request');
const URL = 'http://172.16.241.103:10032/hertz/rpc';
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const IMAEWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const TAG = require('../config/tagConfig').tag;
const Attribute = require('../config/tagConfig').attribute;

class RecommenNewController extends BaseController {
  constructor(props) {
    super(props);
  }
  getRecommenNew(userid, TODAYTIME) {
    const self = this;
    return co(function* () {
      // 获取当天是否推荐过
      const recommenRs = yield mysqlInstance.getRecommenByTimeAndUserNewV2(userid, TODAYTIME);
      if (recommenRs.length) {
        const recommenArr = [];
        for (let i = 0; i < recommenRs.length; i++) {
          recommenArr.push(recommenRs[i].recommenid);
        }
        const recommen = yield formatRecommen(recommenArr);
        for (let i = 0; i < recommen.length; i++) {
          const user = recommen[i]['user'];
          const colorFont = yield self.getColorFont(user.userid);
          recommen[i]['user']['colorFont'] = colorFont;
        }
        return {
          backSuccess: true,
          data: recommen,
        };
      } else {
        // 判断新老用户
        const user = yield mysqlInstance.getUserOldNew(userid);
        let recommenRs = yield recommenFun(user[0].old_new, userid, TODAYTIME);
        if (recommenRs['backSuccess']) {
          const recommenArr = [];
          recommenRs = recommenRs['data'];
          for (let i = 0; i < recommenRs.length; i++) {
            recommenArr.push(recommenRs[i]);
          }
          const recommen = yield formatRecommen(recommenArr);
          for (let i = 0; i < recommen.length; i++) {
            const user = recommen[i]['user'];
            const colorFont = yield self.getColorFont(user.userid);
            recommen[i]['user']['colorFont'] = colorFont;
          }

          return {
            backSuccess: true,
            data: recommen,
          };
        } else {
          return {
            backSuccess: true,
            msg: recommenRs['msg'],
          };
        }
      }
    });
  }
}

async function recommenFun(type, userid, TODAYTIME) {
  const self = this;
  // 获取关注拉黑
  let paichuUidArr = [];
  const black = await mysqlInstance.getUserBlackFun2(userid);
  for (let i = 0; i < black.length; i++) {
    const black_userid = parseInt(black[i].black_userid);
    paichuUidArr.push(black_userid);
  }
  const attention = await mysqlInstance.searcUserAttentionUserid(userid);
  for (let i = 0; i < attention.length; i++) {
    const userid = parseInt(attention[i].userid2);
    paichuUidArr.push(userid);
  }
  paichuUidArr.push(userid);
  paichuUidArr = arrToSet(paichuUidArr);
  // 获取最近一个月以推荐过的id
  let paichuCidArr = [];
  const recommendedRs = await mysqlInstance.getRecommendedIdV2(userid, TODAYTIME - 60 * 60 * 24 * 30);
  for (let i = 0; i < recommendedRs.length; i++) {
    const recommenid = parseInt(recommendedRs[i].recommenid);
    paichuCidArr.push(recommenid);
  }
  paichuCidArr = arrToSet(paichuCidArr);
  // type: 1:old 2:new
  if (type === 1) {
    /**
     *【 80 % 】“距离”近用户的最近内容
     * “距离”近的定义：将用户最近发布的10条内容（不满10条，就有几条搜几条），一条条进行搜索，搜索结果按相似分数排完，综合取前10*50条，筛选最近15天内发布的加入推荐内容。
     */
    const userContent = await mysqlInstance.getUserTenContent(userid);
    const userContentArr = [];
    for (let i = 0; i < userContent.length; i++) {
      const content = userContent[i].content;
      userContentArr.push(content);
    }
    let zoeArr = [];
    for (let i = 0; i < userContentArr.length; i++) {
      const content = userContentArr[i];
      const ZoeRs = await createZoeRequest(content);
      if (ZoeRs && ZoeRs['type'] === 'error') {
        elogger.error('Content Error 800311 ' + ZoeRs['msg']);
        return {
          backSuccess: false,
          msg: 'New Recommen Error 10121',
        };
      }
      for (let i = 0; i < ZoeRs.length; i++) {
        if (i >= 50) {
          break;
        }
        const neighbor_id = parseInt(ZoeRs[i].id);
        zoeArr.push(neighbor_id);
      }
    }
    zoeArr = arrToSet(zoeArr);
    zoeArr = arrayMinus(zoeArr, paichuCidArr);
    const EightyPercentRecommenRs = await mysqlInstance.getRecommenContent(zoeArr, paichuUidArr);
    let EightyPercentArr = [];
    for (let i = 0; i < EightyPercentRecommenRs.length; i++) {
      const contentid = EightyPercentRecommenRs[i].id;
      EightyPercentArr.push(contentid);
    }
    EightyPercentArr = EightyPercentArr.slice(0, 7);
    /**
     *【 20 % 】随机的最近内容
     * 随机
     */
    const newContent = await mysqlInstance.getTwoNewContent(paichuUidArr);
    for (let i = 0; i < newContent.length; i++) {
      const contentid = newContent[i].id;
      EightyPercentArr.push(contentid);
    }
    EightyPercentArr = EightyPercentArr.slice(0, 9);
    for (let i = 0; i < EightyPercentArr.length; i++) {
      await mysqlInstance.addRecommenV2(userid, EightyPercentArr[i]);
    }
    return {
      backSuccess: true,
      data: EightyPercentArr,
    };
  } else if (type === 0) {
    /**
     *【 50% 】 最近的好内容
     * 最近的定义【以下都是】：发布时间在15天之内
     * 好内容的定义：点赞+评论>10
     */
    const goodContentRs = await mysqlInstance.getGoodContent(TODAYTIME - 60 * 60 * 24 * 15);
    let goodContentArr = [];
    for (let i = 0; i < goodContentRs.length; i++) {
      goodContentArr.push(goodContentRs[i].id);
    }
    goodContentArr = arrayMinus(goodContentArr, paichuCidArr);
    goodContentArr = goodContentArr.slice(0, 5);
    /**
     *【 25% 】 最近的感兴趣的人的内容
     * 可能感兴趣的人定义：频率区间为同一区间 或 标签有3个以上符合用户选择想认识的人的标签：
     */
    const userInfo = await mysqlInstance.getUserInfo(userid);
    const userLikeTag = userInfo[0].likeTagArr;
    const attribute = userInfo[0].attribute;
    const minHertz = Attribute[attribute]['interval'][0];
    const maxHertz = Attribute[attribute]['interval'][1];
    let userArr = [];
    const likePeopleRs = await mysqlInstance.getHertzLikePeople(minHertz, maxHertz);
    for (let i = 0; i < likePeopleRs.length; i++) {
      const userid = likePeopleRs[i].id;
      userArr.push(userid);
    }
    // const likeTagRs = await mysqlInstance.getTagLikePeople(userLikeTag)
    userArr = arrayMinus(userArr, paichuUidArr);
    userArr = arrToSet(userArr);
    const likeContentRs = await mysqlInstance.getLikeContent(userArr, paichuUidArr);
    for (let i = 0; i < likeContentRs.length; i++) {
      goodContentArr.push(likeContentRs[i].id);
    }
    goodContentArr = arrToSet(goodContentArr);
    goodContentArr = goodContentArr.slice(0, 7);
    /**
     *【 25% 】“距离”近用户的最近内容
     * “距离”近的定义：将用户最近发布的10条内容（不满10条，就有几条搜几条），一条条进行搜索，搜索结果按相似分数排完，综合取前10*50条，筛选最近15天内发布的加入推荐内容。
     */
    const userContent = await mysqlInstance.getUserTenContent(userid);
    const userContentArr = [];
    for (let i = 0; i < userContent.length; i++) {
      const content = userContent[i].content;
      userContentArr.push(content);
    }
    let zoeArr = [];
    for (let i = 0; i < userContentArr.length; i++) {
      const content = userContentArr[i];
      const ZoeRs = await createZoeRequest(content);
      if (ZoeRs && ZoeRs['type'] === 'error') {
        elogger.error('Content Error 800311 ' + ZoeRs['msg']);
        return {
          backSuccess: false,
          msg: 'New Recommen Error 10121',
        };
      }
      for (let i = 0; i < ZoeRs.length; i++) {
        if (i >= 50) {
          break;
        }
        const neighbor_id = parseInt(ZoeRs[i].id);
        zoeArr.push(neighbor_id);
      }
    }
    zoeArr = arrToSet(zoeArr);
    zoeArr = arrayMinus(zoeArr, paichuCidArr);
    const EightyPercentRecommenRs = await mysqlInstance.getRecommenContent(zoeArr, paichuUidArr);
    for (let i = 0; i < EightyPercentRecommenRs.length; i++) {
      const contentid = EightyPercentRecommenRs[i].id;
      goodContentArr.push(contentid);
    }
    goodContentArr = arrToSet(goodContentArr);
    goodContentArr = goodContentArr.slice(0, 9);
    for (let i = 0; i < goodContentArr.length; i++) {
      await mysqlInstance.addRecommenV2(userid, goodContentArr[i]);
    }
    return {
      backSuccess: true,
      data: goodContentArr,
    };
  }
}

function createZoeRequest(content) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'POST',
      url: URL,
      headers: {
        'cache-control': 'no-cache',
        'Content-Type': 'application/json',
      },
      body: {
        function: 'searchByContents',
        params: {
          query_row: {
            content: content,
          },
          modes: ['semantic'],
          size: 20,
        },
      },
      json: true,
    };
    request(options, function (error, response, body) {
      if (error) {
        resolve({
          result: false,
        });
      }
      if (body.code === 200 && body.result.length && body.result[0].id) {
        resolve(body.result);
      } else {
        resolve({
          result: false,
        });
      }
    });
  });
}

/**
 *
 * @param {
 * contentid,
 * authorid,
 * recommen_type
 * } args
 */
async function formatRecommen(recommenArr) {
  const self = this;
  const RS = await mysqlInstance.getRecommenContentByInV2(recommenArr);
  const result = [];
  for (let i = 0; i < RS.length; i++) {
    const accordContent = '昨日最热';
    const user = {
      gender: RS[i].gender,
      userid: RS[i].userid,
      avatar: WAILIAN + RS[i].avatar,
      nickname: RS[i].nickname,
      signature: RS[i].signature.length > 20 ? `${RS[i].signature.slice(0, 20)}...` : RS[i].signature,
      hertz: `${returnFloat(RS[i].hertz)}`,
    };
    const content = {
      accordContent: accordContent,
      accordContentId: 1,
      contentid: RS[i].contentid,
      content: parseInt(RS[i].status) === 1 ? '“阿哦，这条动态已被删除”' : RS[i].content === '' ? null : RS[i].content,
      images: null,
      musicInfo: null,
    };
    const tagArrStr = RS[i]['tagArr'];
    let tagArr = tagArrStr.split(',');
    tagArr = arrToSet(tagArr);
    let tagInfoArr = [];
    let paoTagInfoArr = [];
    for (let i = 0; i < tagArr.length; i++) {
      const num = tagArr[i];
      const tagInfo = TAG[num];
      if (tagInfo['type'] == 0) {
        paoTagInfoArr.push(tagInfo);
      } else {
        tagInfoArr.push(tagInfo);
      }
    }
    user['tag'] = tagInfoArr;
    if (RS[i].type == 1) {
      let imageArrStr = '';
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
    } else if (RS[i].type == 2) {
      let imageArrStr = '';
      const musicContent = RS[i].musicContent;
      const musicContentData = JSON.parse(musicContent);
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
      content['musicInfo'] = musicContentData;
    }
    // 1:音乐 2:图文 3:纯文字 4:随机
    content['iconType'] = 3;
    if (content['images']) {
      content['iconType'] = 2;
    }
    if (content['musicInfo']) {
      content['iconType'] = 1;
    }
    if (parseInt(RS[i].status) === 1) {
      content['images'] = '';
      content['musicInfo'] = null;
    }
    result.push({
      user: user,
      content: content,
    });
  }
  return result;
}

function arrToSet(arr) {
  let newSet = new Set();
  for (let i = 0; i < arr.length; i++) {
    newSet.add(arr[i]);
  }
  const newArr = Array.from(newSet);
  return newArr;
}

function arrayMinus(arr1, arr2) {
  var result = [];
  arr1.forEach(function (x) {
    if (arr2.indexOf(x) === -1) {
      result.push(x);
    } else {
      return;
    }
  });
  return result;
}

function returnFloat(value) {
  const s = value.toString().split('.');
  if (s.length == 1) {
    value = value.toString() + '.00';
    return value;
  }
  if (s.length > 1) {
    if (s[1].length < 2) {
      value = value.toString() + '0';
    }
    return value;
  }
}

module.exports.recommenNewInstance = new RecommenNewController();
