'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const TAG = require('../config/tagConfig').tag;
const Attribute = require('../config/tagConfig').attribute;
const { DefaultConfig } = require('../config/default');
const AVATAR_WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;

class shudongController extends BaseController {
  constructor(props) {
    super(props);
  }

  getList(page, size) {
    const self = this;
    return co(function* () {
      const rs = yield mysqlInstance.getShuDongList(page, size);
      const arr = [];
      for (let i = 0; i < rs.length; i++) {
        const data = {
          sid: rs[i].id,
          message: rs[i].msg,
          created: rs[i].created,
          userid: rs[i].userid,
          gender: rs[i].gender.toString(),
        };
        const user_hertz = rs[i]['hertz'];
        for (let key in Attribute) {
          const att = Attribute[key];
          const interval = att['interval'];
          const min = interval[0];
          const max = interval[1];
          if (user_hertz >= min && user_hertz <= max) {
            data['whale'] = att['whale'];
            data['avatar'] = att['num'];
          }
        }
        arr.push(data);
      }
      const count = yield mysqlInstance.getShuDongCount();
      const data_ = {
        page: `${page}`,
        size: `${size}`,
        count: count[0].count.toString(),
        data: arr,
      };
      return {
        backSuccess: true,
        data: data_,
      };
    });
  }

  addShudong(userid, msg) {
    const self = this;
    return co(function* () {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const rs = yield mysqlInstance.addShudong(userid, msg, time);
      if (rs && rs['type'] === 'error') {
        elogger.error('Island Error 1002 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'shudong Error 1002',
        };
      }
      return {
        backSuccess: true,
        data: [],
      };
    });
  }

  addLiuyan(userid, to, msg) {
    const self = this;
    return co(function* () {
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      if (parseInt(to) === 108474) {
        const rs = yield mysqlInstance.addFankui(userid, to, msg, time);
        if (rs && rs['type'] === 'error') {
          elogger.error('Island Error 1002 ' + rs['sql']);
          return {
            backSuccess: false,
            msg: 'fankui Error 1002',
          };
        }
        const rs2 = yield mysqlInstance.addLiuyan(to, userid, '您的反馈已收到, 我们会尽快处理', time);
        if (rs2 && rs2['type'] === 'error') {
          elogger.error('Island Error 1002 ' + rs2['sql']);
          return {
            backSuccess: false,
            msg: 'fankui Error 1002',
          };
        }
        return {
          backSuccess: true,
          data: [],
        };
      }
      const rs = yield mysqlInstance.addLiuyan(userid, to, msg, time);
      if (rs && rs['type'] === 'error') {
        elogger.error('Island Error 1002 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'liuyan Error 1002',
        };
      }
      return {
        backSuccess: true,
        data: [],
      };
    });
  }

  getLiuyanList(userid, page, size) {
    const self = this;
    return co(function* () {
      let rs = [];
      if (parseInt(userid) === 108474) {
        rs = yield mysqlInstance.getFankuiList(page, size);
      } else {
        rs = yield mysqlInstance.getLiuyanList(userid, page, size);
      }
      const arr = [];
      for (let i = 0; i < rs.length; i++) {
        const data = {
          lid: rs[i].id,
          userid: rs[i].userid,
          gender: rs[i].gender,
          message: rs[i].msg,
          created: rs[i].created,
          nickname: rs[i].nickname,
          avatar: AVATAR_WAILIAN + rs[i].avatar,
          tonickname: rs[i].tonickname,
          toavatar: AVATAR_WAILIAN + rs[i].toavatar,
          status: rs[i].status,
        };
        arr.push(data);
      }
      let count = [];
      let unreadCount = [];
      if (parseInt(userid) === 108474) {
        count = yield mysqlInstance.getFankuiCount();
        unreadCount = yield mysqlInstance.getFankuiUnreadCount();
      } else {
        count = yield mysqlInstance.getLiuyanCount(userid);
        unreadCount = yield mysqlInstance.getLiuyanUnreadCount(userid);
      }
      const data_ = {
        page: `${page}`,
        size: `${size}`,
        count: count[0].count,
        unreadCount: unreadCount[0].count,
        data: arr,
      };
      return {
        backSuccess: true,
        data: data_,
      };
    });
  }

  readLiuyan(userid, lid) {
    const self = this;
    return co(function* () {
      if (parseInt(userid) === 108474) {
        const rs = yield mysqlInstance.getFankui(lid);
        if (rs.length && rs[0].status === 0) {
          yield mysqlInstance.updateFankui(lid);
        }
      } else {
        const rs = yield mysqlInstance.getLiuyan(userid, lid);
        if (rs.length && rs[0].status === 0) {
          yield mysqlInstance.updateLiuyan(lid);
        }
      }

      return {
        backSuccess: true,
      };
    });
  }

  unreadCount(userid) {
    const self = this;
    return co(function* () {
      let rs = [];
      if (parseInt(userid) === 108474) {
        rs = yield mysqlInstance.getFankuiUnreadCount();
      } else {
        rs = yield mysqlInstance.getLiuyanUnreadCount(userid);
      }
      return {
        backSuccess: true,
        data: { unreadCount: rs[0].count },
        // data: { unreadCount: 100 },
      };
    });
  }

  deleteLiuyan(userid, lids) {
    const self = this;
    return co(function* () {
      if (parseInt(userid) === 108474) {
        yield mysqlInstance.deleteFankui(lids);
      } else {
        yield mysqlInstance.deleteLiuyan(userid, lids);
      }
      return {
        backSuccess: true,
      };
    });
  }

  clearUserLiuyan(userid) {
    return co(function* () {
      yield mysqlInstance.clearUserLiuyan(userid);
      return {
        backSuccess: true,
      };
    });
  }
}

module.exports.shudongInstance = new shudongController();
