'use strict';
const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const request = require('request');
const URL = 'http://172.16.241.103:10032/hertz/rpc';
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const IMAEWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const TAG = require('../config/tagConfig').tag;
class RecommenNewController extends BaseController {
  constructor(props) {
    super(props);
  }
  getRecommenNew(userid, TODAYTIME) {
    const self = this;
    return co(function* () {
      // 获取当天是否推荐过
      const recommenRs = yield mysqlInstance.getRecommenByTimeAndUserNew(userid, TODAYTIME);
      if (recommenRs.length) {
        const BODY = [];
        for (let i = 0; i < recommenRs.length; i++) {
          const body = {
            accordid: recommenRs[i].accordid,
            recommen_type: recommenRs[i].accord_type,
            contentid: recommenRs[i].recommenid,
          };
          BODY.push(body);
        }
        const recommen = yield formatRecommen(BODY, userid, TODAYTIME);
        for (let i = 0; i < recommen.length; i++) {
          const user = recommen[i]['user'];
          const colorFont = yield self.getColorFont(user.userid);
          recommen[i]['user']['colorFont'] = colorFont;
        }
        return {
          backSuccess: true,
          data: recommen,
        };
      } else {
        /**
                 * 输入的规则
                    a.选取该用户最近点赞的两条动态（今天用了这两条，明天尽可能就不用这两条了）
                    b.选取用户最近评论的两条动态（一样是尽量不重复使用同一条）
                    c.选取用户最近发布的一条动态（也是尽力不重复）
                    d.随机选取海洋里一条动态
                 */
        const shuruRs = yield shuruFun(userid, TODAYTIME);
        const zoeArr = [];
        for (const key in shuruRs) {
          const value = shuruRs[key];
          for (let i = 0; i < value.length; i++) {
            const data = value[i];
            // const ZoeRs = yield createRequest(data);
            // if (ZoeRs.result) {
            //   const zoeData = ZoeRs.data;
            //   zoeArr.push(zoeData);
            // }
          }
        }
        /**
                 * 输出的规则：
                    a.点赞动态输入后，选排名最高的前3名
                    b.评论动态输入后，选排名最高的前2名
                    c.发布动态输入后，选排名最高的前2名
                    d.随机动态选一条
                   对输出的过滤：  
                    1.过滤拉黑用户的内容
                    2.过滤关注用户的内容
                    3.过滤发布时间超过一个月的内容
                    4.过滤看过的内容（不重复推荐）
                 */
        const shuchuRs = yield shuchuFun(userid, zoeArr, TODAYTIME);
        // 落地 mysql
        // 格式化输出
        const recommen = yield formatRecommen(shuchuRs, userid, TODAYTIME);
        for (let i = 0; i < recommen.length; i++) {
          const user = recommen[i]['user'];
          const colorFont = yield self.getColorFont(user.userid);
          recommen[i]['user']['colorFont'] = colorFont;
        }
        return {
          backSuccess: true,
          data: recommen,
        };
      }
    });
  }
}
/**
 *
 * @param {
 * contentid,
 * authorid,
 * recommen_type
 * } args
 */
async function formatRecommen(BODY, userid, TODAYTIME) {
  const self = this;
  const recommenArr = [];
  for (let i = 0; i < BODY.length; i++) {
    const contentid = BODY[i].contentid;
    recommenArr.push(contentid);
  }
  const RS = await mysqlInstance.getRecommenContentByIn(recommenArr, userid, TODAYTIME);
  const result = [];
  for (let i = 0; i < RS.length; i++) {
    let accordContent = '';
    const accord_type = RS[i].accord_type;
    switch (parseInt(accord_type)) {
      case 1:
        accordContent = '根据你点赞过的动态推荐';
        break;
      case 2:
        accordContent = '根据你评论过的动态推荐';
        break;
      case 3:
        accordContent = '根据你发布过的动态推荐';
        break;
      case 4:
        accordContent = '昨日最热';
        break;
    }
    const user = {
      gender: RS[i].gender,
      userid: RS[i].userid,
      avatar: WAILIAN + RS[i].avatar,
      nickname: RS[i].nickname,
      signature: RS[i].signature.length > 20 ? `${RS[i].signature.slice(0, 20)}...` : RS[i].signature,
      hertz: `${returnFloat(RS[i].hertz)}`,
    };

    const content = {
      accordContent: accordContent,
      accordContentId: RS[i].accordid,
      contentid: RS[i].contentid,
      content: parseInt(RS[i].status) === 1 ? '“阿哦，这条动态已被删除”' : RS[i].content === '' ? null : RS[i].content,
      images: null,
      musicInfo: null,
    };
    const tagArrStr = RS[i]['tagArr'];
    let tagArr = tagArrStr.split(',');
    tagArr = arrToSet(tagArr);
    let tagInfoArr = [];
    let paoTagInfoArr = [];
    for (let i = 0; i < tagArr.length; i++) {
      const num = tagArr[i];
      const tagInfo = TAG[num];
      if (tagInfo['type'] == 0) {
        paoTagInfoArr.push(tagInfo);
      } else {
        tagInfoArr.push(tagInfo);
      }
    }
    user['tag'] = tagInfoArr;
    if (RS[i].type == 1) {
      let imageArrStr = '';
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
    } else if (RS[i].type == 2) {
      let imageArrStr = '';
      const musicContent = RS[i].musicContent;
      const musicContentData = JSON.parse(musicContent);
      // const shareUrl = musicContentData['shareUrl']
      // const musicInfo = await SFMInstance.getMusicInfo(shareUrl)
      // if (musicInfo && musicInfo['type']) {
      //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      // }
      if (RS[i].images) {
        const images = RS[i].images;
        const imagesArr_ = images.split(',');
        for (let z = 0; z < imagesArr_.length; z++) {
          if (z === imagesArr_.length - 1) {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]}`;
          } else {
            imageArrStr += `${IMAEWAILIAN}${imagesArr_[z]},`;
          }
        }
      }
      content['images'] = imageArrStr;
      content['musicInfo'] = musicContentData;
    }
    // 1:音乐 2:图文 3:纯文字 4:随机
    content['iconType'] = 3;
    if (content['images']) {
      content['iconType'] = 2;
    }
    if (content['musicInfo']) {
      content['iconType'] = 1;
    }
    if (accord_type === 4) {
      content['iconType'] = 4;
    }
    if (parseInt(RS[i].status) === 1) {
      content['images'] = '';
      content['musicInfo'] = null;
    }
    result.push({
      user: user,
      content: content,
    });
  }
  return result;
}
/**
* 输入的规则
a.选取该用户最近点赞的两条动态（今天用了这两条，明天尽可能就不用这两条了）
b.选取用户最近评论的两条动态（一样是尽量不重复使用同一条）
c.选取用户最近发布的一条动态（也是尽力不重复）
d.随机选取海洋里一条动态
*/
async function shuruFun(userid, TODAYTIME) {
  // const recommenedRs = await mysqlInstance.getFromRecommenEd(userid, TODAYTIME)
  // for (let i = 0; i < recommenedRs.length; i++) {
  //     const accordid = recommenedRs[i].accordid;
  //     const recommenid = recommenedRs[i].recommenid;
  //     const accord_type = recommenedRs[i].accord_type;
  // }
  const likeArr = [];
  const commentArr = [];
  const contentArr = [];
  const randomArr = [];
  const accordidEdArr = [];
  // 已有accordid
  const accordidEdRs = await mysqlInstance.getContentByRecommenType(userid, TODAYTIME, 1);
  for (let i = 0; i < accordidEdRs.length; i++) {
    const accordid = accordidEdRs[i].accordid;
    if (accordid) {
      accordidEdArr.push(accordid);
    }
  }
  // 根据点赞
  // const likeTypeRs = await mysqlInstance.getContentByRecommenType(userid, TODAYTIME, 1);
  // const likedArr = [];
  // for (let i = 0; i < likeTypeRs.length; i++) {
  //     const accordid = likeTypeRs[i].accordid;
  //     likedArr.push(accordid);
  // }
  // const likeRs = await mysqlInstance.getContentidByLike(userid, likedArr);
  const likeRs = await mysqlInstance.getContentidByLike(userid, accordidEdArr);
  for (let i = 0; i < likeRs.length; i++) {
    const data = {
      contentid: likeRs[i].id,
      content: likeRs[i].content,
      recommen_type: 1,
    };
    likeArr.push(data);
  }
  // 根据评论
  // const commentTypeRs = await mysqlInstance.getContentByRecommenType(userid, TODAYTIME, 2);
  // const commentedArr = [];
  // for (let i = 0; i < commentTypeRs.length; i++) {
  //     const accordid = commentTypeRs[i].accordid;
  //     commentedArr.push(accordid);
  // }
  const commentRs = await mysqlInstance.getContentidByComment(userid, accordidEdArr);
  for (let i = 0; i < commentRs.length; i++) {
    const data = {
      contentid: commentRs[i].id,
      content: commentRs[i].content,
      recommen_type: 2,
    };
    commentArr.push(data);
  }
  // 根据发布的动态
  // const contentTypeRs = await mysqlInstance.getContentByRecommenType(userid, TODAYTIME, 3);
  // const contentedArr = [];
  // for (let i = 0; i < contentTypeRs.length; i++) {
  //     const accordid = contentTypeRs[i].accordid;
  //     contentedArr.push(accordid);
  // }
  const contentRs = await mysqlInstance.getContentidByContentPush(userid, accordidEdArr);
  for (let i = 0; i < contentRs.length; i++) {
    const data = {
      contentid: contentRs[i].id,
      content: contentRs[i].content,
      recommen_type: 3,
    };
    contentArr.push(data);
  }
  // 随机推荐一条
  // const randomTypeRs = await mysqlInstance.getContentByRecommenType(userid, TODAYTIME, 4);
  // const randomedArr = [];
  // for (let i = 0; i < randomTypeRs.length; i++) {
  //     const accordid = randomTypeRs[i].accordid;
  //     randomedArr.push(accordid);
  // }
  const randomRs = await mysqlInstance.getContentidByRandom(accordidEdArr);
  for (let i = 0; i < randomRs.length; i++) {
    const data = {
      contentid: randomRs[i].id,
      content: randomRs[i].content,
      recommen_type: 4,
    };
    randomArr.push(data);
  }
  return { likeArr, commentArr, contentArr, randomArr };
}
/**
 * 
 * @param {*} userid 
 * @param {
 * [{
 *  recommen_type,
 *  body: [{
        "item_id": 1,
        "neighbor_id": 31521,
        "score": 0.6927070021629333
    }]
 * }]
 * } zoeArr 
 * @param {*} TODAYTIME 
 */
async function shuchuFun(userid, zoeArr, TODAYTIME) {
  const allSedRecommenRs = await mysqlInstance.getAllSedRecommen(userid, TODAYTIME);
  const allSedRecommenArr = [];
  for (let i = 0; i < allSedRecommenRs.length; i++) {
    const id = allSedRecommenRs[i].recommenid;
    allSedRecommenArr.push(id);
  }
  const allArr = [];
  for (let i = 0; i < zoeArr.length; i++) {
    const recommen_type = zoeArr[i].recommen_type;
    const accordid = zoeArr[i].accordid;
    const zoeBody = zoeArr[i].body;
    const neighborIdArr = [];
    for (let j = 0; j < zoeBody.length; j++) {
      const neighbor_id = zoeBody[j].id;
      neighborIdArr.push(neighbor_id);
    }
    const notSedRs = await mysqlInstance.getNotSedRecommen(neighborIdArr, TODAYTIME);
    const notSedArr = [];
    for (let j = 0; j < notSedRs.length; j++) {
      const contentid = notSedRs[j].id;
      notSedArr.push(contentid);
    }
    // 排除关注
    const notUseArr = [];
    const AttentionRs = await mysqlInstance.getContentInAttention(userid, notSedArr);
    for (let j = 0; j < AttentionRs.length; j++) {
      notUseArr.push(parseInt(AttentionRs[j].id));
    }
    // 排除拉黑
    const blackRs = await mysqlInstance.getContentInBlack(userid, notSedArr);
    for (let j = 0; j < blackRs.length; j++) {
      notUseArr.push(parseInt(blackRs[j].id));
    }
    // 排除自己的
    const myRs = await mysqlInstance.getContentInMy(userid, notSedArr);
    for (let j = 0; j < myRs.length; j++) {
      notUseArr.push(parseInt(myRs[j].id));
    }
    // 过滤一个月
    const monthRs = await mysqlInstance.getContentOverOneMonth(userid, notSedArr, TODAYTIME);
    for (let j = 0; j < monthRs.length; j++) {
      notUseArr.push(parseInt(monthRs[j].id));
    }
    const useArr = [];
    for (let j = 0; j < notSedArr.length; j++) {
      const contentid = parseInt(notSedArr[j]);
      if (notUseArr.indexOf(contentid) === -1) {
        useArr.push(contentid);
      }
    }
    const body = {
      accordid: accordid,
      recommen_type: recommen_type,
      neighborIdArr: useArr,
    };
    allArr.push(body);
  }
  let RS = {
    1: [],
    2: [],
    3: [],
    4: [],
  };
  const arr = [];
  for (let i = 0; i < allArr.length; i++) {
    const recommen_type = allArr[i].recommen_type;
    const accordid = allArr[i].accordid;
    const neighborIdArr = allArr[i].neighborIdArr;
    for (let z = 0; z < neighborIdArr.length; z++) {
      const data = {
        accordid: accordid,
        recommen_type: recommen_type,
        contentid: neighborIdArr[z],
      };
      arr.push(data);
    }
  }

  for (let j = 0; j < arr.length; j++) {
    const recommen_type = arr[j].recommen_type;
    RS[recommen_type].push(arr[j]);
  }

  const haveArr = [];

  for (const key in RS) {
    let num = 0;
    switch (parseInt(key)) {
      case 1:
        num = 3;
        break;
      case 2:
        num = 3;
        break;
      case 3:
        num = 2;
        break;
      case 4:
        num = 1;
        break;
    }
    let start = 0;
    const arr = [];
    let xunhuanNum = 0;
    do {
      if (RS[key].length <= num) {
        start = 10;
      } else {
        const contentidBody = RS[key][Math.floor(Math.random() * RS[key].length)];
        const contentid = contentidBody.contentid;
        if (haveArr.indexOf(contentid) === -1) {
          haveArr.push(contentid);
          arr.push(contentidBody);
          start++;
        } else {
          xunhuanNum++;
          if (xunhuanNum > 10) {
            // const rs = yield mysqlInstance.getContentidByRandom(haveArr);
            // haveArr.push(contentid)
            // arr.push(contentidBody)
            // start++
            start = 10;
          }
        }
      }
    } while (start < num);
    RS[key] = arr;
  }
  const BODY = [];
  for (const key in RS) {
    for (let j = 0; j < RS[key].length; j++) {
      BODY.push(RS[key][j]);
    }
  }
  if (BODY.length !== 9) {
    const limit = 9 - BODY.length;
    const randomTypeRs = await mysqlInstance.getContentByRecommenType(userid, TODAYTIME, 4);
    const randomedArr = [];
    for (let i = 0; i < randomTypeRs.length; i++) {
      const accordid = randomTypeRs[i].accordid;
      if (accordid) {
        randomedArr.push(accordid);
      }
    }
    const randomRs = await mysqlInstance.getContentidByRandomV2(randomedArr, limit);
    for (let i = 0; i < randomRs.length; i++) {
      const data = {
        accordid: null,
        recommen_type: 4,
        contentid: randomRs[i].id,
      };
      BODY.push(data);
    }
  }
  const recommenRs = await mysqlInstance.getRecommenByTimeAndUserNew(userid, TODAYTIME);
  if (recommenRs.length) {
    const BODY = [];
    for (let i = 0; i < recommenRs.length; i++) {
      const body = {
        accordid: recommenRs[i].accordid,
        recommen_type: recommenRs[i].accord_type,
        contentid: recommenRs[i].recommenid,
      };
      BODY.push(body);
    }
    return BODY;
  }
  for (let j = 0; j < BODY.length; j++) {
    await mysqlInstance.addRecommenNew(userid, BODY[j]);
  }
  return BODY;
}

function createRequest(data) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'POST',
      url: URL,
      headers: {
        'cache-control': 'no-cache',
        'Content-Type': 'application/json',
      },
      body: {
        function: 'searchByContents',
        params: {
          query_row: {
            content: data['content'],
          },
          // modes: ['semantic', 'analyze'],
          size: 100,
        },
      },
      json: true,
    };
    request(options, function (error, response, body) {
      if (error) {
        resolve({
          result: false,
        });
      }
      if (body && body.code === 200 && body.result.length && body.result[0].id) {
        const rs = {
          body: body.result,
          recommen_type: data.recommen_type,
          accordid: data['contentid'],
        };
        resolve({
          result: true,
          data: rs,
        });
      } else {
        resolve({
          result: false,
        });
      }
    });
  });
}

function arrToSet(arr) {
  let newSet = new Set();
  for (let i = 0; i < arr.length; i++) {
    newSet.add(arr[i]);
  }
  const newArr = Array.from(newSet);
  return newArr;
}

function returnFloat(value) {
  const s = value.toString().split('.');
  if (s.length == 1) {
    value = value.toString() + '.00';
    return value;
  }
  if (s.length > 1) {
    if (s[1].length < 2) {
      value = value.toString() + '0';
    }
    return value;
  }
}

module.exports.recommenNewInstance = new RecommenNewController();
