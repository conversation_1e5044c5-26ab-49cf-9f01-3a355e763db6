'use strict';

const co = require('co');
const iap = require('iap');
const RESPONSES = require('../config/iosPayConfig').responses;
const BaseController = require('./BaseController');
const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;
const imService = require('../services/IM_service').IMInstance;
const youmengService = require('../services/youmeng').YMInstance;
const moment = require('moment');

const FOOD = {
  weight_10kg: 5,
  weight_52kg: 6,
  weight_520kg: 7,
  weight_1000kg: 8,
};
class PayController extends BaseController {
  constructor(props) {
    super(props);
  }
  iosPay(receiptData, uid, product_id) {
    return co(function* () {
      const receipt_hash = getHashCode(receiptData);
      const receiptDataRs = yield mysqlInstance.getPayByReceiptHashAndUserid(receipt_hash, uid);
      if (receiptDataRs.length) {
        return {
          backSuccess: false,
          msg: '该 receiptData 重复使用',
        };
      }
      const newPayId = yield mysqlInstance.addReceiptData(uid, receiptData, product_id, receipt_hash);
      const platform = 'apple';
      const payment = {
        receipt: receiptData,
        productId: product_id,
        // packageName: "cn.52hezi.sp.FTHZ",
        secret: 'c19b71f13a154189822ee61292e88647',
      };
      const rs = yield verifyPayment(platform, payment);
      if (!rs['result']) {
        yield mysqlInstance.updatePayStatus(newPayId, 3);
        const response = RESPONSES[rs['err'].status];
        return {
          backSuccess: false,
          msg: {
            status: rs['err'].status,
            response: response,
          },
        };
      }

      // const rs = {
      //     result: true,
      //     data: {
      //         "receipt": {
      //             "receipt_type": "ProductionSandbox",
      //             "adam_id": 0,
      //             "app_item_id": 0,
      //             "bundle_id": "cn.52hezi.sp.FTHZ",
      //             "application_version": "20201026",
      //             "download_id": 0,
      //             "version_external_identifier": 0,
      //             "receipt_creation_date": "2020-12-28 06:33:31 Etc/GMT",
      //             "receipt_creation_date_ms": "1609137211000",
      //             "receipt_creation_date_pst": "2020-12-27 22:33:31 America/Los_Angeles",
      //             "request_date": "2020-12-28 06:33:38 Etc/GMT",
      //             "request_date_ms": "1609137218617",
      //             "request_date_pst": "2020-12-27 22:33:38 America/Los_Angeles",
      //             "original_purchase_date": "2013-08-01 07:00:00 Etc/GMT",
      //             "original_purchase_date_ms": "1375340400000",
      //             "original_purchase_date_pst": "2013-08-01 00:00:00 America/Los_Angeles",
      //             "original_application_version": "1.0",
      //             "in_app": [{
      //                 "quantity": "1",
      //                 "product_id": "weight_10kg",
      //                 "transaction_id": "1000000758974965",
      //                 "original_transaction_id": "1000000758974965",
      //                 "purchase_date": "2020-12-28 06:33:31 Etc/GMT",
      //                 "purchase_date_ms": "1609137211000",
      //                 "purchase_date_pst": "2020-12-27 22:33:31 America/Los_Angeles",
      //                 "original_purchase_date": "2020-12-28 06:33:31 Etc/GMT",
      //                 "original_purchase_date_ms": "1609137211000",
      //                 "original_purchase_date_pst": "2020-12-27 22:33:31 America/Los_Angeles",
      //                 "is_trial_period": "false"
      //             }]
      //         },
      //         "latestReceiptInfo": [{
      //             "quantity": "1",
      //             "product_id": "weight_10kg",
      //             "transaction_id": "1000000758974965",
      //             "original_transaction_id": "1000000758974965",
      //             "purchase_date": "2020-12-28 06:33:31 Etc/GMT",
      //             "purchase_date_ms": "1609137211000",
      //             "purchase_date_pst": "2020-12-27 22:33:31 America/Los_Angeles",
      //             "original_purchase_date": "2020-12-28 06:33:31 Etc/GMT",
      //             "original_purchase_date_ms": "1609137211000",
      //             "original_purchase_date_pst": "2020-12-27 22:33:31 America/Los_Angeles",
      //             "is_trial_period": "false"
      //         }],
      //         "latestExpiredReceiptInfo": null,
      //         "productId": "weight_10kg",
      //         "transactionId": "1000000758974965",
      //         "purchaseDate": 1609137211000,
      //         "expirationDate": null,
      //         "pendingRenewalInfo": null,
      //         "environment": "sandbox",
      //         "platform": "apple"
      //     }
      // }

      const response = rs['data'];
      const productId = response['productId'];
      if (product_id !== productId) {
        return {
          backSuccess: false,
          msg: '商品信息不符',
        };
      }
      if (response['environment'] === 'sandbox') {
        return {
          backSuccess: true,
        };
      }
      const foodId = FOOD[productId];
      const foodRs = yield mysqlInstance.getFoodById(foodId);
      if (foodRs && foodRs['type'] === 'error') {
        elogger.error('Feed Error 2001 ' + foodRs['msg']);
        return {
          backSuccess: false,
          msg: 'Feed Error 3001-1',
        };
      }

      let toUserInfo = yield redisInstance.hmget('SOCKET:USER:NAMESPACE:ALL', `${uid}`);
      if (toUserInfo && toUserInfo[0]) {
        toUserInfo = JSON.parse(toUserInfo[0]);
      }
      const toUserWeight_ = toUserInfo['weight'];

      let toStatisticsWeight = toUserWeight_;
      const toUserWeight = parseInt(parseFloat(toUserInfo.weight) * 100);
      toUserInfo.weight = ((toUserWeight + parseFloat(foodRs[0].weight) * 100) / 100).toFixed(2);

      yield redisInstance.hashAdd('SOCKET:USER:NAMESPACE:ALL', [`${uid}`, JSON.stringify(toUserInfo)]);

      const toRedisRS = yield redisInstance.hashGet('HASH:SOCKET:FOOD:STATISTICS');
      for (const key in toRedisRS) {
        if (parseInt(key) === parseInt(uid)) {
          toStatisticsWeight =
            parseInt(parseFloat(JSON.parse(toRedisRS[key]).weight) * 100) + parseFloat(foodRs[0].weight) * 100;
          break;
        }
      }

      toUserInfo.weight = (toStatisticsWeight / 100).toFixed(2);
      for (const key in toUserInfo) {
        if (typeof toUserInfo[key] === 'number') {
          toUserInfo[key] = `${toUserInfo[key]}`;
        }
      }
      yield redisInstance.hashAdd('HASH:SOCKET:FOOD:STATISTICS', [`${uid}`, JSON.stringify(toUserInfo)]);

      const key__ = 'HASH:USER:INFO:' + uid;
      const redisRS__ = yield redisInstance.hashGet(key__);
      const data = {
        userid: uid,
        text: `购买体重 ${foodRs[0].weight}KG 成功`,
        from: 4,
        nickname: redisRS__['nickname'],
      };
      const sendMsgRs = imService.sendMsg(data);

      const deviceToken = redisRS__['deviceToken'];

      const key2__ = `STR:USER:PUSH:COUNT:${uid}`;
      const redisRS2__ = yield redisInstance.get(key2__);
      const pushCount = redisRS2__;

      const body__ = {
        text: redisRS__['nickname'],
        device_token: deviceToken,
        pushCount: pushCount,
        to_userid: uid,
      };
      const msgPush = youmengService.msgPush(body__);

      const notificationData = {
        userid: uid,
        text: `购买体重 ${foodRs[0].weight}KG 成功`,
        avatar: redisRS__['avatar'],
        nickname: redisRS__['nickname'],
        type: 6,
        payid: newPayId,
      };
      yield mysqlInstance.insertIntoNotification(notificationData);
      const redislist = yield redisInstance.lpush(`LIST:USER:CONMENT:${uid}`, uid);
      if (parseInt(redislist) == 30) {
        yield redisInstance.rpop(`LIST:USER:CONMENT:${uid}`);
      }

      yield mysqlInstance.updatePayStatusV2(newPayId, 2, JSON.stringify(response));

      return {
        backSuccess: true,
      };
    });
  }

  async incomeStatistics(startTime, endTime) {
    const result = await mysqlInstance.getIncomeStatistics(startTime, endTime);
    const arr = [];
    for (let i = 0; i < result.length; i++) {
      const data = {
        nickname: result[i].nickname,
        iap_id: result[i].iap_id,
        price: result[i].price,
        create_time: moment(result[i].create_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
        update_time: moment(result[i].update_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
      };
      arr.push(data);
    }

    const result_ = await mysqlInstance.getIncomeStatisticsSum(startTime, endTime);
    const income = result_[0].Income;

    const result__ = await mysqlInstance.getIncomeStatisticsCount(startTime, endTime);
    const count = result__[0].count;
    const args = {
      record: arr,
      count: count,
      income: income || 0,
    };
    return {
      backSuccess: true,
      data: args,
    };
  }
}

function verifyPayment(platform, payment) {
  return new Promise((resolve, reject) => {
    iap.verifyPayment(platform, payment, function (err, response) {
      if (err) {
        resolve({
          result: false,
          err: err,
        });
      }
      resolve({
        result: true,
        data: response,
      });
    });
  });
}

function getHashCode(str, caseSensitive) {
  if (!caseSensitive) {
    str = str.toLowerCase();
  }
  var hash = 1315423911,
    i,
    ch;
  for (i = str.length - 1; i >= 0; i--) {
    ch = str.charCodeAt(i);
    hash ^= (hash << 5) + ch + (hash >> 2);
  }

  return hash & 0x7fffffff;
}

module.exports.payInstance = new PayController();
