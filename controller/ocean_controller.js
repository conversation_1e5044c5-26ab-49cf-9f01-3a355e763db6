'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const request = require('request');
const URL = 'http://172.16.241.103:6763/r/ocean/';
const newUrl = 'http://172.16.241.103:52069/r/ocean/';
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const IMAEWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const VIDEOWAILIAN = DefaultConfig.wailian.VIDEO_DOMAIN;
const redisInstance = require('../models/redis').redisInstance;
const mysqlInstance = require('../models/mysql').mysqlInstance;
class OceanController extends BaseController {
  constructor(props) {
    super(props);
  }

  getOceanFun(userid, hertz) {
    const self = this;
    return co(function* () {
      let rs = null;
      if (userid === 11 || userid === 101678 || userid === 102541) {
        rs = yield createRequestV2(userid, hertz);
      } else {
        rs = yield createRequest(userid);
      }

      let data = JSON.parse(rs['data']);
      for (let i = 0; i < data['data'][0]['data'].length; i++) {
        const userid = data['data'][0]['data'][i].user.uid;
        const colorFont = yield self.getColorFont(userid);
        data['data'][0]['data'][i]['user']['colorFont'] = colorFont;
        const contentid = data['data'][0]['data'][i].affair.aid;
        const type = data['data'][0]['data'][i].affair.type;
        if (parseInt(type) === 3) {
          const contentRs = yield mysqlInstance.getContentByIdV2(contentid);
          const linkShareContent = contentRs[0].linkshare;
          const linkShareContentData = JSON.parse(linkShareContent);
          data['data'][0]['data'][i].affair['linkShare'] = linkShareContentData;
        } else {
          data['data'][0]['data'][i].affair['linkShare'] = null;
        }
        if (parseInt(type) === 4) {
          const contentRs = yield mysqlInstance.getContentByIdV2(contentid);
          const video = contentRs[0].video;
          data['data'][0]['data'][i].affair['video'] = `${VIDEOWAILIAN}${video}`;
        } else {
          data['data'][0]['data'][i].affair['video'] = null;
        }
      }
      data = JSON.stringify(data);
      rs['data'] = data;

      return rs;
    });
  }

  getOceanFunV2(userid, hertz) {
    return co(function* () {
      const rs = yield createRequestV2(userid, hertz);
      return rs;
    });
  }
}

function createRequest(userid) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'GET',
      url: URL,
      qs: { userId: parseInt(userid) },
      headers: {
        'cache-control': 'no-cache',
      },
    };
    request(options, function (error, response, body) {
      if (error) {
        return resolve({
          result: false,
          data: body,
        });
      }

      return resolve({
        result: true,
        data: body,
      });
    });
  });
}

function createRequestV2(userid, hertz) {
  return new Promise((resolve, reject) => {
    const qs = { userId: parseInt(userid) };
    if (hertz) {
      qs['hertz'] = parseFloat(hertz);
    }
    const options = {
      method: 'GET',
      url: newUrl,
      qs: qs,
      headers: {
        'cache-control': 'no-cache',
      },
    };
    request(options, function (error, response, body) {
      if (error) {
        return resolve({
          result: false,
          data: body,
        });
      }
      if (body.startsWith('<!DOCTYPE HTML PUBLIC')) {
        return resolve({
          result: false,
          data: '{}',
        });
      }
      body = JSON.parse(body);
      if (!body.data) {
        return resolve({
          result: false,
          data: '{}',
        });
      }

      return resolve({
        result: true,
        data: JSON.stringify(body),
      });
    });
  });
}

module.exports.oceanInstance = new OceanController();
