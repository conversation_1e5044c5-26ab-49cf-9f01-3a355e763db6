'use strict';

const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const VIDEOWAILIAN = DefaultConfig.wailian.VIDEO_DOMAIN;
const AVATAR_WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const imService = require('../services/IM_service').IMInstance;
const youmengService = require('../services/youmeng').YMInstance;
const contentTagType = require('../config/tagType').tagType;
const request = require('request');
const SFMInstance = require('../services/space_fm').SFMService;
const URL = 'http://172.16.241.103:10032/hertz/rpc';
const IMService = require('../services/IM_service').IMInstance;

const LINKSHAREBODY = DefaultConfig.LINKSHAREBODY;

const LINKSHAREICON = {
  jike: DefaultConfig.wailian.DOC_DOMAIN + 'jike.png',
  weixin: DefaultConfig.wailian.DOC_DOMAIN + 'wechat.png',
  douban: DefaultConfig.wailian.DOC_DOMAIN + 'douban.png',
  b23: DefaultConfig.wailian.DOC_DOMAIN + 'bilibili.png',
  weibo: DefaultConfig.wailian.DOC_DOMAIN + 'weibo.png',
  xiaoyuzhou: DefaultConfig.wailian.DOC_DOMAIN + 'xiaoyouzhou.png',
  zhihu: DefaultConfig.wailian.DOC_DOMAIN + 'zhihu.png',
};
class ContentController extends BaseController {
  constructor(props) {
    super(props);
  }

  getContentTag() {
    return co(function* () {
      const rs = yield mysqlInstance.getContentTag();
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 10001 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Content Error 10001',
        };
      }
      const arr = [];
      for (let i = 0; i < rs.length; i++) {
        let slogan = null;
        if (rs[i].id === 4) {
          const date = new Date();
          slogan = dateFormat('YYYY-mm-dd HH:MM', date);
        } else {
          slogan = rs[i].slogan;
        }
        const data = {
          tagid: `${rs[i].id}`,
          name: rs[i].name,
          slogan: slogan,
        };
        arr.push(data);
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  putContent(args) {
    const self = this;
    return co(function* () {
      args['text'] = self.toLiteral(args['text']);
      if (args['timing']) {
        args['status'] = 3;
      } else {
        args['status'] = 0;
      }
      const rs = yield mysqlInstance.putContent(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 1001 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Content Error 1001',
        };
      }
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      let type = '1';
      if (args['video']) {
        type = '4';
      }
      const redisRs = yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + rs, [
        'userid',
        args['userid'],
        'text',
        args['text'],
        'images',
        args['images'],
        'video',
        args['video'],
        'imageType',
        args['imageType'],
        'create_time',
        time,
        'tagType',
        args['tag'],
        'type',
        type,
        'likeNum',
        '0',
        'commentNum',
        '0',
        'location',
        args['location'],
      ]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:CONTENT:INFO:>>${redisRs['msg']},contentid>>${rs}`);
      }
      const key = 'ZSET:USER:CONTENT:' + args['userid'];
      const score = yield redisInstance.zsetScoreGet(key);
      const userArrRs = yield redisInstance.zsetAdd(key, score, rs);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:USER:CONTENT:>>${userArrRs['msg']},contentid>>${rs}`);
      }
      yield mysqlInstance.updateUserContentStatus(args['userid'], 1);
      const arr = [];
      arr.push({ contentid: rs.toString() });
      if (args['lat'] && args['lon'] && args['location']) {
        yield self.updateUserLocation(args['userid'], args['location']);
      }
      const redisListRs = yield redisInstance.lrange(`LIST:CONTENT:NEW`, 0, -1);
      const redisList = [];
      for (let i = 0; i < redisListRs.length; i++) {
        redisList.push(parseInt(redisListRs[i]));
      }
      if (redisList.indexOf(args['userid']) == -1) {
        if (redisList.length >= 500) {
          yield redisInstance.rpop(`LIST:CONTENT:NEW`);
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        } else {
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        }
      }
      yield self.changeUserHertz(args['userid'], 1);
      // if (args['userid'] === 7) {
      if (args['atUserIds'] && !args['timing']) {
        const atUserIds = args['atUserIds'].split(',').map((id) => parseInt(id));
        if (atUserIds.length > 0) {
          // 获取发送者信息（只需查询一次）
          const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);

          // 准备通用数据
          const content = args['text'] ? args['text'].slice(0, 50) : '';
          let image = '';
          if (args['images']) {
            const imagesArr = args['images'].split(',');
            image = imagesArr[0];
          }

          // 处理每个被@的用户
          for (const toUserid of atUserIds) {
            // 跳过自己@自己的情况
            if (toUserid === args['userid']) {
              continue;
            }

            // const msg = `用户 ${userInfo[0].nickname} 在${
            //   userInfo[0].gender === 1 ? '他' : userInfo[0].gender === 2 ? '她' : 'Ta'
            // }的动态里提到了你`;

            const notificationData = {
              userid: toUserid,
              text: ' 提及了你',
              avatar: userInfo[0]['avatar'],
              nickname: userInfo[0]['nickname'],
              type: 7,
              contentid: rs,
              content: content,
              images: image,
            };

            yield mysqlInstance.insertIntoNotification(notificationData);
          }
        }
      }
      // }
      // if () {

      // }

      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  updateUserLocation(userid, location) {
    return co(function* () {
      const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
      const index = Math.floor(Math.random() * locationArr.length);
      const location_ = locationArr[index];
      const data = {
        country: location === 'null' ? location_ : location || location_,
        province: location === 'null' ? location_ : location || location_,
        city: location === 'null' ? location_ : location || location_,
        userid: userid,
      };
      yield mysqlInstance.updateUserLocation(data);
      const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, [
        'city',
        location || '',
        'province',
        location || '',
        'country',
        location || '',
      ]);
      if (redisUserInfo && redisUserInfo['type'] === 'error') {
        elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${userid}`);
      }
      return;
    });
  }

  getWYMusicInfo(musicText) {
    const self = this;
    return co(function* () {
      const musicId = self.getCaption(musicText);
      const musicInfoUrl = `http://music.163.com/api/song/detail/?id=${musicId}&ids=[${musicId}]`;
      const musicMp3Url = `http://music.163.com/song/media/outer/url?id=${musicId}.mp3`;
      let musicInfo = yield self.getWangYiYunMusicInfo(musicInfoUrl);
      if (musicInfo && musicInfo['type'] === 'error') {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }
      musicInfo = JSON.parse(musicInfo);
      const songsInfo = musicInfo['songs'];
      if (!songsInfo || (songsInfo && songsInfo.length == 0)) {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }

      const artists = songsInfo[0]['artists'];
      let artistsStr = '';
      for (let i = 0; i < artists.length; i++) {
        if (i == 0) {
          artistsStr += artists[i]['name'];
        } else {
          artistsStr += `/${artists[i]['name']}`;
        }
      }
      const musicInfoData = {
        musicName: songsInfo[0]['name'],
        albumName: songsInfo[0]['album']['name'],
        artists: artistsStr,
        imgUrl: songsInfo[0]['album']['blurPicUrl'],
        musicMp3Url: musicMp3Url,
        from: 1,
      };
      return {
        backSuccess: true,
        data: musicInfoData,
      };
    });
  }

  getMusicInfo(musicText, from) {
    return co(function* () {
      const musicInfo = yield SFMInstance.getMusicInfo(musicText);
      if (musicInfo && !musicInfo['type']) {
        return {
          backSuccess: false,
          msg: musicInfo['data'],
        };
      }
      const musicInfoData = {
        musicName: musicInfo['data'].name,
        albumName: '',
        artists: musicInfo['data'].artist,
        imgUrl: musicInfo['data'].imageUrl,
        musicMp3Url: musicInfo['data'].playUrl,
        from: from,
        type: 1,
      };
      return {
        backSuccess: true,
        data: musicInfoData,
      };
    });
  }

  getQQMusicInfo(musicText) {
    const self = this;
    return co(function* () {
      musicText = self.httpString(musicText);
      const musicInfoUrl = musicText[0];
      let musicInfo = yield self.getQQMusicInfoFun(musicInfoUrl);
      if (musicInfo && musicInfo['type'] === 'error') {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }
      return {
        backSuccess: true,
        data: musicInfo,
      };
    });
  }

  putWYMusicContent(args) {
    const self = this;
    return co(function* () {
      args['text'] = self.toLiteral(args['text']);
      const musicText = args['musicText'];
      const musicId = self.getCaption(musicText);
      const musicInfoUrl = `http://music.163.com/api/song/detail/?id=${musicId}&ids=[${musicId}]`;
      const musicMp3Url = `http://music.163.com/song/media/outer/url?id=${musicId}.mp3`;
      let musicInfo = yield self.getWangYiYunMusicInfo(musicInfoUrl);
      if (musicInfo && musicInfo['type'] === 'error') {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }
      musicInfo = JSON.parse(musicInfo);
      const songsInfo = musicInfo['songs'];
      if (songsInfo.length == 0) {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }

      const artists = songsInfo[0]['artists'];
      let artistsStr = '';
      for (let i = 0; i < artists.length; i++) {
        if (i == 0) {
          artistsStr += artists[i]['name'];
        } else {
          artistsStr += `/${artists[i]['name']}`;
        }
      }
      const musicInfoData = {
        shareUrl: musicText,
        musicName: songsInfo[0]['name'],
        albumName: songsInfo[0]['album']['name'],
        artists: artistsStr,
        imgUrl: songsInfo[0]['album']['blurPicUrl'],
        musicMp3Url: musicMp3Url,
        from: 1,
      };
      let musicContent = JSON.stringify(musicInfoData);
      args['musicContent'] = self.toLiteral(musicContent);
      const rs = yield mysqlInstance.putMusicContent(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 1001 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Content Error 1001',
        };
      }
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const redisRs = yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + rs, [
        'userid',
        args['userid'],
        'text',
        args['text'],
        'images',
        args['images'],
        'imageType',
        args['imageType'],
        'create_time',
        time,
        'tagType',
        args['tag'],
        'type',
        '2',
        'musicContent',
        musicContent,
        'likeNum',
        '0',
        'commentNum',
        '0',
        'location',
        args['location'],
      ]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:CONTENT:INFO:>>${redisRs['msg']},contentid>>${rs}`);
      }
      const key = 'ZSET:USER:CONTENT:' + args['userid'];
      const score = yield redisInstance.zsetScoreGet(key);
      const userArrRs = yield redisInstance.zsetAdd(key, score, rs);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:USER:CONTENT:>>${userArrRs['msg']},contentid>>${rs}`);
      }
      yield mysqlInstance.updateUserContentStatus(args['userid'], 1);
      const arr = [];
      arr.push({ contentid: rs.toString() });
      if (args['lat'] && args['lon'] && args['location']) {
        yield self.updateUserLocation(args['userid'], args['location']);
      }
      const redisListRs = yield redisInstance.lrange(`LIST:CONTENT:NEW`, 0, -1);
      const redisList = [];
      for (let i = 0; i < redisListRs.length; i++) {
        redisList.push(parseInt(redisListRs[i]));
      }
      if (redisList.indexOf(args['userid']) == -1) {
        if (redisList.length >= 500) {
          yield redisInstance.rpop(`LIST:CONTENT:NEW`);
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        } else {
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        }
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  putQQMusicContent(args) {
    const self = this;
    return co(function* () {
      args['text'] = self.toLiteral(args['text']);
      const shareUrl = args['musicText'];
      let musicText = args['musicText'];
      musicText = self.httpString(musicText);
      const musicInfoUrl = musicText[0];
      let musicInfo = yield self.getQQMusicInfoFun(musicInfoUrl);
      if (musicInfo && musicInfo['type'] === 'error') {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }

      const musicInfoData = {
        shareUrl: shareUrl,
        musicName: musicInfo['musicName'],
        albumName: musicInfo['albumName'],
        artists: musicInfo['artists'],
        imgUrl: musicInfo['imgUrl'],
        musicMp3Url: musicInfo['musicMp3Url'],
        from: 3,
      };
      let musicContent = JSON.stringify(musicInfoData);
      args['musicContent'] = self.toLiteral(musicContent);
      const rs = yield mysqlInstance.putMusicContent(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 1001 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Content Error 1001',
        };
      }
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const redisRs = yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + rs, [
        'userid',
        args['userid'],
        'text',
        args['text'],
        'images',
        args['images'],
        'imageType',
        args['imageType'],
        'create_time',
        time,
        'tagType',
        args['tag'],
        'type',
        '2',
        'musicContent',
        musicContent,
        'likeNum',
        '0',
        'commentNum',
        '0',
        'location',
        args['location'],
      ]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:CONTENT:INFO:>>${redisRs['msg']},contentid>>${rs}`);
      }
      const key = 'ZSET:USER:CONTENT:' + args['userid'];
      const score = yield redisInstance.zsetScoreGet(key);
      const userArrRs = yield redisInstance.zsetAdd(key, score, rs);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:USER:CONTENT:>>${userArrRs['msg']},contentid>>${rs}`);
      }
      yield mysqlInstance.updateUserContentStatus(args['userid'], 1);
      const arr = [];
      arr.push({ contentid: rs.toString() });
      if (args['lat'] && args['lon'] && args['location']) {
        yield self.updateUserLocation(args['userid'], args['location']);
      }
      const redisListRs = yield redisInstance.lrange(`LIST:CONTENT:NEW`, 0, -1);
      const redisList = [];
      for (let i = 0; i < redisListRs.length; i++) {
        redisList.push(parseInt(redisListRs[i]));
      }
      if (redisList.indexOf(args['userid']) == -1) {
        if (redisList.length >= 500) {
          yield redisInstance.rpop(`LIST:CONTENT:NEW`);
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        } else {
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        }
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  putLinkShareContent(args) {
    const self = this;
    return co(function* () {
      args['text'] = self.toLiteral(args['text']);
      let shareText = args['shareText'];
      if (args['timing']) {
        args['status'] = 3;
      } else {
        args['status'] = 0;
      }
      let from = null;
      for (const key in LINKSHAREBODY) {
        if (shareText.indexOf(key) !== -1) {
          from = LINKSHAREBODY[key];
        }
      }

      if (!from) {
        return {
          backSuccess: false,
          msg: '暂时只支持 微信、微博、豆瓣、哔哩哔哩、知乎、即刻、小宇宙',
        };
      }
      let icon = null;
      for (const key in LINKSHAREICON) {
        if (shareText.indexOf(key) !== -1) {
          icon = LINKSHAREICON[key];
        }
      }

      const linkShareInfoData = {
        shareText: shareText,
        from: from,
        icon: icon,
      };

      let linkShareContent = JSON.stringify(linkShareInfoData);
      args['linkShareContent'] = self.toLiteral(linkShareContent);
      const rs = yield mysqlInstance.putLinkShareContent(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 1001 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Content Error 1001',
        };
      }
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const redisRs = yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + rs, [
        'userid',
        args['userid'],
        'text',
        args['text'],
        'images',
        args['images'],
        'imageType',
        args['imageType'],
        'create_time',
        time,
        'tagType',
        args['tag'],
        'type',
        '2',
        'linkShareContent',
        linkShareContent,
        'likeNum',
        '0',
        'commentNum',
        '0',
        'location',
        args['location'],
      ]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:CONTENT:INFO:>>${redisRs['msg']},contentid>>${rs}`);
      }
      const key = 'ZSET:USER:CONTENT:' + args['userid'];
      const score = yield redisInstance.zsetScoreGet(key);
      const userArrRs = yield redisInstance.zsetAdd(key, score, rs);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:USER:CONTENT:>>${userArrRs['msg']},contentid>>${rs}`);
      }
      yield mysqlInstance.updateUserContentStatus(args['userid'], 1);
      const arr = [];
      arr.push({ contentid: rs.toString() });
      if (args['lat'] && args['lon'] && args['location']) {
        yield self.updateUserLocation(args['userid'], args['location']);
      }
      const redisListRs = yield redisInstance.lrange(`LIST:CONTENT:NEW`, 0, -1);
      const redisList = [];
      for (let i = 0; i < redisListRs.length; i++) {
        redisList.push(parseInt(redisListRs[i]));
      }
      if (redisList.indexOf(args['userid']) == -1) {
        if (redisList.length >= 500) {
          yield redisInstance.rpop(`LIST:CONTENT:NEW`);
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        } else {
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        }
      }
      yield self.changeUserHertz(args['userid'], 1);
      if (args['atUserIds'] && !args['timing']) {
        const atUserIds = args['atUserIds'].split(',').map((id) => parseInt(id));
        if (atUserIds.length > 0) {
          // 获取发送者信息（只需查询一次）
          const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);

          // 准备通用数据
          const content = args['text'] ? args['text'].slice(0, 50) : '';
          let image = '';
          if (args['images']) {
            const imagesArr = args['images'].split(',');
            image = imagesArr[0];
          }

          // 处理每个被@的用户
          for (const toUserid of atUserIds) {
            // 跳过自己@自己的情况
            if (toUserid === args['userid']) {
              continue;
            }

            // const msg = `用户 ${userInfo[0].nickname} 在${
            //   userInfo[0].gender === 1 ? '他' : userInfo[0].gender === 2 ? '她' : 'Ta'
            // }的动态里提到了你`;

            const notificationData = {
              userid: toUserid,
              text: ' 提及了你',
              avatar: userInfo[0]['avatar'],
              nickname: userInfo[0]['nickname'],
              type: 7,
              contentid: rs,
              content: content,
              images: image,
            };

            yield mysqlInstance.insertIntoNotification(notificationData);
          }
        }
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  putMusicContentNew(args) {
    const self = this;
    return co(function* () {
      if (args['timing']) {
        args['status'] = 3;
      } else {
        args['status'] = 0;
      }
      args['text'] = self.toLiteral(args['text']);
      const shareUrl = args['musicText'];
      let musicText = args['musicText'];
      const musicInfo = yield SFMInstance.getMusicInfo(musicText);
      if (musicInfo && !musicInfo['type']) {
        return {
          backSuccess: false,
          msg: musicInfo['data'],
        };
      }
      const playUrl = musicInfo['data'].playUrl;

      if (playUrl.indexOf('qq.com') !== -1) {
        args['from'] = 3;
      } else if (playUrl.indexOf('163.com') !== -1) {
        args['from'] = 1;
      } else if (playUrl.indexOf('xiami.net') !== -1) {
        args['from'] = 2;
      } else {
        return {
          backSuccess: false,
          msg: '未找到歌曲信息',
        };
      }
      const musicInfoData = {
        shareUrl: shareUrl,
        musicName: musicInfo['data'].name,
        albumName: '',
        artists: musicInfo['data'].artist,
        imgUrl: musicInfo['data'].imageUrl,
        musicMp3Url: musicInfo['data'].playUrl,
        from: args['from'],
      };

      let musicContent = JSON.stringify(musicInfoData);
      args['musicContent'] = self.toLiteral(musicContent);
      const rs = yield mysqlInstance.putMusicContent(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 1001 ' + rs['sql']);
        return {
          backSuccess: false,
          msg: 'Content Error 1001',
        };
      }
      const time = parseInt(Math.round(new Date().getTime() / 1000));
      const redisRs = yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + rs, [
        'userid',
        args['userid'],
        'text',
        args['text'],
        'images',
        args['images'],
        'imageType',
        args['imageType'],
        'create_time',
        time,
        'tagType',
        args['tag'],
        'type',
        '2',
        'musicContent',
        musicContent,
        'likeNum',
        '0',
        'commentNum',
        '0',
        'location',
        args['location'],
      ]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:CONTENT:INFO:>>${redisRs['msg']},contentid>>${rs}`);
      }
      const key = 'ZSET:USER:CONTENT:' + args['userid'];
      const score = yield redisInstance.zsetScoreGet(key);
      const userArrRs = yield redisInstance.zsetAdd(key, score, rs);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:USER:CONTENT:>>${userArrRs['msg']},contentid>>${rs}`);
      }
      yield mysqlInstance.updateUserContentStatus(args['userid'], 1);
      const arr = [];
      arr.push({ contentid: rs.toString() });
      if (args['lat'] && args['lon'] && args['location']) {
        yield self.updateUserLocation(args['userid'], args['location']);
      }
      const redisListRs = yield redisInstance.lrange(`LIST:CONTENT:NEW`, 0, -1);
      const redisList = [];
      for (let i = 0; i < redisListRs.length; i++) {
        redisList.push(parseInt(redisListRs[i]));
      }
      if (redisList.indexOf(args['userid']) == -1) {
        if (redisList.length >= 500) {
          yield redisInstance.rpop(`LIST:CONTENT:NEW`);
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        } else {
          yield redisInstance.lpush(`LIST:CONTENT:NEW`, args['userid']);
        }
      }
      yield self.changeUserHertz(args['userid'], 1);
      if (args['atUserIds'] && !args['timing']) {
        const atUserIds = args['atUserIds'].split(',').map((id) => parseInt(id));
        if (atUserIds.length > 0) {
          // 获取发送者信息（只需查询一次）
          const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);

          // 准备通用数据
          const content = args['text'] ? args['text'].slice(0, 50) : '';
          let image = '';
          if (args['images']) {
            const imagesArr = args['images'].split(',');
            image = imagesArr[0];
          }

          // 处理每个被@的用户
          for (const toUserid of atUserIds) {
            // 跳过自己@自己的情况
            if (toUserid === args['userid']) {
              continue;
            }

            // const msg = `用户 ${userInfo[0].nickname} 在${
            //   userInfo[0].gender === 1 ? '他' : userInfo[0].gender === 2 ? '她' : 'Ta'
            // }的动态里提到了你`;

            const notificationData = {
              userid: toUserid,
              text: ' 提及了你',
              avatar: userInfo[0]['avatar'],
              nickname: userInfo[0]['nickname'],
              type: 7,
              contentid: rs,
              content: content,
              images: image,
            };

            yield mysqlInstance.insertIntoNotification(notificationData);
          }
        }
      }
      return {
        backSuccess: true,
        data: arr,
      };
    });
  }

  getWangYiYunMusicInfo(url) {
    return new Promise((resolve, reject) => {
      request(url, function (error, response, body) {
        if (!error && response && response.statusCode == 200) {
          if (body['code'] == 400) {
            resolve({
              type: 'error',
              msg: error,
            });
          } else {
            resolve(body);
          }
        } else {
          resolve({
            type: 'error',
            msg: error,
          });
        }
      });
    });
  }

  getQQMusicInfoFun(url) {
    const self = this;
    return new Promise((resolve, reject) => {
      const data = {
        json: true,
        url: url,
        method: 'GET',
        headers: {
          'User-Agent':
            'Mozilla/5.0 (iPhone; CPU iPhone OS 11_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/11.0 Mobile/15E148 Safari/604.1',
        },
      };
      request(data, function (error, response, body) {
        let dds = body.replace(/ /g, ''); //dds为得到后的内容
        dds = dds.replace(/\s*/g, '');
        dds = dds.replace(/[\r\n]/g, '');
        const result = self.getCaptionFun(dds);
        if (
          result['musicName'].indexOf('DOCTYPEhtml') > 0 ||
          result['artists'].indexOf('DOCTYPEhtml') > 0 ||
          result['imgUrl'].indexOf('DOCTYPEhtml') > 0 ||
          result['musicMp3Url'].indexOf('DOCTYPE') > 0 ||
          !result['imgUrl'].startsWith('//')
        ) {
          resolve({
            type: 'error',
            msg: error,
          });
        } else {
          result['imgUrl'] = `http:${result['imgUrl']}`;
          resolve(result);
        }
      });
    });
  }

  getContentByTag(args) {
    const self = this;
    return co(function* () {
      // if (args['userid'] === 7 && args['tagid'] === 6) {
      //   const rs = yield mysqlInstance.getContentByTagV2(args);
      //   if (rs && rs['type'] === 'error') {
      //     elogger.error('Content Error 11001 ' + rs['msg']);
      //     return {
      //       backSuccess: false,
      //       msg: 'Content Error 11001',
      //     };
      //   }

      //   const rsCount = yield mysqlInstance.getContentCountByTagV2(args);
      //   if (rsCount && rsCount['type'] === 'error') {
      //     elogger.error('Content Error 110011 ' + rs['msg']);
      //     return {
      //       backSuccess: false,
      //       msg: 'Content Error 110011',
      //     };
      //   }

      //   const data = [];
      //   for (let i = 0; i < rs.length; i++) {
      //     if (rs[i].type == 1) {
      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let j = 0; j < imagesArr_.length; j++) {
      //           if (j === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
      //           }
      //         }
      //       }

      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: '',
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: [],
      //           type: '1',
      //           linkShare: [],
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     } else if (rs[i].type == 2) {
      //       const musicContent = rs[i].musicContent;
      //       const musicContentData = JSON.parse(musicContent);
      //       // const shareUrl = musicContentData['shareUrl']
      //       // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
      //       // if (musicInfo && musicInfo['type']) {
      //       //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      //       // }
      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let i = 0; i < imagesArr_.length; i++) {
      //           if (i === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
      //           }
      //         }
      //       }

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: '',
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: musicContentData,
      //           type: '2',
      //           linkShare: [],
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     } else if (rs[i].type == 3) {
      //       const linkShareContent = rs[i].linkshare;
      //       const linkShareContentData = JSON.parse(linkShareContent);
      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let i = 0; i < imagesArr_.length; i++) {
      //           if (i === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
      //           }
      //         }
      //       }

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: '',
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: [],
      //           type: '3',
      //           linkShare: linkShareContentData,
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     } else if (rs[i].type == 4) {
      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let j = 0; j < imagesArr_.length; j++) {
      //           if (j === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
      //           }
      //         }
      //       }

      //       let videoArrStr = '';
      //       if (rs[i].video) {
      //         const videos = rs[i].video;
      //         const videosArr_ = videos.split(',');
      //         for (let j = 0; j < videosArr_.length; j++) {
      //           if (j === videosArr_.length - 1) {
      //             videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
      //           } else {
      //             videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
      //           }
      //         }
      //       }

      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: videoArrStr,
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: [],
      //           type: '4',
      //           linkShare: [],
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     }
      //   }

      //   return {
      //     backSuccess: true,
      //     data: data,
      //     count: rsCount[0]['count'],
      //   };
      // } else if (args['userid'] === 7 && args['tagid'] === 1) {
      //   const reportRs = yield mysqlInstance.getAllReport();
      //   const reportUser = [];
      //   const arr = [105211, 103463, 105229];
      //   for (let i = 0; i < reportRs.length; i++) {
      //     const reported_userid = reportRs[i].reported_userid;
      //     const count = reportRs[i].count;
      //     if (parseInt(count) >= 5) {
      //       if (arr.indexOf(reported_userid) < 0) {
      //         reportUser.push(reported_userid);
      //       }
      //     }
      //   }
      //   args['reportUser'] = reportUser;
      //   const rs = yield mysqlInstance.getContentByTagV4(args);
      //   if (rs && rs['type'] === 'error') {
      //     elogger.error('Content Error 11001 ' + rs['msg']);
      //     return {
      //       backSuccess: false,
      //       msg: 'Content Error 11001',
      //     };
      //   }

      //   const rsCount = yield mysqlInstance.getContentCountByTagV3(reportUser);
      //   if (rsCount && rsCount['type'] === 'error') {
      //     elogger.error('Content Error 110011 ' + rs['msg']);
      //     return {
      //       backSuccess: false,
      //       msg: 'Content Error 110011',
      //     };
      //   }

      //   const data = [];
      //   for (let i = 0; i < rs.length; i++) {
      //     if (rs[i].type == 1) {
      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let j = 0; j < imagesArr_.length; j++) {
      //           if (j === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
      //           }
      //         }
      //       }

      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: '',
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: [],
      //           type: '1',
      //           linkShare: [],
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     } else if (rs[i].type == 2) {
      //       const musicContent = rs[i].musicContent;
      //       const musicContentData = JSON.parse(musicContent);
      //       // const shareUrl = musicContentData['shareUrl']
      //       // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
      //       // if (musicInfo && musicInfo['type']) {
      //       //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      //       // }
      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let i = 0; i < imagesArr_.length; i++) {
      //           if (i === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
      //           }
      //         }
      //       }

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: '',
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: musicContentData,
      //           type: '2',
      //           linkShare: [],
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     } else if (rs[i].type == 3) {
      //       const linkShareContent = rs[i].linkshare;
      //       const linkShareContentData = JSON.parse(linkShareContent);
      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let i = 0; i < imagesArr_.length; i++) {
      //           if (i === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
      //           }
      //         }
      //       }

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: '',
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: [],
      //           type: '3',
      //           linkShare: linkShareContentData,
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     } else if (rs[i].type == 4) {
      //       let imageArrStr = '';
      //       if (rs[i].images) {
      //         const images = rs[i].images;
      //         const imagesArr_ = images.split(',');
      //         for (let j = 0; j < imagesArr_.length; j++) {
      //           if (j === imagesArr_.length - 1) {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
      //           } else {
      //             imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
      //           }
      //         }
      //       }

      //       let videoArrStr = '';
      //       if (rs[i].video) {
      //         const videos = rs[i].video;
      //         const videosArr_ = videos.split(',');
      //         for (let j = 0; j < videosArr_.length; j++) {
      //           if (j === videosArr_.length - 1) {
      //             videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
      //           } else {
      //             videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
      //           }
      //         }
      //       }

      //       const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //       if (likeRs && likeRs['type'] === 'error') {
      //         elogger.error('Content Error 6003 ' + likeRs['msg']);
      //         return {
      //           backSuccess: false,
      //           msg: 'Content Error 6003',
      //         };
      //       }
      //       const tagTypeId = rs[i]['tagType'];
      //       const tagType = contentTagType[tagTypeId].name;

      //       const contentInfo = {
      //         affair: {
      //           images: imageArrStr,
      //           video: videoArrStr,
      //           content: rs[i].content || '',
      //           likeRs: likeRs.length ? '1' : '0',
      //           dateline: `${rs[i].create_time}`,
      //           tagType: tagType || '0',
      //           aid: `${rs[i]['id']}`,
      //           imageType: rs[i].imageType,
      //           tagName: tagType || '',
      //           commentNum: `${rs[i].commentNum}`,
      //           likeNum: `${rs[i].likeNum}`,
      //           musicContent: [],
      //           type: '4',
      //           linkShare: [],
      //         },
      //         user: {
      //           gender: `${rs[i].gender}`,
      //           hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //           uid: `${rs[i].userid}`,
      //           nickname: rs[i].nickname,
      //           city: rs[i].city,
      //           avatar: AVATAR_WAILIAN + rs[i].avatar,
      //         },
      //       };
      //       const colorFont = yield self.getColorFont(rs[i].userid);
      //       contentInfo['user']['colorFont'] = colorFont;
      //       data.push(contentInfo);
      //     }
      //   }

      //   return {
      //     backSuccess: true,
      //     data: data,
      //     count: rsCount[0]['count'],
      //   };
      // } else {
      //   if (args['userid'] === 7) {
      //     const rs = yield mysqlInstance.getContentByTagV3(args);
      //     const rsCount = yield mysqlInstance.getContentCountByTag(args);
      //     if (rsCount && rsCount['type'] === 'error') {
      //       elogger.error('Content Error 110011 ' + rs['msg']);
      //       return {
      //         backSuccess: false,
      //         msg: 'Content Error 110011',
      //       };
      //     }
      //     const data = [];
      //     for (let i = 0; i < rs.length; i++) {
      //       if (rs[i].type == 1) {
      //         let imageArrStr = '';
      //         if (rs[i].images) {
      //           const images = rs[i].images;
      //           const imagesArr_ = images.split(',');
      //           for (let j = 0; j < imagesArr_.length; j++) {
      //             if (j === imagesArr_.length - 1) {
      //               imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
      //             } else {
      //               imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
      //             }
      //           }
      //         }

      //         const likeRs = rs[i].likeId;
      //         const tagTypeId = rs[i]['tagType'];
      //         const tagType = contentTagType[tagTypeId].name;

      //         const contentInfo = {
      //           affair: {
      //             images: imageArrStr,
      //             video: '',
      //             content: rs[i].content || '',
      //             likeRs: likeRs ? '1' : '0',
      //             dateline: `${rs[i].create_time}`,
      //             tagType: tagType || '0',
      //             aid: `${rs[i]['id']}`,
      //             imageType: rs[i].imageType,
      //             tagName: tagType || '',
      //             commentNum: `${rs[i].commentNum}`,
      //             likeNum: `${rs[i].likeNum}`,
      //             musicContent: [],
      //             linkShare: [],
      //             type: '1',
      //           },
      //           user: {
      //             gender: `${rs[i].gender}`,
      //             hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //             uid: `${rs[i].userid}`,
      //             nickname: rs[i].nickname,
      //             city: rs[i].city,
      //             avatar: AVATAR_WAILIAN + rs[i].avatar,
      //           },
      //         };
      //         const colorFont = yield self.getColorFont(rs[i].userid);
      //         contentInfo['user']['colorFont'] = colorFont;
      //         data.push(contentInfo);
      //       } else if (rs[i].type == 2) {
      //         const musicContent = rs[i].musicContent;
      //         const musicContentData = JSON.parse(musicContent);
      //         // const shareUrl = musicContentData['shareUrl']
      //         // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
      //         // if (musicInfo && musicInfo['type']) {
      //         //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
      //         // }
      //         const likeRs = rs[i].likeId;

      //         const tagTypeId = rs[i]['tagType'];
      //         const tagType = contentTagType[tagTypeId].name;

      //         let imageArrStr = '';
      //         if (rs[i].images) {
      //           const images = rs[i].images;
      //           const imagesArr_ = images.split(',');
      //           for (let i = 0; i < imagesArr_.length; i++) {
      //             if (i === imagesArr_.length - 1) {
      //               imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
      //             } else {
      //               imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
      //             }
      //           }
      //         }
      //         const contentInfo = {
      //           affair: {
      //             images: imageArrStr,
      //             video: '',
      //             content: rs[i].content || '',
      //             likeRs: likeRs ? '1' : '0',
      //             dateline: `${rs[i].create_time}`,
      //             tagType: tagType || '0',
      //             aid: `${rs[i]['id']}`,
      //             imageType: rs[i].imageType,
      //             tagName: tagType || '',
      //             commentNum: `${rs[i].commentNum}`,
      //             likeNum: `${rs[i].likeNum}`,
      //             musicContent: musicContentData,
      //             linkShare: [],
      //             type: '2',
      //           },
      //           user: {
      //             gender: `${rs[i].gender}`,
      //             hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //             uid: `${rs[i].userid}`,
      //             nickname: rs[i].nickname,
      //             city: rs[i].city,
      //             avatar: AVATAR_WAILIAN + rs[i].avatar,
      //           },
      //         };
      //         const colorFont = yield self.getColorFont(rs[i].userid);
      //         contentInfo['user']['colorFont'] = colorFont;
      //         data.push(contentInfo);
      //       } else if (rs[i].type == 3) {
      //         const linkShareContent = rs[i].linkshare;
      //         const linkShareContentData = JSON.parse(linkShareContent);
      //         const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //         if (likeRs && likeRs['type'] === 'error') {
      //           elogger.error('Content Error 6003 ' + likeRs['msg']);
      //           return {
      //             backSuccess: false,
      //             msg: 'Content Error 6003',
      //           };
      //         }
      //         const tagTypeId = rs[i]['tagType'];
      //         const tagType = contentTagType[tagTypeId].name;

      //         let imageArrStr = '';
      //         if (rs[i].images) {
      //           const images = rs[i].images;
      //           const imagesArr_ = images.split(',');
      //           for (let i = 0; i < imagesArr_.length; i++) {
      //             if (i === imagesArr_.length - 1) {
      //               imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
      //             } else {
      //               imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
      //             }
      //           }
      //         }

      //         const contentInfo = {
      //           affair: {
      //             images: imageArrStr,
      //             video: '',
      //             content: rs[i].content || '',
      //             likeRs: likeRs.length ? '1' : '0',
      //             dateline: `${rs[i].create_time}`,
      //             tagType: tagType || '0',
      //             aid: `${rs[i]['id']}`,
      //             imageType: rs[i].imageType,
      //             tagName: tagType || '',
      //             commentNum: `${rs[i].commentNum}`,
      //             likeNum: `${rs[i].likeNum}`,
      //             musicContent: [],
      //             type: '3',
      //             linkShare: linkShareContentData,
      //           },
      //           user: {
      //             gender: `${rs[i].gender}`,
      //             hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //             uid: `${rs[i].userid}`,
      //             nickname: rs[i].nickname,
      //             city: rs[i].city,
      //             avatar: AVATAR_WAILIAN + rs[i].avatar,
      //           },
      //         };
      //         const colorFont = yield self.getColorFont(rs[i].userid);
      //         contentInfo['user']['colorFont'] = colorFont;
      //         data.push(contentInfo);
      //       } else if (rs[i].type == 4) {
      //         let imageArrStr = '';
      //         if (rs[i].images) {
      //           const images = rs[i].images;
      //           const imagesArr_ = images.split(',');
      //           for (let j = 0; j < imagesArr_.length; j++) {
      //             if (j === imagesArr_.length - 1) {
      //               imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
      //             } else {
      //               imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
      //             }
      //           }
      //         }

      //         let videoArrStr = '';
      //         if (rs[i].video) {
      //           const videos = rs[i].video;
      //           const videosArr_ = videos.split(',');
      //           for (let j = 0; j < videosArr_.length; j++) {
      //             if (j === videosArr_.length - 1) {
      //               videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
      //             } else {
      //               videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
      //             }
      //           }
      //         }

      //         const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
      //         if (likeRs && likeRs['type'] === 'error') {
      //           elogger.error('Content Error 6003 ' + likeRs['msg']);
      //           return {
      //             backSuccess: false,
      //             msg: 'Content Error 6003',
      //           };
      //         }
      //         const tagTypeId = rs[i]['tagType'];
      //         const tagType = contentTagType[tagTypeId].name;

      //         const contentInfo = {
      //           affair: {
      //             images: imageArrStr,
      //             video: videoArrStr,
      //             content: rs[i].content || '',
      //             likeRs: likeRs.length ? '1' : '0',
      //             dateline: `${rs[i].create_time}`,
      //             tagType: tagType || '0',
      //             aid: `${rs[i]['id']}`,
      //             imageType: rs[i].imageType,
      //             tagName: tagType || '',
      //             commentNum: `${rs[i].commentNum}`,
      //             likeNum: `${rs[i].likeNum}`,
      //             musicContent: [],
      //             type: '4',
      //             linkShare: [],
      //           },
      //           user: {
      //             gender: `${rs[i].gender}`,
      //             hertz: `${self.returnFloat(rs[i]['hertz'])}`,
      //             uid: `${rs[i].userid}`,
      //             nickname: rs[i].nickname,
      //             city: rs[i].city,
      //             avatar: AVATAR_WAILIAN + rs[i].avatar,
      //           },
      //         };
      //         const colorFont = yield self.getColorFont(rs[i].userid);
      //         contentInfo['user']['colorFont'] = colorFont;
      //         data.push(contentInfo);
      //       }
      //     }
      //     const redislist = yield redisInstance.lpush(`LIST:USER:THEME:${args['userid']}`, args['tagid']);
      //     if (parseInt(redislist) == 30) {
      //       yield redisInstance.rpop(`LIST:USER:THEME:${args['userid']}`);
      //     }
      //     return {
      //       backSuccess: true,
      //       data: data,
      //       count: rsCount[0]['count'],
      //     };
      //   } else {
      const rs = yield mysqlInstance.getContentByTag(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 11001 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 11001',
        };
      }

      const rsCount = yield mysqlInstance.getContentCountByTag(args);
      if (rsCount && rsCount['type'] === 'error') {
        elogger.error('Content Error 110011 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 110011',
        };
      }

      const data = [];
      for (let i = 0; i < rs.length; i++) {
        if (rs[i].type == 1) {
          let imageArrStr = '';
          if (rs[i].images) {
            const images = rs[i].images;
            const imagesArr_ = images.split(',');
            for (let j = 0; j < imagesArr_.length; j++) {
              if (j === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
              }
            }
          }

          const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
          if (likeRs && likeRs['type'] === 'error') {
            elogger.error('Content Error 6003 ' + likeRs['msg']);
            return {
              backSuccess: false,
              msg: 'Content Error 6003',
            };
          }
          const tagTypeId = rs[i]['tagType'];
          const tagType = contentTagType[tagTypeId].name;

          // const contentInfo = {
          //     contentid: `${rs[i]['id']}`,
          //     authorid: `${rs[i].userid}`,
          //     avatar: AVATAR_WAILIAN + rs[i].avatar,
          //     nickName: rs[i].nickname,
          //     signature: rs[i].signature,
          //     gender: rs[i].gender == 1 ? '男' : '女',
          //     city: rs[i].city,
          //     images: imageArrStr,
          //     imageType: rs[i].imageType,
          //     content: rs[i].content || '',
          //     createTime: `${rs[i].create_time}`,
          //     likeNum: `${rs[i].likeNum}`,
          //     commentNum: `${rs[i].commentNum}`,
          //     likeRs: likeRs.length ? '1' : '0',
          //     tagType: tagType || ''
          // }

          const contentInfo = {
            affair: {
              images: imageArrStr,
              video: '',
              content: rs[i].content || '',
              likeRs: likeRs.length ? '1' : '0',
              dateline: `${rs[i].create_time}`,
              tagType: tagType || '0',
              aid: `${rs[i]['id']}`,
              imageType: rs[i].imageType,
              tagName: tagType || '',
              commentNum: `${rs[i].commentNum}`,
              likeNum: `${rs[i].likeNum}`,
              musicContent: [],
              linkShare: [],
              type: '1',
            },
            user: {
              gender: `${rs[i].gender}`,
              hertz: `${self.returnFloat(rs[i]['hertz'])}`,
              uid: `${rs[i].userid}`,
              nickname: rs[i].nickname,
              city: rs[i].city,
              avatar: AVATAR_WAILIAN + rs[i].avatar,
            },
          };
          const colorFont = yield self.getColorFont(rs[i].userid);
          contentInfo['user']['colorFont'] = colorFont;
          data.push(contentInfo);
        } else if (rs[i].type == 2) {
          const musicContent = rs[i].musicContent;
          const musicContentData = JSON.parse(musicContent);
          // const shareUrl = musicContentData['shareUrl']
          // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
          // if (musicInfo && musicInfo['type']) {
          //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
          // }
          const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
          if (likeRs && likeRs['type'] === 'error') {
            elogger.error('Content Error 6003 ' + likeRs['msg']);
            return {
              backSuccess: false,
              msg: 'Content Error 6003',
            };
          }
          const tagTypeId = rs[i]['tagType'];
          const tagType = contentTagType[tagTypeId].name;

          let imageArrStr = '';
          if (rs[i].images) {
            const images = rs[i].images;
            const imagesArr_ = images.split(',');
            for (let i = 0; i < imagesArr_.length; i++) {
              if (i === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
              }
            }
          }

          // const contentInfo = {
          //     contentid: `${rs[i]['id']}`,
          //     authorid: `${rs[i].userid}`,
          //     avatar: AVATAR_WAILIAN + rs[i].avatar,
          //     nickName: rs[i].nickname,
          //     signature: rs[i].signature,
          //     gender: rs[i].gender == 1 ? '男' : '女',
          //     city: rs[i].city,
          //     images: imageArrStr,
          //     imageType: rs[i].imageType,
          //     content: rs[i].content || '',
          //     createTime: `${rs[i].create_time}`,
          //     likeNum: `${rs[i].likeNum}`,
          //     commentNum: `${rs[i].commentNum}`,
          //     likeRs: likeRs.length ? '1' : '0',
          //     tagType: tagType || ''
          // }

          const contentInfo = {
            affair: {
              images: imageArrStr,
              video: '',
              content: rs[i].content || '',
              likeRs: likeRs.length ? '1' : '0',
              dateline: `${rs[i].create_time}`,
              tagType: tagType || '0',
              aid: `${rs[i]['id']}`,
              imageType: rs[i].imageType,
              tagName: tagType || '',
              commentNum: `${rs[i].commentNum}`,
              likeNum: `${rs[i].likeNum}`,
              musicContent: musicContentData,
              linkShare: [],
              type: '2',
            },
            user: {
              gender: `${rs[i].gender}`,
              hertz: `${self.returnFloat(rs[i]['hertz'])}`,
              uid: `${rs[i].userid}`,
              nickname: rs[i].nickname,
              city: rs[i].city,
              avatar: AVATAR_WAILIAN + rs[i].avatar,
            },
          };
          const colorFont = yield self.getColorFont(rs[i].userid);
          contentInfo['user']['colorFont'] = colorFont;
          data.push(contentInfo);
        } else if (rs[i].type == 3) {
          const linkShareContent = rs[i].linkshare;
          const linkShareContentData = JSON.parse(linkShareContent);
          const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
          if (likeRs && likeRs['type'] === 'error') {
            elogger.error('Content Error 6003 ' + likeRs['msg']);
            return {
              backSuccess: false,
              msg: 'Content Error 6003',
            };
          }
          const tagTypeId = rs[i]['tagType'];
          const tagType = contentTagType[tagTypeId].name;

          let imageArrStr = '';
          if (rs[i].images) {
            const images = rs[i].images;
            const imagesArr_ = images.split(',');
            for (let i = 0; i < imagesArr_.length; i++) {
              if (i === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
              }
            }
          }

          const contentInfo = {
            affair: {
              images: imageArrStr,
              video: '',
              content: rs[i].content || '',
              likeRs: likeRs.length ? '1' : '0',
              dateline: `${rs[i].create_time}`,
              tagType: tagType || '0',
              aid: `${rs[i]['id']}`,
              imageType: rs[i].imageType,
              tagName: tagType || '',
              commentNum: `${rs[i].commentNum}`,
              likeNum: `${rs[i].likeNum}`,
              musicContent: [],
              type: '3',
              linkShare: linkShareContentData,
            },
            user: {
              gender: `${rs[i].gender}`,
              hertz: `${self.returnFloat(rs[i]['hertz'])}`,
              uid: `${rs[i].userid}`,
              nickname: rs[i].nickname,
              city: rs[i].city,
              avatar: AVATAR_WAILIAN + rs[i].avatar,
            },
          };
          const colorFont = yield self.getColorFont(rs[i].userid);
          contentInfo['user']['colorFont'] = colorFont;
          data.push(contentInfo);
        } else if (rs[i].type == 4) {
          let imageArrStr = '';
          if (rs[i].images) {
            const images = rs[i].images;
            const imagesArr_ = images.split(',');
            for (let j = 0; j < imagesArr_.length; j++) {
              if (j === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
              }
            }
          }

          let videoArrStr = '';
          if (rs[i].video) {
            const videos = rs[i].video;
            const videosArr_ = videos.split(',');
            for (let j = 0; j < videosArr_.length; j++) {
              if (j === videosArr_.length - 1) {
                videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
              } else {
                videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
              }
            }
          }

          const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
          if (likeRs && likeRs['type'] === 'error') {
            elogger.error('Content Error 6003 ' + likeRs['msg']);
            return {
              backSuccess: false,
              msg: 'Content Error 6003',
            };
          }
          const tagTypeId = rs[i]['tagType'];
          const tagType = contentTagType[tagTypeId].name;

          const contentInfo = {
            affair: {
              images: imageArrStr,
              video: videoArrStr,
              content: rs[i].content || '',
              likeRs: likeRs.length ? '1' : '0',
              dateline: `${rs[i].create_time}`,
              tagType: tagType || '0',
              aid: `${rs[i]['id']}`,
              imageType: rs[i].imageType,
              tagName: tagType || '',
              commentNum: `${rs[i].commentNum}`,
              likeNum: `${rs[i].likeNum}`,
              musicContent: [],
              type: '4',
              linkShare: [],
            },
            user: {
              gender: `${rs[i].gender}`,
              hertz: `${self.returnFloat(rs[i]['hertz'])}`,
              uid: `${rs[i].userid}`,
              nickname: rs[i].nickname,
              city: rs[i].city,
              avatar: AVATAR_WAILIAN + rs[i].avatar,
            },
          };
          const colorFont = yield self.getColorFont(rs[i].userid);
          contentInfo['user']['colorFont'] = colorFont;
          data.push(contentInfo);
        }
      }
      const redislist = yield redisInstance.lpush(`LIST:USER:THEME:${args['userid']}`, args['tagid']);
      if (parseInt(redislist) == 30) {
        yield redisInstance.rpop(`LIST:USER:THEME:${args['userid']}`);
      }
      return {
        backSuccess: true,
        data: data,
        count: rsCount[0]['count'],
      };
      // }
      // }
    });
  }

  getContentByTagNew(args) {
    const self = this;
    return co(function* () {
      try {
        const rs = yield mysqlInstance.getContentByTagNew(args);
        if (rs && rs['type'] === 'error') {
          elogger.error('Content Error 11001 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 11001',
          };
        }

        const rsCount = yield mysqlInstance.getContentCountByTag(args);
        if (rsCount && rsCount['type'] === 'error') {
          elogger.error('Content Error 110011 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 110011',
          };
        }
        const aidArr = [];
        for (let i = 0; i < rs.length; i++) {
          const aid = rs[i]['id'];
          aidArr.push(aid);
        }
        const likeArr = [];
        const likeRs2 = yield mysqlInstance.searchLikeFunV2(args['userid'], aidArr);
        for (let i = 0; i < likeRs2.length; i++) {
          likeArr.push(likeRs2[i]['id']);
        }

        const data = [];
        for (let i = 0; i < rs.length; i++) {
          if (rs[i].type == 1) {
            let imageArrStr = '';
            if (rs[i].images) {
              const images = rs[i].images;
              const imagesArr_ = images.split(',');
              for (let j = 0; j < imagesArr_.length; j++) {
                if (j === imagesArr_.length - 1) {
                  imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
                } else {
                  imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
                }
              }
            }

            // const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
            // if (likeRs && likeRs['type'] === 'error') {
            //   elogger.error('Content Error 6003 ' + likeRs['msg']);
            //   return {
            //     backSuccess: false,
            //     msg: 'Content Error 6003',
            //   };
            // }
            const tagTypeId = rs[i]['tagType'];
            const tagType = contentTagType[tagTypeId].name;

            const contentInfo = {
              affair: {
                images: imageArrStr,
                video: '',
                content: rs[i].content || '',
                likeRs: likeArr.includes(rs[i]['id']) ? '1' : '0',
                dateline: `${rs[i].create_time}`,
                tagType: tagType || '0',
                aid: `${rs[i]['id']}`,
                imageType: rs[i].imageType,
                tagName: tagType || '',
                commentNum: `${rs[i].commentNum}`,
                likeNum: `${rs[i].likeNum}`,
                musicContent: [],
                linkShare: [],
                type: '1',
              },
              user: {
                gender: `${rs[i].gender}`,
                hertz: `${self.returnFloat(rs[i]['hertz'])}`,
                uid: `${rs[i].userid}`,
                nickname: rs[i].nickname,
                city: rs[i].city,
                avatar: AVATAR_WAILIAN + rs[i].avatar,
              },
            };
            const colorFont = yield self.getColorFont(rs[i].userid);
            contentInfo['user']['colorFont'] = colorFont;
            data.push(contentInfo);
          } else if (rs[i].type == 2) {
            const musicContent = rs[i].musicContent;
            const musicContentData = JSON.parse(musicContent);
            // const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
            // if (likeRs && likeRs['type'] === 'error') {
            //   elogger.error('Content Error 6003 ' + likeRs['msg']);
            //   return {
            //     backSuccess: false,
            //     msg: 'Content Error 6003',
            //   };
            // }
            const tagTypeId = rs[i]['tagType'];
            const tagType = contentTagType[tagTypeId].name;

            let imageArrStr = '';
            if (rs[i].images) {
              const images = rs[i].images;
              const imagesArr_ = images.split(',');
              for (let i = 0; i < imagesArr_.length; i++) {
                if (i === imagesArr_.length - 1) {
                  imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
                } else {
                  imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
                }
              }
            }
            const contentInfo = {
              affair: {
                images: imageArrStr,
                video: '',
                content: rs[i].content || '',
                likeRs: likeArr.includes(rs[i]['id']) ? '1' : '0',
                dateline: `${rs[i].create_time}`,
                tagType: tagType || '0',
                aid: `${rs[i]['id']}`,
                imageType: rs[i].imageType,
                tagName: tagType || '',
                commentNum: `${rs[i].commentNum}`,
                likeNum: `${rs[i].likeNum}`,
                musicContent: musicContentData,
                linkShare: [],
                type: '2',
              },
              user: {
                gender: `${rs[i].gender}`,
                hertz: `${self.returnFloat(rs[i]['hertz'])}`,
                uid: `${rs[i].userid}`,
                nickname: rs[i].nickname,
                city: rs[i].city,
                avatar: AVATAR_WAILIAN + rs[i].avatar,
              },
            };
            const colorFont = yield self.getColorFont(rs[i].userid);
            contentInfo['user']['colorFont'] = colorFont;
            data.push(contentInfo);
          } else if (rs[i].type == 3) {
            const linkShareContent = rs[i].linkshare;
            const linkShareContentData = JSON.parse(linkShareContent);
            // const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
            // if (likeRs && likeRs['type'] === 'error') {
            //   elogger.error('Content Error 6003 ' + likeRs['msg']);
            //   return {
            //     backSuccess: false,
            //     msg: 'Content Error 6003',
            //   };
            // }
            const tagTypeId = rs[i]['tagType'];
            const tagType = contentTagType[tagTypeId].name;

            let imageArrStr = '';
            if (rs[i].images) {
              const images = rs[i].images;
              const imagesArr_ = images.split(',');
              for (let i = 0; i < imagesArr_.length; i++) {
                if (i === imagesArr_.length - 1) {
                  imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
                } else {
                  imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
                }
              }
            }

            const contentInfo = {
              affair: {
                images: imageArrStr,
                video: '',
                content: rs[i].content || '',
                likeRs: likeArr.includes(rs[i]['id']) ? '1' : '0',
                dateline: `${rs[i].create_time}`,
                tagType: tagType || '0',
                aid: `${rs[i]['id']}`,
                imageType: rs[i].imageType,
                tagName: tagType || '',
                commentNum: `${rs[i].commentNum}`,
                likeNum: `${rs[i].likeNum}`,
                musicContent: [],
                type: '3',
                linkShare: linkShareContentData,
              },
              user: {
                gender: `${rs[i].gender}`,
                hertz: `${self.returnFloat(rs[i]['hertz'])}`,
                uid: `${rs[i].userid}`,
                nickname: rs[i].nickname,
                city: rs[i].city,
                avatar: AVATAR_WAILIAN + rs[i].avatar,
              },
            };
            const colorFont = yield self.getColorFont(rs[i].userid);
            contentInfo['user']['colorFont'] = colorFont;
            data.push(contentInfo);
          } else if (rs[i].type == 4) {
            let imageArrStr = '';
            if (rs[i].images) {
              const images = rs[i].images;
              const imagesArr_ = images.split(',');
              for (let j = 0; j < imagesArr_.length; j++) {
                if (j === imagesArr_.length - 1) {
                  imageArrStr += `${WAILIAN}${imagesArr_[j]}`;
                } else {
                  imageArrStr += `${WAILIAN}${imagesArr_[j]},`;
                }
              }
            }

            let videoArrStr = '';
            if (rs[i].video) {
              const videos = rs[i].video;
              const videosArr_ = videos.split(',');
              for (let j = 0; j < videosArr_.length; j++) {
                if (j === videosArr_.length - 1) {
                  videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
                } else {
                  videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
                }
              }
            }

            // const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], rs[i]['id']);
            // if (likeRs && likeRs['type'] === 'error') {
            //   elogger.error('Content Error 6003 ' + likeRs['msg']);
            //   return {
            //     backSuccess: false,
            //     msg: 'Content Error 6003',
            //   };
            // }
            const tagTypeId = rs[i]['tagType'];
            const tagType = contentTagType[tagTypeId].name;

            const contentInfo = {
              affair: {
                images: imageArrStr,
                video: videoArrStr,
                content: rs[i].content || '',
                likeRs: likeArr.includes(rs[i]['id']) ? '1' : '0',
                dateline: `${rs[i].create_time}`,
                tagType: tagType || '0',
                aid: `${rs[i]['id']}`,
                imageType: rs[i].imageType,
                tagName: tagType || '',
                commentNum: `${rs[i].commentNum}`,
                likeNum: `${rs[i].likeNum}`,
                musicContent: [],
                type: '4',
                linkShare: [],
              },
              user: {
                gender: `${rs[i].gender}`,
                hertz: `${self.returnFloat(rs[i]['hertz'])}`,
                uid: `${rs[i].userid}`,
                nickname: rs[i].nickname,
                city: rs[i].city,
                avatar: AVATAR_WAILIAN + rs[i].avatar,
              },
            };
            const colorFont = yield self.getColorFont(rs[i].userid);
            contentInfo['user']['colorFont'] = colorFont;
            data.push(contentInfo);
          }
        }
        const redislist = yield redisInstance.lpush(`LIST:USER:THEME:${args['userid']}`, args['tagid']);
        if (parseInt(redislist) == 30) {
          yield redisInstance.rpop(`LIST:USER:THEME:${args['userid']}`);
        }

        const contentIds = [];
        for (let i = 0; i < data.length; i++) {
          contentIds.push(data[i].affair.aid);
        }
        const commentRs = yield mysqlInstance.getCommentByIds(args['userid'], contentIds);
        const commentIdArr = Array.from(commentRs).map((item) => item.contentid);
        const commentSet = new Set(commentIdArr);
        for (let i = 0; i < data.length; i++) {
          const isComment = commentSet.has(parseInt(data[i].affair.aid)) ? '1' : '0';
          data[i].affair['commentRs'] = isComment;
        }

        return {
          backSuccess: true,
          data: data,
          count: rsCount[0]['count'],
          lastId: rs.length === 20 ? rs[rs.length - 1]['id'] : 0,
        };
      } catch (error) {
        console.log(error);
      }
    });
  }

  delContent(args) {
    const self = this;
    return co(function* () {
      const contentRs = yield mysqlInstance.searchContent(args['contentid'], args['userid']);
      if (contentRs && contentRs['type'] === 'error') {
        elogger.error('Content Error 2001 ' + contentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 2001',
        };
      }
      if (!contentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 2002 没有此条动态',
        };
      }
      const rs = yield mysqlInstance.delContent(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 2003 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 2003',
        };
      }

      const contentCount = yield mysqlInstance.getContentCount(args['userid']);
      const count = contentCount[0].count;

      if (count == 0) {
        yield mysqlInstance.updateUserContentStatus(args['userid'], 0);
      }

      const redisRs = yield redisInstance.hashDel('HASH:CONTENT:INFO:' + args['contentid']);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:CONTENT:INFO:>>${redisRs['msg']},contentid>>${args['contentid']}`);
      }
      const key = 'ZSET:USER:CONTENT:' + args['userid'];
      const userArrRs = yield redisInstance.zsetDel(key, args['contentid']);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:USER:CONTENT:>>${userArrRs['msg']},contentid>>${args['contentid']}`);
      }

      return {
        backSuccess: true,
      };
    });
  }

  putContentLike(args) {
    const self = this;
    return co(function* () {
      let STATUS = 0;
      let NICKNAME = null;
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 3001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 3001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 3002 没有此条动态',
        };
      }
      const key = 'ZSET:CONTENT:LIKE:' + args['contentid'];
      const searchContentLikeRs = yield mysqlInstance.searchContentLike(args);
      if (!searchContentLikeRs.length) {
        const result = yield self.addLike(args);
        if (result && result['backSuccess']) {
          const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);
          const nickName = userInfo[0]['nickname'];
          NICKNAME = nickName;
          return {
            backSuccess: true,
            status: 0,
            nickName: NICKNAME,
          };
        } else {
          return {
            backSuccess: false,
            msg: result['msg'],
          };
        }
      } else {
        const status = searchContentLikeRs[0]['status'];
        if (status == 0) {
          const result = yield self.releaseLike(args);
          if (result && result['backSuccess']) {
            return {
              backSuccess: true,
              status: 1,
              nickName: 'NICKNAME',
            };
          } else {
            return {
              backSuccess: false,
              msg: result['msg'],
            };
          }
        } else {
          const result = yield self.addLike(args);
          if (result && result['backSuccess']) {
            return {
              backSuccess: true,
              status: 1,
              nickName: 'NICKNAME',
            };
          } else {
            return {
              backSuccess: false,
              msg: result['msg'],
            };
          }
        }
      }
      // const searchContentLikeRs = yield mysqlInstance.searchContentLike(args)
      // if (searchContentLikeRs && searchContentLikeRs['type'] === 'error') {
      //     elogger.error('Content Error 3003 ' + searchContentLikeRs['msg'])
      //     return {
      //         backSuccess: false,
      //         msg: 'Content Error 3003'
      //     }
      // }
      // if (!searchContentLikeRs.length) {
      //     const rs = yield mysqlInstance.putContentLike(args);
      //     if (rs && rs['type'] === 'error') {
      //         elogger.error('Content Error 3004 ' + rs['msg'])
      //         return {
      //             backSuccess: false,
      //             msg: 'Content Error 3004'
      //         }
      //     }
      //     yield mysqlInstance.updateContentLikeNum(args['contentid'], 1)
      //     const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid'])
      //     let likeNum = redisContentRs['likeNum'] || 0;
      //     likeNum = parseInt(likeNum) + 1;

      //     let likeUserArr = redisContentRs['likeUserArr'];
      //     if (likeUserArr) {
      //         likeUserArr = likeUserArr.split(',');
      //     } else {
      //         likeUserArr = [];
      //     }
      //     likeUserArr.push(`${args['userid']}`);
      //     const newSet = new Set();
      //     for (let i = 0; i < likeUserArr.length; i++) {
      //         newSet.add(likeUserArr[i]);
      //     }
      //     likeUserArr = Array.from(newSet);
      //     likeUserArr = likeUserArr.join(',')

      //     yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['likeNum', likeNum, 'likeUserArr', likeUserArr]);
      //     //更新动态点赞数

      //     // const score = (yield redisInstance.zsetScoreGet(key)).length;
      //     // const redisRs = yield redisInstance.zsetAdd(key, score, args['userid'])
      //     // if (redisRs && redisRs['type'] === 'error') {
      //     //     elogger.error(`ZSET:CONTENT:LIKE:>>${redisRs['msg']},contentid>>${args['contentid']}`)
      //     // }

      //     const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid'])
      //     const nickName = userInfo[0]['nickname'];
      //     NICKNAME = nickName;
      //     // const data = {
      //     //     userid: args['authorid'],
      //     //     text: `${nickName} 赞了你的动态`
      //     // }
      //     // const sendMsgRs = yield imService.sendMsg(data)
      //     // if (!sendMsgRs['backSuccess']) {
      //     // }
      //     if (parseInt(args['authorid']) !== parseInt(args['userid'])) {
      //         const contentRs = searchContentRs[0]['content'];
      //         let content = '';
      //         if (contentRs) {
      //             content = contentRs.slice(0, 50);
      //         }
      //         const images = searchContentRs[0]['images'];
      //         let image = '';
      //         if (images) {
      //             const imagesArr = images.split(',');
      //             image = imagesArr[0]
      //         }

      //         const notificationData = {
      //             userid: args['authorid'],
      //             text: ' 赞了你的动态',
      //             avatar: userInfo[0]['avatar'],
      //             nickname: userInfo[0]['nickname'],
      //             type: 1,
      //             contentid: args['contentid'],
      //             content: content,
      //             images: image
      //         }
      //         notificationData['content'] = self.toLiteral(notificationData['content']);
      //         const insertIntoNotification = yield mysqlInstance.insertIntoNotification(notificationData)
      //         const redislist = yield redisInstance.lpush(`LIST:USER:LIKE:${args['userid']}`, args['authorid'])
      //         if (parseInt(redislist) == 30) {
      //             yield redisInstance.rpop(`LIST:USER:LIKE:${args['userid']}`)
      //         }
      //     }

      //     return {
      //         backSuccess: true,
      //         status: 0,
      //         nickName: NICKNAME
      //     }
      // } else {
      //     let status = searchContentLikeRs[0]['status'];
      //     if (status == 0) {
      //         status = 1;
      //         STATUS = status;
      //         yield mysqlInstance.updateContentLikeNum(args['contentid'], -1);
      //         const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid']);
      //         let likeNum = redisContentRs['likeNum'] || 0;
      //         if (parseInt(likeNum) > 0) {
      //             likeNum = parseInt(likeNum) - 1;
      //         }

      //         let likeUserArr = redisContentRs['likeUserArr'];
      //         if (likeUserArr) {
      //             likeUserArr = likeUserArr.split(',');
      //         } else {
      //             likeUserArr = [];
      //         }
      //         const index = likeUserArr.indexOf(`${args['userid']}`);
      //         if (index > -1) {
      //             likeUserArr.splice(index, 1);
      //         }

      //         likeUserArr = likeUserArr.join(',')

      //         yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['likeNum', likeNum, 'likeUserArr', likeUserArr]);
      //         args['status'] = status
      //         const rs = yield mysqlInstance.updateContentLike(args)
      //         if (rs && rs['type'] === 'error') {
      //             elogger.error('Content Error 3005 ' + rs['msg'])
      //             return {
      //                 backSuccess: false,
      //                 msg: 'Content Error 3005'
      //             }
      //         }
      //         // const redisRs = yield redisInstance.zsetDel(key, args['userid'])
      //         // if (redisRs && redisRs['type'] === 'error') {
      //         //     elogger.error(`ZSET:CONTENT:LIKE:>>${redisRs['msg']},contentid>>${args['contentid']}`)
      //         // }
      //     } else {
      //         status = 0;
      //         yield mysqlInstance.updateContentLikeNum(args['contentid'], 1)
      //         const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid'])
      //         let likeNum = redisContentRs['likeNum'] || 0;
      //         likeNum = parseInt(likeNum) + 1;
      //         let likeUserArr = redisContentRs['likeUserArr'];
      //         if (likeUserArr) {
      //             likeUserArr = likeUserArr.split(',');
      //         } else {
      //             likeUserArr = [];
      //         }
      //         likeUserArr.push(`${args['userid']}`);
      //         const newSet = new Set();
      //         for (let i = 0; i < likeUserArr.length; i++) {
      //             newSet.add(likeUserArr[i]);
      //         }
      //         likeUserArr = Array.from(newSet);
      //         likeUserArr = likeUserArr.join(',')

      //         yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['likeNum', likeNum, 'likeUserArr', likeUserArr]);
      //         // const score = yield redisInstance.zsetScoreGet(key);
      //         // const redisRs = yield redisInstance.zsetAdd(key, score, args['userid'])
      //         // if (redisRs && redisRs['type'] === 'error') {
      //         //     elogger.error(`ZSET:CONTENT:LIKE:>>${redisRs['msg']},contentid>>${args['contentid']}`)
      //         // }
      //         const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid'])
      //         const nickName = userInfo[0]['nickname']
      //         NICKNAME = nickName;
      //         STATUS = status;
      //         // const data = {
      //         //     userid: args['authorid'],
      //         //     text: `${nickName} 赞了你的动态`
      //         // }
      //         // const sendMsgRs = yield imService.sendMsg(data)
      //         // if (!sendMsgRs['backSuccess']) {
      //         // }
      //         args['status'] = status
      //         const rs = yield mysqlInstance.updateContentLike(args)
      //         if (rs && rs['type'] === 'error') {
      //             elogger.error('Content Error 3006 ' + rs['msg'])
      //             return {
      //                 backSuccess: false,
      //                 msg: 'Content Error 3006'
      //             }
      //         }
      //         if (parseInt(args['authorid']) !== parseInt(args['userid'])) {
      //             const contentRs = searchContentRs[0]['content'];
      //             let content = '';
      //             if (contentRs) {
      //                 content = contentRs.slice(0, 50);
      //             }
      //             const images = searchContentRs[0]['images'];
      //             let image = '';
      //             if (images) {
      //                 const imagesArr = images.split(',');
      //                 image = imagesArr[0]
      //             }

      //             const notificationData = {
      //                 userid: args['authorid'],
      //                 text: ' 赞了你的动态',
      //                 avatar: userInfo[0]['avatar'],
      //                 nickname: userInfo[0]['nickname'],
      //                 type: 1,
      //                 contentid: args['contentid'],
      //                 content: content,
      //                 images: image
      //             }
      //             const redislist = yield redisInstance.lpush(`LIST:USER:LIKE:${args['userid']}`, args['authorid'])
      //             if (parseInt(redislist) == 30) {
      //                 yield redisInstance.rpop(`LIST:USER:LIKE:${args['userid']}`)
      //             }
      //             // const insertIntoNotification = yield mysqlInstance.insertIntoNotification(notificationData)
      //         }
      //     }
      //     return {
      //         backSuccess: true,
      //         status: 1,
      //         nickName: NICKNAME
      //     }
      // }
    });
  }

  addLike(args) {
    const self = this;
    return co(function* () {
      let NICKNAME = null;
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 3001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 3001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 3002 没有此条动态',
        };
      }
      const searchContentLikeRs = yield mysqlInstance.searchContentLike(args);
      if (searchContentLikeRs && searchContentLikeRs['type'] === 'error') {
        elogger.error('Content Error 3003 ' + searchContentLikeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 3003',
        };
      }
      if (!searchContentLikeRs.length) {
        const rs = yield mysqlInstance.putContentLike(args);
        if (rs && rs['type'] === 'error') {
          elogger.error('Content Error 3004 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 3004',
          };
        }
        yield mysqlInstance.updateContentLikeNum(args['contentid'], 1);
        // const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid'])
        // let likeNum = redisContentRs['likeNum'] || 0;
        // likeNum = parseInt(likeNum) + 1;

        // let likeUserArr = redisContentRs['likeUserArr'];
        // if (likeUserArr) {
        //     likeUserArr = likeUserArr.split(',');
        // } else {
        //     likeUserArr = [];
        // }
        // likeUserArr.push(`${args['userid']}`);
        // const newSet = new Set();
        // for (let i = 0; i < likeUserArr.length; i++) {
        //     newSet.add(likeUserArr[i]);
        // }
        // likeUserArr = Array.from(newSet);
        // likeUserArr = likeUserArr.join(',')

        // yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['likeNum', likeNum, 'likeUserArr', likeUserArr]);

        const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);
        const nickName = userInfo[0]['nickname'];
        NICKNAME = nickName;

        if (parseInt(args['authorid']) !== parseInt(args['userid'])) {
          const contentRs = searchContentRs[0]['content'];
          let content = '';
          if (contentRs) {
            content = contentRs.slice(0, 50);
          }
          const images = searchContentRs[0]['images'];
          let image = '';
          if (images) {
            const imagesArr = images.split(',');
            image = imagesArr[0];
          }

          const notificationData = {
            userid: args['authorid'],
            text: ' 赞了你的动态',
            avatar: userInfo[0]['avatar'],
            nickname: userInfo[0]['nickname'],
            type: 1,
            contentid: args['contentid'],
            content: content,
            images: image,
          };
          notificationData['content'] = self.toLiteral(notificationData['content']);
          yield mysqlInstance.insertIntoNotification(notificationData);
          const redislist = yield redisInstance.lpush(`LIST:USER:LIKE:${args['userid']}`, args['authorid']);
          if (parseInt(redislist) == 30) {
            yield redisInstance.rpop(`LIST:USER:LIKE:${args['userid']}`);
          }
        }

        //存储今天点赞的人
        const likeUserKey = 'SET:LIKE:USER:' + args['userid'];
        yield redisInstance.setAdd(likeUserKey, args['authorid']);
        yield self.changeUserHertz(args['userid'], 2);
        return {
          backSuccess: true,
          status: 0,
          // nickName: NICKNAME
        };
      } else {
        const status = searchContentLikeRs[0].status;
        if (status == 0) {
          return {
            backSuccess: false,
            msg: '已点赞该动态',
          };
        } else {
          yield mysqlInstance.updateContentLikeNum(args['contentid'], 1);
          // const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid']);
          // let likeNum = redisContentRs['likeNum'] || 0;
          // likeNum = parseInt(likeNum) + 1;
          // let likeUserArr = redisContentRs['likeUserArr'];
          // if (likeUserArr) {
          //   likeUserArr = likeUserArr.split(',');
          // } else {
          //   likeUserArr = [];
          // }
          // likeUserArr.push(`${args['userid']}`);
          // const newSet = new Set();
          // for (let i = 0; i < likeUserArr.length; i++) {
          //   newSet.add(likeUserArr[i]);
          // }
          // likeUserArr = Array.from(newSet);
          // likeUserArr = likeUserArr.join(',');

          // yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], [
          //   'likeNum',
          //   likeNum,
          //   'likeUserArr',
          //   likeUserArr,
          // ]);

          args['status'] = 0;
          const rs = yield mysqlInstance.updateContentLike(args);
          if (rs && rs['type'] === 'error') {
            elogger.error('Content Error 3006 ' + rs['msg']);
            return {
              backSuccess: false,
              msg: 'Content Error 3006',
            };
          }
          yield self.changeUserHertz(args['userid'], 2);
          return {
            backSuccess: true,
            status: 1,
            nickName: '',
          };
        }
      }
    });
  }

  releaseLike(args) {
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 3001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 3001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 3002 没有此条动态',
        };
      }
      const searchContentLikeRs = yield mysqlInstance.searchContentLike(args);
      if (searchContentLikeRs && searchContentLikeRs['type'] === 'error') {
        elogger.error('Content Error 3003 ' + searchContentLikeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 3003',
        };
      }
      if (!searchContentLikeRs.length) {
        return {
          backSuccess: false,
          msg: '未点赞此条',
        };
      } else {
        yield mysqlInstance.updateContentLikeNum(args['contentid'], -1);
        // const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid']);
        // let likeNum = redisContentRs['likeNum'] || 0;
        // if (parseInt(likeNum) > 0) {
        //   likeNum = parseInt(likeNum) - 1;
        // }

        // let likeUserArr = redisContentRs['likeUserArr'];
        // if (likeUserArr) {
        //   likeUserArr = likeUserArr.split(',');
        // } else {
        //   likeUserArr = [];
        // }
        // const index = likeUserArr.indexOf(`${args['userid']}`);
        // if (index > -1) {
        //   likeUserArr.splice(index, 1);
        // }

        // likeUserArr = likeUserArr.join(',');

        // yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], [
        //   'likeNum',
        //   parseInt(likeNum),
        //   'likeUserArr',
        //   likeUserArr,
        // ]);

        // yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['likeNum', parseInt(likeNum)]);
        args['status'] = 1;
        const rs = yield mysqlInstance.updateContentLike(args);
        if (rs && rs['type'] === 'error') {
          elogger.error('Content Error 3005 ' + rs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 3005',
          };
        }
        return {
          backSuccess: true,
          data: '',
        };
      }
    });
  }

  putContentComment(args) {
    const self = this;
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 4001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 4001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 4002 没有此条动态',
        };
      }
      args['comment'] = self.toLiteral(args['comment']);
      const rs = yield mysqlInstance.putContentComment(args);
      if (rs && rs['type'] === 'error') {
        elogger.error('Content Error 4003 ' + rs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 4003',
        };
      }

      const commentid = rs;
      const redisRs = yield redisInstance.hashAdd('HASH:COMMENT:INFO:' + commentid, [
        'userid',
        args['userid'],
        'authorid',
        args['authorid'],
        'contentid',
        args['contentid'],
        'comment',
        args['comment'],
        'to_userid',
        args['to_userid'],
        'to_commentid',
        args['to_commentid'],
        'belongs',
        args['belongs'],
        'create_time',
        args['time'],
      ]);
      if (redisRs && redisRs['type'] === 'error') {
        elogger.error(`HASH:COMMENT:INFO:>>${redisRs['msg']},commentid>>${commentid}`);
      }
      const key = 'ZSET:CONTENT:COMMENT:' + args['contentid'];
      const score = yield redisInstance.zsetScoreGet(key);
      const userArrRs = yield redisInstance.zsetAdd(key, score, commentid);
      if (userArrRs && userArrRs['type'] === 'error') {
        elogger.error(`ZSET:CONTENT:COMMENT:>>${userArrRs['msg']},contentid>>${args['contentid']}`);
      }

      yield mysqlInstance.updateContentCommentNum(args['contentid'], +1);
      const userInfo = yield mysqlInstance.getUserInfoByUserid(args['userid']);
      const nickName = userInfo[0]['nickname'];
      const data = {
        userid: args['to_userid'],
        text: `${nickName} 评论了你`,
        from: 2,
        nickname: nickName,
      };

      if (args['belongs'] == 0 && parseInt(args['authorid']) !== parseInt(args['userid'])) {
        // const sendMsgRs = imService.sendMsg(data)

        // const key__ = 'HASH:USER:INFO:' + args['to_userid']
        // const redisRS__ = yield redisInstance.hashGet(key__);
        // const deviceToken = redisRS__['deviceToken'];

        // const key2__ = `STR:USER:PUSH:COUNT:${args['to_userid']}`
        // const redisRS2__ = yield redisInstance.get(key2__);
        // const pushCount = redisRS2__

        // const body__ = {
        //     text: `${nickName} 评论了你`,
        //     device_token: deviceToken,
        //     pushCount: pushCount,
        //     to_userid: args['to_userid']
        // }
        // const msgPush = youmengService.msgPush(body__)

        const contentRs = searchContentRs[0]['content'];
        let content = '';
        if (contentRs) {
          content = contentRs.slice(0, 50);
        }
        const images = searchContentRs[0]['images'];
        let image = '';
        if (images) {
          const imagesArr = images.split(',');
          image = imagesArr[0];
        }

        const notificationData = {
          userid: args['to_userid'],
          text: ' 评论了你',
          avatar: userInfo[0]['avatar'],
          nickname: userInfo[0]['nickname'],
          type: 2,
          contentid: args['contentid'],
          content: content,
          images: image,
        };
        notificationData['content'] = self.toLiteral(notificationData['content']);
        yield mysqlInstance.insertIntoNotification(notificationData);
        const redislist = yield redisInstance.lpush(`LIST:USER:CONMENT:${args['userid']}`, args['to_userid']);
        if (parseInt(redislist) == 30) {
          yield redisInstance.rpop(`LIST:USER:CONMENT:${args['userid']}`);
        }
      } else if (args['belongs'] !== 0 && parseInt(args['to_userid']) !== parseInt(args['userid'])) {
        // const sendMsgRs = imService.sendMsg(data)

        // const key__ = 'HASH:USER:INFO:' + args['to_userid']
        // const redisRS__ = yield redisInstance.hashGet(key__);
        // const deviceToken = redisRS__['deviceToken'];

        // const key2__ = `STR:USER:PUSH:COUNT:${args['to_userid']}`
        // const redisRS2__ = yield redisInstance.get(key2__);
        // const pushCount = redisRS2__

        // const body__ = {
        //     text: `${nickName} 评论了你`,
        //     device_token: deviceToken,
        //     pushCount: pushCount,
        //     to_userid: args['to_userid']
        // }
        // const msgPush = youmengService.msgPush(body__)

        const contentRs = searchContentRs[0]['content'];
        let content = '';
        if (contentRs) {
          content = contentRs.slice(0, 50);
        }
        const images = searchContentRs[0]['images'];
        let image = '';
        if (images) {
          const imagesArr = images.split(',');
          image = imagesArr[0];
        }

        const notificationData = {
          userid: args['to_userid'],
          text: ' 评论了你',
          avatar: userInfo[0]['avatar'],
          nickname: userInfo[0]['nickname'],
          type: 2,
          contentid: args['contentid'],
          content: content,
          images: image,
        };
        notificationData['content'] = self.toLiteral(notificationData['content']);
        yield mysqlInstance.insertIntoNotification(notificationData);
        const redislist = yield redisInstance.lpush(`LIST:USER:CONMENT:${args['userid']}`, args['to_userid']);
        if (parseInt(redislist) == 30) {
          yield redisInstance.rpop(`LIST:USER:CONMENT:${args['userid']}`);
        }
      }

      const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid']);
      let commentNum = 0; // 默认值设为 0

      // 如果 redisContentRs 存在才去获取 commentNum
      if (redisContentRs) {
        commentNum = parseInt(redisContentRs['commentNum'] || 0);
      }

      commentNum += 1; // 增加评论数
      yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['commentNum', commentNum]);
      return {
        backSuccess: true,
        data: rs,
      };
    });
  }

  delComment(args) {
    const self = this;
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 5001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 5001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 5002 没有此条动态',
        };
      }
      const searchCommentRs = yield mysqlInstance.searchComment(args['commentid']);
      if (searchCommentRs && searchCommentRs['type'] === 'error') {
        elogger.error('Content Error 5003 ' + searchCommentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 5003 ',
        };
      }
      if (!searchCommentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 5003 没有此条评论/回复',
        };
      }
      let delArrSet = new Set();
      delArrSet.add(args['commentid']);

      let total = 1;
      let to_commentidArr = [];
      to_commentidArr.push(args['commentid']);
      do {
        let commentSet = new Set();
        for (let i = 0; i < to_commentidArr.length; i++) {
          const searchToCommentRs = yield mysqlInstance.searchToCommentRs(to_commentidArr[i]);
          if (searchToCommentRs && searchToCommentRs['type'] === 'error') {
            elogger.error('Content Error 5004 ' + searchToCommentRs['msg']);
            return {
              backSuccess: false,
              msg: 'Content Error 5004',
            };
          }
          if (!searchToCommentRs.length) {
            continue;
          }
          for (let j = 0; j < searchToCommentRs.length; j++) {
            const commentid = searchToCommentRs[j]['id'];
            commentSet.add(commentid);
            delArrSet.add(commentid);
          }
        }
        const arr = Array.from(commentSet);
        if (!arr.length) {
          total = 0;
        }
        to_commentidArr = arr;
      } while (total != 0);
      const delArr = Array.from(delArrSet);
      for (let i = 0; i < delArr.length; i++) {
        const commentid = delArr[i];
        const delRs = yield mysqlInstance.delComment(commentid);
        if (delRs && delRs['type'] === 'error') {
          elogger.error('Content Error 5005 ' + delRs['msg']);
          return {
            backSuccess: false,
            msg: 'Content Error 5005',
          };
        }
        yield redisInstance.hashDel(`HASH:COMMENT:INFO:${commentid}`);
        yield redisInstance.zsetDel(`ZSET:CONTENT:COMMENT:${args['contentid']}`, commentid);
        yield mysqlInstance.updateContentCommentNum(args['contentid'], -1);
      }
      const redisContentRs = yield redisInstance.hashGet('HASH:CONTENT:INFO:' + args['contentid']);
      let commentNum = redisContentRs['commentNum'] || 0;
      commentNum = parseInt(commentNum) + 1;
      yield redisInstance.hashAdd('HASH:CONTENT:INFO:' + args['contentid'], ['commentNum', commentNum]);
      return {
        backSuccess: true,
        data: '删除成功',
      };
    });
  }

  newGetContentInfo(args) {
    const self = this;
    return co(function* () {
      let searchContentRs = [];
      if (args['userid'] === 7) {
        searchContentRs = yield mysqlInstance.searchContentInfoByIdV2(args['contentid']);
      } else {
        searchContentRs = yield mysqlInstance.searchContentInfoById(args['contentid']);
      }
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 6001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 6001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: '没有此条动态',
        };
      }

      // 处理图片URL
      let imageArrStr = '';
      if (searchContentRs[0].images) {
        const images = searchContentRs[0].images;
        const imagesArr_ = images.split(',');
        for (let i = 0; i < imagesArr_.length; i++) {
          if (i === imagesArr_.length - 1) {
            imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
          } else {
            imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
          }
        }
      }

      // 处理视频URL
      let videoArrStr = '';
      if (searchContentRs[0].video) {
        const videos = searchContentRs[0].video;
        const videosArr_ = videos.split(',');
        for (let j = 0; j < videosArr_.length; j++) {
          if (j === videosArr_.length - 1) {
            videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
          } else {
            videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
          }
        }
      }

      // 获取点赞和评论信息
      const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], args['contentid']);
      if (likeRs && likeRs['type'] === 'error') {
        elogger.error('Content Error 6003 ' + likeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 6003',
        };
      }

      const commentRs = yield mysqlInstance.searchCommentFun(args['userid'], args['contentid']);
      if (commentRs && commentRs['type'] === 'error') {
        elogger.error('Content Error 60034 ' + commentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 60034',
        };
      }

      // 根据不同类型处理特定内容
      let musicContentData = [];
      let linkShareContentData = [];
      if (searchContentRs[0].type == 2) {
        const musicContent = searchContentRs[0].musicContent;
        musicContentData = JSON.parse(musicContent);
      } else if (searchContentRs[0].type == 3) {
        const linkShareContent = searchContentRs[0].linkshare;
        linkShareContentData = JSON.parse(linkShareContent);
      }

      // 构建基础内容信息
      const contentInfo = {
        contentid: `${args['contentid']}`,
        status: `${searchContentRs[0].status}`,
        sponsor: `${searchContentRs[0].sponsor}`,
        top: `${searchContentRs[0].top}`,
        authorid: `${searchContentRs[0].authorid}`,
        avatar: AVATAR_WAILIAN + searchContentRs[0].avatar,
        nickName: searchContentRs[0].nickname,
        signature: searchContentRs[0].signature,
        gender: searchContentRs[0].gender == 1 ? '男' : '女',
        // city: searchContentRs[0].city == 'null' ? '北冰洋' : searchContentRs[0].city,
        city: searchContentRs[0].location == 'null' ? '北冰洋' : searchContentRs[0].location,
        images: imageArrStr,
        video: searchContentRs[0].type == 4 ? videoArrStr : '',
        hertz: `${self.returnFloat(searchContentRs[0].hertz)}`,
        imageType: searchContentRs[0].imageType,
        content: searchContentRs[0].content || '',
        createTime: `${searchContentRs[0].create_time}`,
        likeNum: `${searchContentRs[0].likeNum}`,
        commentNum: `${searchContentRs[0].commentNum}`,
        likeRs: likeRs.length ? '1' : '0',
        commentRs: commentRs.length ? '1' : '0',
        type: `${searchContentRs[0].type}`,
        musicContent: musicContentData,
        linkShare: linkShareContentData,
        timing: `${searchContentRs[0].timing}`,
        atUserIds: searchContentRs[0].atUserIds ? searchContentRs[0].atUserIds.split(',') : [],
      };

      // 获取用户颜色字体
      const colorFont = yield self.getColorFont(searchContentRs[0].authorid);
      contentInfo['colorFont'] = colorFont;

      // 更新浏览计数
      if (searchContentRs[0].authorid !== args['userid']) {
        const key = `SET:USER:BROWSE:${args['userid']}`;
        const browseRs = yield redisInstance.get(key);

        if (browseRs) {
          const value = `${parseInt(browseRs) + 1}`;
          yield redisInstance.onlySet(key, value);
        } else {
          yield redisInstance.onlySet(key, '1');
        }
      }

      return {
        backSuccess: true,
        data: contentInfo,
      };
    });
  }

  getContentInfo(args) {
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchContentInfo(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 7001 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 7001',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 7002 没有此条动态',
        };
      }
      let imageArrStr = '';
      if (searchContentRs[0].images) {
        const images = searchContentRs[0].images;
        const imagesArr_ = images.split(',');
        for (let i = 0; i < imagesArr_.length; i++) {
          if (i === imagesArr_.length - 1) {
            imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
          } else {
            imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
          }
        }
      }

      const likeRs = yield mysqlInstance.searchLikeFun(args['userid'], args['contentid']);
      if (likeRs && likeRs['type'] === 'error') {
        elogger.error('Content Error 7003 ' + likeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 7003',
        };
      }

      const contentInfo = {
        contentid: `${args['contentid']}`,
        authorid: `${args['authorid']}`,
        avatar: AVATAR_WAILIAN + searchContentRs[0].avatar,
        nickName: searchContentRs[0].nickname,
        signature: searchContentRs[0].signature,
        gender: searchContentRs[0].gender == 1 ? '男' : '女',
        // city: searchContentRs[0].city,
        city: searchContentRs[0].location == 'null' ? '北冰洋' : searchContentRs[0].location,
        images: imageArrStr,
        hertz: `${self.returnFloat(searchContentRs[0].hertz)}`,
        imageType: searchContentRs[0].imageType,
        content: searchContentRs[0].content || '',
        createTime: `${searchContentRs[0].create_time}`,
        likeNum: `${searchContentRs[0].likeNum}`,
        commentNum: `${searchContentRs[0].commentNum}`,
        likeRs: likeRs.length ? '1' : '0',
      };

      if (args['userid'] !== args['authorid']) {
        const key = `SET:USER:BROWSE:${args['userid']}`;
        const browseRs = yield redisInstance.get(key);

        if (browseRs) {
          const value = `${parseInt(browseRs) + 1}`;
          yield redisInstance.onlySet(key, value);
        } else {
          yield redisInstance.onlySet(key, '1');
        }
      }

      return {
        backSuccess: true,
        data: contentInfo,
      };
    });
  }

  getContentLike(args) {
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 8003 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 8003',
        };
      }
      if (!searchContentRs.length) {
        return {
          backSuccess: false,
          msg: 'Content Error 8004 没有此条动态',
        };
      }
      const contentLikeRs = yield mysqlInstance.searchContentLikeInfo(
        args['contentid'],
        args['authorid'],
        args['page'],
        args['size'],
      );
      if (contentLikeRs && contentLikeRs['type'] === 'error') {
        elogger.error('Content Error 8001 ' + contentLikeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 8001',
        };
      }
      const countRs = yield mysqlInstance.searchContentLikeCount(args['contentid'], args['authorid']);
      if (countRs && countRs['type'] === 'error') {
        elogger.error('Content Error 8002 ' + countRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 8002',
        };
      }
      const contentLike = [];

      if (contentLikeRs.length) {
        for (let i = 0; i < contentLikeRs.length; i++) {
          const data = {
            avatar: AVATAR_WAILIAN + contentLikeRs[i].avatar,
            nickName: contentLikeRs[i].nickname,
            userid: `${contentLikeRs[i].userid}`,
            gender: contentLikeRs[i].gender == 1 ? '男' : '女',
            createTime: `${contentLikeRs[i].create_time}`,
          };
          contentLike.push(data);
        }
      }
      return {
        backSuccess: true,
        data: contentLike,
        count: countRs[0].count,
      };
    });
  }

  getContentComment(args) {
    const self = this;
    return co(function* () {
      const searchContentRs = yield mysqlInstance.searchContent(args['contentid'], args['authorid']);
      if (searchContentRs && searchContentRs['type'] === 'error') {
        elogger.error('Content Error 9002 ' + searchContentRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 9002',
        };
      }
      if (!searchContentRs.length) {
        if (args.uid !== 7) {
          return {
            backSuccess: false,
            msg: '没有此条动态',
          };
        }
      }

      const contentCommonRs = yield mysqlInstance.searchContentCommonInfo(args['contentid'], args['authorid']);
      if (contentCommonRs && contentCommonRs['type'] === 'error') {
        elogger.error('Content Error 9001 ' + contentCommonRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 9001',
        };
      }
      const arr = [];
      if (contentCommonRs.length) {
        for (let i = 0; i < contentCommonRs.length; i++) {
          const belongs = contentCommonRs[i]['belongs'];
          if (belongs === 0) {
            const commendata = {
              belongs: 0,
              commentid: contentCommonRs[i]['id'],
              nickName: contentCommonRs[i]['nickname'],
              userid: contentCommonRs[i]['userid'],
              to_userid: contentCommonRs[i]['to_userid'],
              avatar: AVATAR_WAILIAN + contentCommonRs[i]['avatar'],
              comment: contentCommonRs[i]['comment'],
              create_time: `${contentCommonRs[i]['create_time']}`,
              data: [],
            };
            const colorFont = yield self.getColorFont(contentCommonRs[i]['userid']);
            commendata['colorFont'] = colorFont;
            for (let j = 0; j < contentCommonRs.length; j++) {
              if (contentCommonRs[j]['belongs'] === contentCommonRs[i]['id']) {
                const data = {
                  belongs: contentCommonRs[j]['belongs'],
                  commentid: contentCommonRs[j]['id'],
                  nickName: contentCommonRs[j]['nickname'],
                  avatar: AVATAR_WAILIAN + contentCommonRs[j]['avatar'],
                  userid: contentCommonRs[j]['userid'],
                  to_userid: contentCommonRs[j]['to_userid'],
                  comment: contentCommonRs[j]['comment'],
                  create_time: `${contentCommonRs[j]['create_time']}`,
                  to_nickName: contentCommonRs[j]['to_nickname'],
                  to_avatar: AVATAR_WAILIAN + contentCommonRs[j]['to_avatar'],
                };
                const colorFont_ = yield self.getColorFont(contentCommonRs[j]['userid']);
                data['colorFont'] = colorFont_;
                commendata['data'].push(data);
              }
            }
            arr.push(commendata);
          }
        }
      }
      const page = args['page'];
      const size = args['size'];
      const start = (page - 1) * size;
      const end = page * size;
      const commonArr = arr.slice(start, end);
      return {
        backSuccess: true,
        data: commonArr,
        count: contentCommonRs.length,
      };
    });
  }

  contentSearch(content) {
    const self = this;
    return co(function* () {
      // if (content === '离开不是结局') {
      //     const contentInfo = {
      //         contentid: 1,
      //         authorid: 7,
      //         avatar: AVATAR_WAILIAN + '8822019731632795033',
      //         nickName: '这BUG我改还不行么',
      //         gender: '男',
      //         images: '',
      //         video: '',
      //         hertz: '12.72',
      //         imageType: '',
      //         content: 'XXXXXXXXXXXXXXXXXXXXX',
      //         createTime: '1639100568',
      //         type: '1',
      //         musicContent: [],
      //         linkShare: [],
      //     }
      //     const ARR = []
      //     ARR.push(contentInfo);
      //     return {
      //         backSuccess: true,
      //         data: ARR
      //     }
      // }
      const zoeArr_ = [];
      const ZoeRs = yield self.createZoeRequest(content);
      if (ZoeRs && ZoeRs['type'] === 'error') {
        elogger.error('Content Error 800311 ' + ZoeRs['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 8003',
        };
      }
      for (let i = 0; i < ZoeRs.length; i++) {
        const neighbor_id = ZoeRs[i].id;
        zoeArr_.push(neighbor_id);
      }
      const zoeArr = zoeArr_.slice(0, 20);
      const result = yield mysqlInstance.getSearchContent(zoeArr);
      const ARR = [];
      for (let i = 0; i < result.length; i++) {
        const searchContentRs = result[i];
        if (searchContentRs.type == 1) {
          let imageArrStr = '';
          if (searchContentRs.images) {
            const images = searchContentRs.images;
            const imagesArr_ = images.split(',');
            for (let i = 0; i < imagesArr_.length; i++) {
              if (i === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
              }
            }
          }

          const contentInfo = {
            contentid: searchContentRs['contentid'],
            authorid: searchContentRs.userid,
            avatar: AVATAR_WAILIAN + searchContentRs.avatar,
            nickName: searchContentRs.nickname,
            gender: searchContentRs.gender == 1 ? '男' : '女',
            images: imageArrStr,
            video: '',
            hertz: `${self.returnFloat(searchContentRs.hertz)}`,
            imageType: searchContentRs.imageType,
            content: searchContentRs.content || '',
            createTime: searchContentRs.create_time,
            type: '1',
            musicContent: [],
            linkShare: [],
          };

          // return {
          //     backSuccess: true,
          //     data: contentInfo
          // }
          ARR.push(contentInfo);
        } else if (searchContentRs.type == 2) {
          const musicContent = searchContentRs.musicContent;
          const musicContentData = JSON.parse(musicContent);
          // const shareUrl = musicContentData['shareUrl']
          // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
          // if (musicInfo && musicInfo['type']) {
          //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
          // }
          let imageArrStr = '';
          if (searchContentRs.images) {
            const images = searchContentRs.images;
            const imagesArr_ = images.split(',');
            for (let i = 0; i < imagesArr_.length; i++) {
              if (i === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
              }
            }
          }

          const contentInfo = {
            contentid: searchContentRs['contentid'],
            authorid: searchContentRs.userid,
            avatar: AVATAR_WAILIAN + searchContentRs.avatar,
            nickName: searchContentRs.nickname,
            gender: searchContentRs.gender == 1 ? '男' : '女',
            images: imageArrStr,
            video: '',
            hertz: `${self.returnFloat(searchContentRs.hertz)}`,
            imageType: searchContentRs.imageType,
            content: searchContentRs.content || '',
            createTime: searchContentRs.create_time,
            type: '2',
            musicContent: musicContentData,
            linkShare: [],
          };

          // return {
          //     backSuccess: true,
          //     data: contentInfo
          // }
          ARR.push(contentInfo);
        } else if (searchContentRs.type == 3) {
          const linkShareContent = searchContentRs.linkshare;
          const linkShareContentData = JSON.parse(linkShareContent);

          let imageArrStr = '';
          if (searchContentRs.images) {
            const images = searchContentRs.images;
            const imagesArr_ = images.split(',');
            for (let i = 0; i < imagesArr_.length; i++) {
              if (i === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
              }
            }
          }

          const contentInfo = {
            contentid: searchContentRs['contentid'],
            authorid: searchContentRs.userid,
            avatar: AVATAR_WAILIAN + searchContentRs.avatar,
            nickName: searchContentRs.nickname,
            gender: searchContentRs.gender == 1 ? '男' : '女',
            images: imageArrStr,
            video: '',
            hertz: `${self.returnFloat(searchContentRs.hertz)}`,
            imageType: searchContentRs.imageType,
            content: searchContentRs.content || '',
            createTime: searchContentRs.create_time,
            type: '3',
            musicContent: [],
            linkShare: linkShareContentData,
          };

          // return {
          //     backSuccess: true,
          //     data: contentInfo
          // }
          ARR.push(contentInfo);
        } else if (searchContentRs.type == 4) {
          let imageArrStr = '';
          if (searchContentRs.images) {
            const images = searchContentRs.images;
            const imagesArr_ = images.split(',');
            for (let i = 0; i < imagesArr_.length; i++) {
              if (i === imagesArr_.length - 1) {
                imageArrStr += `${WAILIAN}${imagesArr_[i]}`;
              } else {
                imageArrStr += `${WAILIAN}${imagesArr_[i]},`;
              }
            }
          }

          let videoArrStr = '';
          if (searchContentRs.video) {
            const videos = searchContentRs.video;
            const videosArr_ = videos.split(',');
            for (let j = 0; j < videosArr_.length; j++) {
              if (j === videosArr_.length - 1) {
                videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
              } else {
                videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
              }
            }
          }

          const contentInfo = {
            contentid: searchContentRs['contentid'],
            authorid: searchContentRs.userid,
            avatar: AVATAR_WAILIAN + searchContentRs.avatar,
            nickName: searchContentRs.nickname,
            gender: searchContentRs.gender == 1 ? '男' : '女',
            images: imageArrStr,
            video: videoArrStr,
            hertz: `${self.returnFloat(searchContentRs.hertz)}`,
            imageType: searchContentRs.imageType,
            content: searchContentRs.content || '',
            createTime: searchContentRs.create_time,
            type: '4',
            musicContent: [],
            linkShare: [],
          };
          ARR.push(contentInfo);
        }
      }
      return {
        backSuccess: true,
        data: ARR,
      };
    });
  }

  // contentSearchNew(nickname, page, size) {
  //     const self = this;
  //     return co(function* () {
  //         const ARR = []

  //         for (let i = 0; i < 20; i++) {
  //             const contentInfo = {
  //                 contentid: 1,
  //                 authorid: 7,
  //                 avatar: AVATAR_WAILIAN + '8822019731632795033',
  //                 nickName: '这BUG我改还不行么',
  //                 gender: '男',
  //                 images: '',
  //                 video: '',
  //                 hertz: '12.72',
  //                 imageType: '',
  //                 content: `XXXX${i}XX${i}XXX${i}XXXX${i}XXX${i}XX${i}${i}${i}XXX`,
  //                 createTime: 1639100568,
  //                 type: '1',
  //                 musicContent: [],
  //                 linkShare: [],
  //             }
  //             ARR.push(contentInfo);
  //         }

  //         return {
  //             backSuccess: true,
  //             data: ARR
  //         }
  //     })
  // }

  contentSearchNew(userid, nickname, page, size) {
    const self = this;
    return co(function* () {
      const zoeArr = [];
      yield mysqlInstance.addUserSearchHistory(userid, nickname);
      const ZoeRs = yield mysqlInstance.getUserByNameV2(nickname, page, size);
      for (let i = 0; i < ZoeRs.length; i++) {
        const data = {
          contentid: 1,
          authorid: ZoeRs[i]['id'],
          avatar: ZoeRs[i]['avatar'],
          nickName: ZoeRs[i]['nickname'],
          gender: ZoeRs[i]['gender'] === 1 ? '男' : '女',
          images: '',
          video: '',
          hertz: `${self.returnFloat(ZoeRs[i]['hertz'])}`,
          imageType: '',
          content: ZoeRs[i]['signature'] === '' ? '连接那些遥远的相似性。' : ZoeRs[i]['signature'],
          createTime: 1639100568,
          type: '1',
          musicContent: [],
          linkShare: [],
        };
        data['avatar'] = AVATAR_WAILIAN + data['avatar'];

        zoeArr.push(data);
      }
      return {
        backSuccess: true,
        data: zoeArr,
      };
    });
  }

  createZoeRequest(content) {
    return new Promise((resolve, reject) => {
      const options = {
        method: 'POST',
        url: URL,
        headers: {
          'cache-control': 'no-cache',
          'Content-Type': 'application/json',
        },
        body: {
          function: 'searchByContents',
          params: {
            query_row: {
              content: content,
            },
            // modes: ['semantic', 'analyze'],
            modes: ['semantic'],
            size: 20,
          },
        },
        json: true,
      };
      request(options, function (error, response, body) {
        if (error) {
          resolve({
            result: false,
          });
        }
        if (body.code === 200 && body.result.length && body.result[0].id) {
          resolve(body.result);
        } else {
          resolve({
            result: false,
          });
        }
      });
    });
  }

  getUserLikeList(userid, page, size) {
    const self = this;
    return co(function* () {
      const result = yield mysqlInstance.getUserLikeList(userid, page, size);
      if (result && result['type'] === 'error') {
        elogger.error('Content Error 90022 ' + result['msg']);
        return {
          backSuccess: false,
          msg: 'Content Error 9002',
        };
      }
      const arr = [];
      for (let i = 0; i < result.length; i++) {
        const data = {
          userid: result[i].userid,
          avatar: AVATAR_WAILIAN + result[i]['avatar'],
          nickname: result[i]['nickname'],
          create_time: result[i]['update_time'] ? `${result[i]['update_time']}` : `${result[i]['create_time']}`,
          content: result[i].content,
          musicContentData: null,
          image: null,
          type: result[i].type,
          contentid: result[i].contentid,
          status: result[i].status,
        };
        const colorFont = yield self.getColorFont(result[i].userid);
        data['colorFont'] = colorFont;
        if (!result[i].status) {
          if (result[i].type == 1) {
            let imageArrStr = '';
            if (result[i].images) {
              const images = result[i].images;
              const imagesArr_ = images.split(',');
              imageArrStr += `${WAILIAN}${imagesArr_[0]}`;
            }
            data['image'] = imageArrStr;
          } else if (result[i].type == 2) {
            const musicContent = result[i].musicContent;
            const musicContentData = JSON.parse(musicContent);
            // const shareUrl = musicContentData['shareUrl']
            // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
            // if (musicInfo && musicInfo['type']) {
            //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
            // }

            let imageArrStr = '';
            if (result[i].images) {
              const images = result[i].images;
              const imagesArr_ = images.split(',');
              imageArrStr += `${WAILIAN}${imagesArr_[0]}`;
            }
            data['image'] = imageArrStr;
            data['musicContentData'] = musicContentData;
          } else if (result[i].type == 3) {
            const musicContent = result[i].link;
            const musicContentData = JSON.parse(musicContent);
            // const shareUrl = musicContentData['shareUrl']
            // const musicInfo = yield SFMInstance.getMusicInfo(shareUrl)
            // if (musicInfo && musicInfo['type']) {
            //     musicContentData['musicMp3Url'] = musicInfo['data'].playUrl;
            // }

            let imageArrStr = '';
            if (result[i].images) {
              const images = result[i].images;
              const imagesArr_ = images.split(',');
              imageArrStr += `${WAILIAN}${imagesArr_[0]}`;
            }
            data['image'] = imageArrStr;
            data['musicContentData'] = musicContentData;
          }
        } else {
          data['content'] = '「该动态已删除」';
        }

        arr.push(data);
      }
      const likeCountRs = yield mysqlInstance.getUserLikeCount(userid);
      return {
        backSuccess: true,
        data: arr,
        count: likeCountRs.length ? `${likeCountRs[0].count}` : '0',
      };
    });
  }

  postContentHide(userid, contentid, immediately) {
    return co(function* () {
      const content = yield mysqlInstance.searchContent(contentid, userid);
      if (content.length === 0) {
        return {
          backSuccess: false,
          msg: '动态不存在',
        };
      }
      const status = content[0].status;

      if (immediately && status === 3) {
        yield mysqlInstance.postContentHideV2(userid, contentid, 0);
        if (content[0].atUserIds) {
          const atUserIds = content[0].atUserIds.split(',').map((id) => parseInt(id));
          if (atUserIds.length > 0) {
            // 获取发送者信息（只需查询一次）
            const userInfo = yield mysqlInstance.getUserInfoByUserid(userid);

            // 准备通用数据
            const content_ = content[0].text ? content[0].text.slice(0, 50) : '';
            let image = '';
            if (content[0].images) {
              const imagesArr = content[0].images.split(',');
              image = imagesArr[0];
            }

            // 处理每个被@的用户
            for (const toUserid of atUserIds) {
              // 跳过自己@自己的情况
              if (toUserid === userid) {
                continue;
              }
              const notificationData = {
                userid: toUserid,
                text: ' 提及了你',
                avatar: userInfo[0]['avatar'],
                nickname: userInfo[0]['nickname'],
                type: 7,
                contentid: contentid,
                content: content_,
                images: image,
              };
              yield mysqlInstance.insertIntoNotification(notificationData);
            }
          }
        }
      } else {
        if (status === 2) {
          if (content[0].timing) {
            return {
              backSuccess: false,
              msg: '定时动态无法隐藏',
            };
          } else {
            yield mysqlInstance.postContentHide(userid, contentid, 0);
          }
        } else {
          yield mysqlInstance.postContentHide(userid, contentid, 2);
        }
      }

      return {
        backSuccess: true,
      };
    });
  }

  postContentTop(userid, contentid) {
    return co(function* () {
      const content = yield mysqlInstance.searchContent(contentid, userid);
      if (content.length === 0) {
        return {
          backSuccess: false,
          msg: '动态不存在',
        };
      }
      let top = content[0].top;

      if (top === 0) {
        top = 1;
      } else {
        top = 0;
      }

      yield mysqlInstance.postContentTop(userid, contentid, top);

      return {
        backSuccess: true,
      };
    });
  }
}

function dateFormat(fmt, date) {
  let ret;
  const opt = {
    'Y+': date.getFullYear().toString(), // 年
    'm+': (date.getMonth() + 1).toString(), // 月
    'd+': date.getDate().toString(), // 日
    'H+': date.getHours().toString(), // 时
    'M+': date.getMinutes().toString(), // 分
    'S+': date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp('(' + k + ')').exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, '0'));
    }
  }
  return fmt;
}

module.exports.contentInstance = new ContentController();
