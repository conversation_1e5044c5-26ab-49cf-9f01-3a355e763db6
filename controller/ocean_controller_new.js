'use strict';
const co = require('co');
const BaseController = require('./BaseController');
const mysqlInstance = require('../models/mysql').mysqlInstance;
const redisInstance = require('../models/redis').redisInstance;
const request = require('request');
const URL = 'http://172.16.241.103:10032/hertz/rpc';
const { DefaultConfig } = require('../config/default');
const WAILIAN = DefaultConfig.wailian.AVATAR_DOMAIN;
const imgWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const IMAEWAILIAN = DefaultConfig.wailian.IMAGE_DOMAIN;
const VIDEOWAILIAN = DefaultConfig.wailian.VIDEO_DOMAIN;
const TAG = require('../config/tagConfig').tag;
const Attribute = require('../config/tagConfig').attribute;

class OceanNewController extends BaseController {
  constructor(props) {
    super(props);
  }
  getRecommenNew(userid) {
    const self = this;
    return co(function* () {
      // 判断新老用户
      const TODAYTIME = new Date(new Date().toLocaleDateString()).getTime() / 1000;

      const user = yield mysqlInstance.getUserOldNew(userid);
      let recommenRs = yield recommenFun(user[0].old_new, userid, TODAYTIME);
      if (recommenRs['backSuccess']) {
        const recommenArr = [];
        recommenRs = recommenRs['data'];
        for (let i = 0; i < recommenRs.length; i++) {
          if (i === 0) {
            recommenArr.push(212693);
          } else {
            recommenArr.push(recommenRs[i]);
          }

          // recommenArr.push(recommenRs[i]);
        }
        const recommen = yield formatRecommen(recommenArr, userid);

        let result = {
          code: 0,
          data: [
            {
              data: recommen,
            },
          ],
          debugInfo: {},
          msg: '获取成功',
          number: recommen.length,
          page: 1,
        };
        return {
          backSuccess: true,
          data: JSON.stringify(result),
        };
      } else {
        return {
          backSuccess: false,
          msg: recommenRs['msg'],
        };
      }
    });
  }

  getRecommenNew2(hertz, page, userid) {
    const self = this;
    return co(function* () {
      if (!hertz) {
        const user = yield mysqlInstance.getUserInfoByUserid(userid);
        hertz = user[0].hertz;
      }
      let recommenRs = yield recommenFunNew(parseInt(hertz), page, 20);
      if (recommenRs['backSuccess']) {
        const recommenArr = [];
        recommenRs = recommenRs['data'];
        for (let i = 0; i < recommenRs.length; i++) {
          recommenArr.push(recommenRs[i]);
        }
        const recommen = yield formatRecommenNew(recommenArr, userid);

        let result = {
          code: 0,
          data: [
            {
              data: recommen,
            },
          ],
          debugInfo: {},
          msg: '获取成功',
          number: recommen.length,
          page: 1,
        };
        return {
          backSuccess: true,
          data: JSON.stringify(result),
        };
      } else {
        return {
          backSuccess: false,
          msg: recommenRs['msg'],
        };
      }
    });
  }

  getRecommenNew3(hertz, userid, lastId, lat, lon, location) {
    const self = this;
    return co(function* () {
      if (lat && lon && location) {
        yield self.updateUserLocation(userid, location);
      }
      if (!hertz) {
        const user = yield mysqlInstance.getUserInfoByUserid(userid);
        hertz = user[0].hertz;
      }
      let recommenRs = yield recommenFunNew2(parseInt(hertz), lastId);
      if (recommenRs['backSuccess']) {
        const recommenArr = [];
        recommenRs = recommenRs['data'];
        for (let i = 0; i < recommenRs.length; i++) {
          recommenArr.push(recommenRs[i]);
        }
        const recommen = yield formatRecommenNew2(recommenArr, userid);

        let result = {
          code: 0,
          data: [
            {
              data: recommen,
            },
          ],
          debugInfo: {},
          msg: '获取成功',
          count: recommen.length,
          lastId: recommen.length ? recommen[recommen.length - 1].affair.aid : 0,
        };
        return {
          backSuccess: true,
          data: JSON.stringify(result),
        };
      } else {
        return {
          backSuccess: false,
          msg: recommenRs['msg'],
        };
      }
    });
  }

  updateUserLocation(userid, location) {
    return co(function* () {
      const locationArr = ['太平洋', '大西洋', '北冰洋', '印度洋', '南冰洋'];
      const index = Math.floor(Math.random() * locationArr.length);
      const location_ = locationArr[index];
      const data = {
        country: location !== 0 ? location : location_,
        province: location !== 0 ? location : location_,
        city: location !== 0 ? location : location_,
        userid: userid,
      };
      const userInfo = yield redisInstance.hashGet('HASH:USER:INFO:' + userid);
      if (userInfo && userInfo.locationStatus === '0') {
        yield mysqlInstance.updateUserLocation(data);
        const redisUserInfo = yield redisInstance.hashAdd('HASH:USER:INFO:' + userid, [
          'city',
          location || '',
          'province',
          location || '',
          'country',
          location || '',
        ]);
        if (redisUserInfo && redisUserInfo['type'] === 'error') {
          elogger.error(`HASH:USER:INFO:>>${redisUserInfo['msg']},userid>>${userid}`);
        }
      }
      return;
    });
  }
}

async function recommenFunNew(hertz, page, size) {
  let EightyPercentArr = [];
  const suijiRs = await mysqlInstance.getContentByHertz(hertz, page, size);
  for (let i = 0; i < suijiRs.length; i++) {
    const contentid = parseInt(suijiRs[i].id);
    EightyPercentArr.push(contentid);
  }
  return {
    backSuccess: true,
    data: EightyPercentArr,
  };
}

async function recommenFunNew2(hertz, lastId) {
  let EightyPercentArr = [];
  const suijiRs = await mysqlInstance.getContentByHertz2(hertz, lastId);
  for (let i = 0; i < suijiRs.length; i++) {
    const contentid = parseInt(suijiRs[i].id);
    EightyPercentArr.push(contentid);
  }
  return {
    backSuccess: true,
    data: EightyPercentArr,
  };
}

async function recommenFun(type, userid, TODAYTIME) {
  const self = this;
  // 获取关注拉黑
  let paichuUidArr = [];
  const black = await mysqlInstance.getUserBlackFun2(userid);
  for (let i = 0; i < black.length; i++) {
    const black_userid = parseInt(black[i].black_userid);
    paichuUidArr.push(black_userid);
  }

  const attention = await mysqlInstance.searcUserAttentionUserid(userid);
  for (let i = 0; i < attention.length; i++) {
    const userid = parseInt(attention[i].userid2);
    paichuUidArr.push(userid);
  }
  // 获取黑名单
  const report = await mysqlInstance.getAllReport();
  for (let i = 0; i < report.length; i++) {
    const userid = parseInt(report[i].reported_userid);
    const count = parseInt(report[i].count);
    if (count > 5) {
      paichuUidArr.push(userid);
    }
  }

  paichuUidArr.push(userid);
  paichuUidArr = arrToSet(paichuUidArr);
  // 获取最近一个月以推荐过的id
  let paichuCidArr = [];
  const recommendedRs = await mysqlInstance.getRecommendedIdV2(userid, TODAYTIME - 60 * 60 * 24 * 30);
  for (let i = 0; i < recommendedRs.length; i++) {
    let recommenArr = recommendedRs[i].recommenid;
    recommenArr = recommenArr.split(',');
    for (let j = 0; j < recommenArr.length; j++) {
      const recommenid = parseInt(recommenArr[j]);
      paichuCidArr.push(recommenid);
    }
  }
  paichuCidArr = arrToSet(paichuCidArr);
  // type: 1:old 2:new
  if (type === 1) {
    /**
     *【 80 % 】“距离”近用户的最近内容
     * “距离”近的定义：将用户最近发布的10条内容（不满10条，就有几条搜几条），一条条进行搜索，搜索结果按相似分数排完，综合取前10*50条，筛选最近15天内发布的加入推荐内容。
     */
    const userContent = await mysqlInstance.getUserTenContent(userid);
    const userContentArr = [];
    for (let i = 0; i < userContent.length; i++) {
      const content = userContent[i].content;
      userContentArr.push(content);
    }
    let zoeArr = [];
    for (let i = 0; i < userContentArr.length; i++) {
      const content = userContentArr[i];
      const ZoeRs = await createZoeRequest(content);
      if (ZoeRs && ZoeRs['type'] === 'error') {
        elogger.error('Content Error 800311 ' + ZoeRs['msg']);
        return {
          backSuccess: false,
          msg: 'New Recommen Error 10121',
        };
      }
      for (let i = 0; i < ZoeRs.length; i++) {
        if (i >= 50) {
          break;
        }
        const neighbor_id = parseInt(ZoeRs[i].id);
        zoeArr.push(neighbor_id);
      }
    }
    zoeArr = arrToSet(zoeArr);
    zoeArr = arrayMinus(zoeArr, paichuCidArr);
    const EightyPercentRecommenRs = await mysqlInstance.getRecommenContent(zoeArr, paichuUidArr);
    let EightyPercentArr = [];
    for (let i = 0; i < EightyPercentRecommenRs.length; i++) {
      const contentid = EightyPercentRecommenRs[i].id;
      EightyPercentArr.push(contentid);
    }
    EightyPercentArr = EightyPercentArr.slice(0, 24);
    /**
     *【 20 % 】随机的最近内容
     * 随机
     */
    const newContent = await mysqlInstance.getTwoNewContent(paichuUidArr);
    for (let i = 0; i < newContent.length; i++) {
      const contentid = newContent[i].id;
      EightyPercentArr.push(contentid);
    }
    EightyPercentArr = arrayMinus(EightyPercentArr, paichuCidArr);
    EightyPercentArr = EightyPercentArr.slice(0, 30);
    if (EightyPercentArr.length < 30) {
      const suijiRs = await mysqlInstance.getSuiJiContent(paichuUidArr, paichuCidArr);
      for (let i = 0; i < suijiRs.length; i++) {
        const contentid = parseInt(suijiRs[i].id);
        EightyPercentArr.push(contentid);
      }
      EightyPercentArr = EightyPercentArr.slice(0, 30);
    }
    EightyPercentArr = arrToSet(EightyPercentArr);
    // for (let i = 0; i < EightyPercentArr.length; i++) {
    await mysqlInstance.addRecommenV2(userid, EightyPercentArr.toString());
    // }
    return {
      backSuccess: true,
      data: EightyPercentArr,
    };
  } else if (type === 0) {
    /**
     *【 50% 】 最近的好内容
     * 最近的定义【以下都是】：发布时间在15天之内
     * 好内容的定义：点赞+评论>10
     */
    const goodContentRs = await mysqlInstance.getGoodContent(TODAYTIME - 60 * 60 * 24 * 15);
    let goodContentArr = [];
    for (let i = 0; i < goodContentRs.length; i++) {
      goodContentArr.push(goodContentRs[i].id);
    }
    goodContentArr = arrayMinus(goodContentArr, paichuCidArr);
    goodContentArr = goodContentArr.slice(0, 16);
    /**
     *【 25% 】 最近的感兴趣的人的内容
     * 可能感兴趣的人定义：频率区间为同一区间 或 标签有3个以上符合用户选择想认识的人的标签：
     */
    const userInfo = await mysqlInstance.getUserInfo(userid);
    const userLikeTag = userInfo[0].likeTagArr;
    const attribute = userInfo[0].attribute;
    const minHertz = Attribute[attribute]['interval'][0];
    const maxHertz = Attribute[attribute]['interval'][1];
    let userArr = [];
    const likePeopleRs = await mysqlInstance.getHertzLikePeople(minHertz, maxHertz);
    for (let i = 0; i < likePeopleRs.length; i++) {
      const userid = likePeopleRs[i].id;
      userArr.push(userid);
    }
    // const likeTagRs = await mysqlInstance.getTagLikePeople(userLikeTag)
    userArr = arrayMinus(userArr, paichuUidArr);
    userArr = arrToSet(userArr);
    const likeContentRs = await mysqlInstance.getLikeContent(userArr, paichuUidArr);
    for (let i = 0; i < likeContentRs.length; i++) {
      goodContentArr.push(likeContentRs[i].id);
    }
    goodContentArr = arrToSet(goodContentArr);
    goodContentArr = arrayMinus(goodContentArr, paichuCidArr);
    goodContentArr = goodContentArr.slice(0, 23);
    /**
     *【 25% 】“距离”近用户的最近内容
     * “距离”近的定义：将用户最近发布的10条内容（不满10条，就有几条搜几条），一条条进行搜索，搜索结果按相似分数排完，综合取前10*50条，筛选最近15天内发布的加入推荐内容。
     */
    const userContent = await mysqlInstance.getUserTenContent(userid);
    const userContentArr = [];
    for (let i = 0; i < userContent.length; i++) {
      const content = userContent[i].content;
      userContentArr.push(content);
    }
    let zoeArr = [];
    for (let i = 0; i < userContentArr.length; i++) {
      const content = userContentArr[i];
      const ZoeRs = await createZoeRequest(content);
      if (ZoeRs && ZoeRs['type'] === 'error') {
        elogger.error('Content Error 800311 ' + ZoeRs['msg']);
        return {
          backSuccess: false,
          msg: 'New Recommen Error 10121',
        };
      }
      for (let i = 0; i < ZoeRs.length; i++) {
        if (i >= 50) {
          break;
        }
        const neighbor_id = parseInt(ZoeRs[i].id);
        zoeArr.push(neighbor_id);
      }
    }
    zoeArr = arrToSet(zoeArr);
    zoeArr = arrayMinus(zoeArr, paichuCidArr);
    const EightyPercentRecommenRs = await mysqlInstance.getRecommenContent(zoeArr, paichuUidArr);
    for (let i = 0; i < EightyPercentRecommenRs.length; i++) {
      const contentid = EightyPercentRecommenRs[i].id;
      goodContentArr.push(contentid);
    }
    goodContentArr = arrToSet(goodContentArr);
    goodContentArr = goodContentArr.slice(0, 30);
    if (goodContentArr.length < 30) {
      const suijiRs = await mysqlInstance.getSuiJiContent(paichuUidArr, paichuCidArr);
      for (let i = 0; i < suijiRs.length; i++) {
        const contentid = parseInt(suijiRs[i].id);
        goodContentArr.push(contentid);
      }
      goodContentArr = goodContentArr.slice(0, 30);
    }
    goodContentArr = arrToSet(goodContentArr);
    // for (let i = 0; i < goodContentArr.length; i++) {
    await mysqlInstance.addRecommenV2(userid, goodContentArr.toString());
    // }
    return {
      backSuccess: true,
      data: goodContentArr,
    };
  }
}

function createZoeRequest(content) {
  return new Promise((resolve, reject) => {
    const options = {
      method: 'POST',
      url: URL,
      headers: {
        'cache-control': 'no-cache',
        'Content-Type': 'application/json',
      },
      body: {
        function: 'searchByContents',
        params: {
          query_row: {
            content: content,
          },
          modes: ['semantic'],
          size: 20,
        },
      },
      json: true,
    };
    request(options, function (error, response, body) {
      if (error) {
        resolve({
          result: false,
        });
      }
      if (body['code'] === 200 && body['result'].length && body['result'][0].id) {
        resolve(body.result);
      } else {
        resolve({
          result: false,
        });
      }
    });
  });
}

async function formatRecommen(recommenArr, userid) {
  const self = this;
  let arr = [];
  for (let i = 0; i < recommenArr.length; i++) {
    const contentid = recommenArr[i];
    const contentRs = await mysqlInstance.getContentByIdV2(contentid);
    const likeRs = await mysqlInstance.searchContentLike({ userid, contentid });
    const affair = {
      aid: `${contentRs[0].aid}`,
      commentNum: `${contentRs[0].commentNum}`,
      commentScore: 0.3333333333333333,
      content: contentRs[0].content,
      create_time: `${contentRs[0].create_time}`,
      del_time: 'None',
      exposeNum: '0',
      fusionScore: 1.2499169444444445,
      imageType: contentRs[0].imageType,
      images: '',
      video: '',
      likeNum: `${contentRs[0].likeNum}`,
      likeRs: likeRs.length && likeRs[0].status === 0 ? '1' : '0',
      likeScore: 0.25,
      location: contentRs[0].location,
      musicContent: null,
      recallReason: 'EXPLORER_USER',
      status: `${contentRs[0].status}`,
      tagType: `${contentRs[0].tagType}`,
      timeScore: 0.08305555555555555,
      type: `${contentRs[0].type}`,
      userid: `${contentRs[0].userid}`,
      linkShare: null,
    };

    let imageArrStr = '';
    if (contentRs[0].images) {
      const images = contentRs[0].images;
      const imagesArr_ = images.split(',');
      for (let i = 0; i < imagesArr_.length; i++) {
        if (i === imagesArr_.length - 1) {
          imageArrStr += `${IMAEWAILIAN}${imagesArr_[i]}`;
        } else {
          imageArrStr += `${IMAEWAILIAN}${imagesArr_[i]},`;
        }
      }
    }
    let videoArrStr = '';
    if (contentRs[0].video) {
      const videos = contentRs[0].video;
      const videosArr_ = videos.split(',');
      for (let j = 0; j < videosArr_.length; j++) {
        if (j === videosArr_.length - 1) {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
        } else {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
        }
      }
    }

    const musicContent = contentRs[0].musicContent;
    const musicContentData = JSON.parse(musicContent);
    affair['images'] = imageArrStr;
    affair['video'] = videoArrStr;
    affair['musicContent'] = musicContentData;

    const linkShareContent = contentRs[0].linkshare;
    const linkShareContentData = linkShareContent ? JSON.parse(linkShareContent) : null;
    affair['linkShare'] = linkShareContentData;

    const uid = contentRs[0].uid;
    const colorFont = await getColorFont(uid);
    const data = {
      affair: affair,
      position: i,
      user: {
        avatar: `${WAILIAN}${contentRs[0].avatar}`,
        city: contentRs[0].city,
        gender: `${contentRs[0].gender}`,
        hertz: `${returnFloat(contentRs[0]['hertz'])}`,
        nickname: contentRs[0].nickname,
        uid: `${contentRs[0].uid}`,
        colorFont: colorFont,
      },
    };
    arr.push(data);
  }
  return arr;
}

async function formatRecommenNew(recommenArr, userid) {
  const self = this;
  let arr = [];
  const contentRss = await mysqlInstance.getContentByIdV3(recommenArr);

  for (let i = 0; i < contentRss.length; i++) {
    const contentRs = contentRss[i];
    const contentid = contentRs.aid;
    const likeRs = await mysqlInstance.searchContentLike({ userid, contentid });
    const affair = {
      aid: `${contentRs.aid}`,
      commentNum: `${contentRs.commentNum}`,
      commentScore: 0.3333333333333333,
      content: contentRs.content,
      create_time: `${contentRs.create_time}`,
      del_time: 'None',
      exposeNum: '0',
      fusionScore: 1.2499169444444445,
      imageType: contentRs.imageType,
      images: '',
      video: '',
      likeNum: `${contentRs.likeNum}`,
      likeRs: likeRs.length && likeRs[0].status === 0 ? '1' : '0',
      likeScore: 0.25,
      location: contentRs.location,
      musicContent: null,
      recallReason: 'EXPLORER_USER',
      status: `${contentRs.status}`,
      sponsor: `${contentRs.sponsor}`,
      tagType: `${contentRs.tagType}`,
      timeScore: 0.08305555555555555,
      type: `${contentRs.type}`,
      userid: `${contentRs.userid}`,
      linkShare: null,
    };

    let imageArrStr = '';
    if (contentRs.images) {
      const images = contentRs.images;
      const imagesArr_ = images.split(',');
      for (let i = 0; i < imagesArr_.length; i++) {
        if (i === imagesArr_.length - 1) {
          imageArrStr += `${IMAEWAILIAN}${imagesArr_[i]}`;
        } else {
          imageArrStr += `${IMAEWAILIAN}${imagesArr_[i]},`;
        }
      }
    }
    let videoArrStr = '';
    if (contentRs.video) {
      const videos = contentRs.video;
      const videosArr_ = videos.split(',');
      for (let j = 0; j < videosArr_.length; j++) {
        if (j === videosArr_.length - 1) {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
        } else {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
        }
      }
    }

    const musicContent = contentRs.musicContent;
    const musicContentData = JSON.parse(musicContent);
    affair['images'] = imageArrStr;
    affair['video'] = videoArrStr;
    affair['musicContent'] = musicContentData;

    const linkShareContent = contentRs.linkshare;
    const linkShareContentData = linkShareContent ? JSON.parse(linkShareContent) : null;
    affair['linkShare'] = linkShareContentData;

    const uid = contentRs.uid;
    const colorFont = await getColorFont(uid);
    const data = {
      affair: affair,
      position: i,
      user: {
        avatar: `${WAILIAN}${contentRs.avatar}`,
        city: contentRs.city,
        gender: `${contentRs.gender}`,
        hertz: `${returnFloat(contentRs['hertz'])}`,
        nickname: contentRs.nickname,
        uid: `${contentRs.uid}`,
        colorFont: colorFont,
        signature: contentRs.signature === '' ? '连接那些遥远的相似性。' : contentRs.signature,
      },
    };
    arr.push(data);
  }
  return arr;
}

async function formatRecommenNew2(recommenArr, userid) {
  const self = this;
  let arr = [];
  const contentRss = await mysqlInstance.getContentByIdV3(recommenArr);

  const contentIds = contentRss.map((item) => item.aid);

  const allLikes = await mysqlInstance.batchSearchContentLike({ userid, contentIds });

  const likeStatusMap = {};
  if (allLikes && Array.isArray(allLikes)) {
    allLikes.forEach((like) => {
      likeStatusMap[like.contentid] = like.status;
    });
  }

  const commentRs = await mysqlInstance.getCommentByIds(userid, contentIds);
  const commentIdArr = Array.from(commentRs).map((item) => item.contentid);
  const commentedSet = new Set(commentIdArr);

  for (let i = 0; i < contentRss.length; i++) {
    const contentRs = contentRss[i];
    const contentid = contentRs.aid;

    const isComment = commentedSet.has(contentid) ? '1' : '0';

    const isLiked = likeStatusMap[contentid] === 0 ? '1' : '0';

    const affair = {
      aid: `${contentRs.aid}`,
      commentNum: `${contentRs.commentNum}`,
      commentScore: 0.3333333333333333,
      commentRs: isComment,
      content: contentRs.content,
      create_time: `${contentRs.create_time}`,
      del_time: 'None',
      exposeNum: '0',
      fusionScore: 1.2499169444444445,
      imageType: contentRs.imageType,
      images: '',
      video: '',
      likeNum: `${contentRs.likeNum}`,
      likeRs: isLiked,
      likeScore: 0.25,
      location: contentRs.location,
      musicContent: null,
      recallReason: 'EXPLORER_USER',
      status: `${contentRs.status}`,
      sponsor: `${contentRs.sponsor}`,
      tagType: `${contentRs.tagType}`,
      timeScore: 0.08305555555555555,
      type: `${contentRs.type}`,
      userid: `${contentRs.userid}`,
      linkShare: null,
    };

    let imageArrStr = '';
    if (contentRs.images) {
      const images = contentRs.images;
      const imagesArr_ = images.split(',');
      for (let i = 0; i < imagesArr_.length; i++) {
        if (i === imagesArr_.length - 1) {
          imageArrStr += `${IMAEWAILIAN}${imagesArr_[i]}`;
        } else {
          imageArrStr += `${IMAEWAILIAN}${imagesArr_[i]},`;
        }
      }
    }

    let videoArrStr = '';
    if (contentRs.video) {
      const videos = contentRs.video;
      const videosArr_ = videos.split(',');
      for (let j = 0; j < videosArr_.length; j++) {
        if (j === videosArr_.length - 1) {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]}`;
        } else {
          videoArrStr += `${VIDEOWAILIAN}${videosArr_[j]},`;
        }
      }
    }

    const musicContent = contentRs.musicContent;
    const musicContentData = JSON.parse(musicContent);
    affair['images'] = imageArrStr;
    affair['video'] = videoArrStr;
    affair['musicContent'] = musicContentData;

    const linkShareContent = contentRs.linkshare;
    const linkShareContentData = linkShareContent ? JSON.parse(linkShareContent) : null;
    affair['linkShare'] = linkShareContentData;

    const uid = contentRs.uid;
    const colorFont = await getColorFont(uid);
    const data = {
      affair: affair,
      position: i,
      user: {
        avatar: `${WAILIAN}${contentRs.avatar}`,
        city: contentRs.city,
        gender: `${contentRs.gender}`,
        hertz: `${returnFloat(contentRs['hertz'])}`,
        nickname: contentRs.nickname,
        uid: `${contentRs.uid}`,
        colorFont: colorFont,
        signature: contentRs.signature === '' ? '连接那些遥远的相似性。' : contentRs.signature,
      },
    };
    arr.push(data);
  }
  return arr;
}

async function getColorFont(userid) {
  userid = parseInt(userid);
  // const rs = await mysqlInstance.getColorFont(userid);
  const body = {
    type: 0,
    expire_time: 0,
    status: 0,
  };

  // const now = parseInt(new Date() / 1000);
  // if (rs.length) {
  //   body['type'] = rs[0].type;
  //   body['expire_time'] = rs[0].expire_time;
  //   body['status'] = rs[0].expire_time > now ? 1 : 0;
  // }
  return body;
}

function arrToSet(arr) {
  let newSet = new Set();
  for (let i = 0; i < arr.length; i++) {
    newSet.add(arr[i]);
  }
  const newArr = Array.from(newSet);
  return newArr;
}

function arrayMinus(arr1, arr2) {
  var result = [];
  arr1.forEach(function (x) {
    if (arr2.indexOf(x) === -1) {
      result.push(x);
    } else {
      return;
    }
  });
  return result;
}

function returnFloat(value) {
  const s = value.toString().split('.');
  if (s.length == 1) {
    value = value.toString() + '.00';
    return value;
  }
  if (s.length > 1) {
    if (s[1].length < 2) {
      value = value.toString() + '0';
    }
    return value;
  }
}

module.exports.oceanNewInstance = new OceanNewController();
